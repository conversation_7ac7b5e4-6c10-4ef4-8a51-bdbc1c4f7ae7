<template>
  <j-crud
      :queryMethod="queryEcsInvoRcd"
      :queryForm="queryForm"
      :tabs="tabs"
      default-check-tab="1"
      :show-operation-button="false"
      v-model:checked-row-keys="checkRowKeys"
      ref="crudRef"
  >
    <template #extendFormItems>
      <n-form-item label="发票名称">
        <n-input v-model:value="queryForm.attName" placeholder="请输入发票名称" clearable/>
      </n-form-item>
      <n-form-item label="上传时间">
        <n-date-picker
            v-model:formatted-value="queryForm.updDate"
            value-format="yyyy-MM-dd"
            type="date"
            placeholder="请选择上传时间"
            clearable
        />
      </n-form-item>
      <n-form-item label="发票类型" >
        <n-select
            v-model:value="queryForm.invoType"
            :options="invoTypeOptions"
            placeholder="请选择发票类型"
            clearable
        />
      </n-form-item>
      <n-form-item label="归属人" >
        <j-bus-emp-search
            v-model:value="queryForm.createUser"
            placeholder="请选择归属人"
            clearable
        />
      </n-form-item>
    </template>
    <template #extendButtons>
      <n-button type="success" @click="uploadInvos">上传发票</n-button>
      <n-badge :value="props.attMap.size" :max="99" :offset="[-8, 2]" v-if="curType === '8' && props.attMap.size">
        <n-button type="success" @click="choiceSppplierInvos">选择当前供应商发票</n-button>
      </n-badge>
      <n-button type="info" @click="invoAmend">发票人工审核</n-button>
      <n-popconfirm @positive-click="invoDel" :positive-button-props="{ type: 'error', secondary: true }">
        <template #trigger>
          <n-button type="error">删除</n-button>
        </template>
        是否确认删除
      </n-popconfirm>
    </template>
    <template #extendRightHeader>
      <n-alert type="warning" :show-icon="false"> 当前已选择 {{ checkRowKeys.length }} 张发票</n-alert>
    </template>
    <template #content>
      <!-- 弹窗：附件 -->
      <j-upload
          ref="uploadRef"
          v-model:show="showUpload"
          :show-button="false"
          :exists-file-to-upload="false"
          accept-prop=".pdf,.png,.jpg,.jpeg,.ofd"
          :is-compress="true"
          :camera-upload="true"
          :maxAmount="5"
          @afterUpload="afterUpload"
      />

      <!-- 部分推送数据自带发票文件（如物资报销） -->
      <j-modal v-model:show="showSppplierInvos" title="选择当前供应商发票" @confirm="downloadInvosFromMmisInvoStock">
        <j-n-data-table
            :data="attMapData"
            :row-key="row => row.att"
            :columns="supplierInvosColumns"
            @update:checked-row-keys="handleSupplierInvoCheck"
        ></j-n-data-table>
      </j-modal>

      <!-- 附件预览 -->
      <j-preview v-model:show="showInvoPreview" :oss-path="invoPreviewPath" bucket="ecs" style="z-index: 2100"/>

      <!-- 物资发票库的预览 -->
      <j-preview
          v-model:show="showPreview1"
          :oss-path="previewOssPath"
          :oss-path-name="previewOssPathName"
          bucket="mmis"
      />
      <!-- 发票识别 -->
      <j-bus-invoice-identify ref="invoiceIdentifyRef" @complete="ocrComplete" @submit="invoConfirm"/>

      <n-modal
          v-model:show="errMsgShow"
          title="错误信息"
          class="custom-card"
          preset="card"
          :style="{ width: '600px' }"
          size="huge"
          :bordered="false"
      >
        <template #default>
          <n-alert :show-icon="false" type="warning">
            <n-scrollbar style="max-height: 300px">
              <pre
                  v-for="(item, index) in errMsg"
                  :key="index"
                  style="white-space: pre-wrap; word-wrap: break-word; width: 100%"
              >{{ index + 1 }}:{{ item }}</pre
              >
            </n-scrollbar>
          </n-alert>
        </template>
      </n-modal>
    </template>
  </j-crud>
</template>

<script setup lang="ts">
import {h, nextTick, VNode, onMounted, reactive, ref} from 'vue'
import {useSysStore, useUserStore} from '@/store'
import {deleteEcsInvoRcd, manualAmendInvo, queryEcsInvoRcd} from '@/api/ecs/reimMgt/invoRcd.ts'
import {defineComponent} from 'vue'
import {CRUDColumnInterface, JTab} from '@/types/comps/crud.ts'
import {NTag, NPopover} from 'naive-ui'
import JPGlobal from '@jutil'
import {ocrIdentify} from '@/api/ecs/reimMgt/reimDetail.ts'
import {ElLoading} from 'element-plus'
import {ContainerValueType} from '@/types/enums/enums.ts'
import {mmisInvosFileDownload} from '@/api/mmis/matReceipt/AsetStorageWeb.ts'
import {download} from '@/api/common/common.ts'
import {useRouter} from "vue-router";

const emits = defineEmits(['rtInvos', 'closeInvo', 'openInvo'])

const props = defineProps({
  //发票类型  1：hrp 2；供应商 3：卫材
  invoFrom: {
    type: String,
    default: '1',
  },
  queryWithUser: {
    type: Boolean,
    default: true,
  },
  curType: {
    type: String,
    default: '',
  },
  //部分数据会推送自己的发票附件
  attMap: {
    type: Map,
    default: () => new Map(),
  },
})



const userStore = useUserStore()
const sysStore = useSysStore()
const router = useRouter()

//ref
const crudRef = ref()
const invoiceIdentifyRef = ref()

//按钮权限
let isInvoAdmin = ref(false)
//变量
let showInvoPane = ref(false) //
let showInvoPreview = ref(false)
let showUpload = ref(false)
let invoPreviewPath = ref()
let checkRowKeys = ref([]) //选中的行
let errMsg = ref([]) //错误信息
let errMsgShow = ref(false) //错误信息模态框

let queryForm = ref({
  createUser: props.queryWithUser ? userStore.getUserInfo.username : '',
  state: '',
  invoFrom: props.invoFrom,
  attName: '',
  updDate: null,
  invoType: '',
})
const invoStates = [
  {
    label: '可报销',
    value: '1',
  },
  {
    label: '已报销',
    value: '2',
  },
  {
    label: '重复发票',
    value: '3',
  },
  {
    label: '发票审核中',
    value: '4',
  },
  {
    label: '发票信息不规范',
    value: '5',
  },
]
const columns = ref<Array<CRUDColumnInterface>>([
  {
    type: 'selection',
  },
  {
    title: '文件名称',
    key: 'attName',
    width: 100,
  },
  {
    title: '发票代码',
    key: 'invoCode',
    width: 100,
  },
  {
    title: '发票号码',
    key: 'invoNum',
    width: 100,
  },
  {
    title: '开票日期',
    key: 'invoDate',
    width: 100,
  },
  {
    title: '发票金额',
    key: 'allValoremTax',
    width: 100,
  },
  {
    title: '发票状态',
    key: 'state',
    width: 100,
    render: (row: any): VNode => {
      let txt = ''
      let type = 'warning'
      switch (row.state) {
        case '1':
          txt = '可报销'
          type = 'success'
          break
        case '2':
          txt = '已报销'
          type = 'info'
          break
        case '3':
          txt = '发票重复'
          break
        case '4':
          txt = '报销审核中'
          break
        case '5':
          txt = '发票信息不规范'
          break
        case '6':
          txt = '人工校正不通过'
          break
        case '7':
          txt = '识别失败'
          break
        case '8':
          txt = '核验失败'
          break
        case '9':
          txt = '已作废'
          break
      }
      return h(NTag, {size: 'small', type: type as any}, () => txt)
    },
  },
  {
    title: '发票类型',
    key: 'invoType',
    width: 150,
    render: (row: any) => {
      const dicts = JPGlobal.getDictByType('INVOICE_TYPE')
      const invoType = dicts.find(e => e.value == row.invoType)
      if (invoType) {
        return h(NTag, {size: 'small', type: 'info'}, () => invoType.label)
      }
    },
  },
  {
    title: '是否人工校正',
    key: 'manualAmend',
    width: 100,
    render: (row: any) => {
      if (row.manualAmend && row.manualAmend == '1') {
        return h(NTag, {type: 'success', size: 'small'}, '已人工校正')
      }
      if (row.status && row.status == '1') {
        return h(NTag, {type: 'success', size: 'small'}, '校正申请中')
      }
    },
  },
  {
    title: '校正人员',
    key: 'amendUserName',
    width: 100,
  },
  {
    title: '校验码',
    key: 'chkCode',
    width: 100,
  },
  {
    title: '核验状态',
    key: 'chkState',
    width: 100,
    tagRender: true,
    align: 'center',
    render: (row: any) => {
      if (row.chkTime) {
        switch (row.chkState) {
          case '1':
            return h(NTag, {type: 'success', size: 'small'}, () => '通过')
          case '2':
            return h(NTag, {type: 'error', size: 'small'}, () => '不通过')
        }
      }
      return ''
    },
  },
  /*{
  title: '核验数据',
  key: 'chkData',
  width: 100,
  render: (row: any) => {
    if (row.chkState && row.chkState != '1') {
      return row.chkData
    }
    return ''
  },
},*/
  {
    title: '发票查看',
    key: 'fileChk',
    width: 100,
    align: 'center',
    fixed: 'right',
    render: (row: any) => {
      return h(
          'span',
          {
            style: JPGlobal.linkStyle,
            onclick: () => {
              invoPreviewPath.value = row.att
              showInvoPreview.value = true
            },
          },
          '查看'
      )
    },
  },
])

//方法

//选择发票
const doChooseInvo = () => {
  //获取勾选的发票信息
  let choosedInvos = crudRef.value.originData.filter((item: any) => checkRowKeys.value.includes(item.id))
  //判断发票状态
  let valid = true
  choosedInvos.forEach(item => {
    if (item.state != '1') {
      valid = false
    }
  })
  if (!valid) {
    window.$message.error('请选择可报销发票')
    return
  }
  emits('rtInvos', choosedInvos)
}

let showSppplierInvos = ref(false)
//选择当前供应商发票
const choiceSppplierInvos = () => {
  showSppplierInvos.value = true
  attMapData.value = Array.from(props.attMap.entries()).map(([key, value]) => ({
    attName: key,
    att: value,
  }))
}
let showPreview1 = ref(false)
let previewOssPath = ref('')
let previewOssPathName = ref('')
let attMapData = ref([])
let supplierInvosColumns = ref<Array<CRUDColumnInterface>>([
  {
    title: '#',
    key: 'index', // 使用自增序号作为标识
    render: (row: any, index: number) => index + 1,
    type: ContainerValueType.SELECTION,
    width: 100,
  },
  {
    title: '文件名称',
    key: 'attName',
    width: 100,
  },
  {
    title: '发票文件',
    key: 'att',
    width: 100,
    render: (row: any) => {
      return h(
          NPopover,
          {
            trigger: 'hover',
          },
          {
            trigger: () =>
                h(
                    'span',
                    {
                      style: {
                        color: '#18a058',
                        cursor: 'pointer',
                      },
                      onClick: () => {
                        previewOssPath.value = row.att
                        previewOssPathName.value = row.attName
                        showPreview1.value = true
                      },
                    },
                    row.att
                ),
            default: () => '发票预览',
          }
      )
    },
  },
  {
    title: '文件下载',
    key: 'download',
    width: 80,
    align: 'center',
    render: (row: any) => {
      return h(
          'n-button',
          {
            secondary: true,
            size: 'small',
            type: 'primary',
            onClick: () => {
              download({bucket: 'mmis', path: row.att}).then(res => {
                JPGlobal.download(res.data, row.att)
              })
            },
          },
          {default: () => '下载'}
      )
    },
  },
])
let selectSupplierInvosRows = ref([])
const handleSupplierInvoCheck = (checkedKeys: any) => {
  console.log(checkedKeys)
  selectSupplierInvosRows.value = checkedKeys
}
//从物资发票库下载文件到这里触发上传发票
const downloadInvosFromMmisInvoStock = () => {
  selectSupplierInvosRows.value.forEach(filePath => {
    download({bucket: 'mmis', path: filePath})
        // mmisInvosFileDownload({
        //   filePath: filePath,
        // })
        .then(res => {
          // 根据文件名后缀判断类型
          const ext = filePath.split('.').pop().toLowerCase()
          if (!['pdf', 'png', 'jpg', 'jpeg', 'ofd'].includes(ext)) {
            window.$message.error('不支持的文件格式')
            return
          }
          const mimeType =
              ext === 'pdf'
                  ? 'application/pdf'
                  : ext === 'ofd'
                      ? 'application/ofd'
                      : ['png', 'jpg', 'jpeg'].includes(ext)
                          ? `image/${ext}`
                          : 'application/octet-stream'

          // 创建临时下载链接
          // JPGlobal.download(res.data, 'xt1.pdf')
          const file = new File([res.data], filePath, {
            type: mimeType,
          })
          try {
            afterUpload([file])
          } catch (error) {
            window.$message.error('文件处理失败，请检查文件完整性')
            console.error(error)
          }
        })
        .catch(error => {
          window.$message.error('文件下载失败')
          console.error(error)
        })
  })
  showSppplierInvos.value = false
  selectSupplierInvosRows.value = []
}

const uploadInvos = () => {
  showUpload.value = true
}

const invoDel = () => {
  if (checkRowKeys.value.length == 0) {
    window.$message.error('未选择删除的发票')
    return
  }

  if (queryForm.value.state == '2' || queryForm.value.state == '3') {
    window.$message.error('只有可报销或识别失败的发票才可删除')
    return
  }

  deleteEcsInvoRcd({ids: checkRowKeys.value}).then((res: any) => {
    if (res.code == 200) {
      window.$message.success('删除成功')
      crudRef.value.queryData()
    }
  })
}

const invoAmend = () => {
  //发票状态
  if (checkRowKeys.value.length == 0) {
    window.$message.error('当前未选择记录')
    return
  }
  let amendData = crudRef.value.originData.filter((item: any) => checkRowKeys.value.includes(item.id))
  //判断状态
  let valid = true
  amendData.forEach((item: any) => {
    if (['1', '2', '3', '4'].includes(item.state) || item.status == '1') {
      valid = false
    }
  })
  if (!valid) {
    window.$message.error('提交人工审核发票不规范')
    return
  }
  manualAmendInvo({ids: checkRowKeys.value}).then(res => {
    if (res.code == 200) {
      window.$message.success('发票人工校正申请成功')
      crudRef.value.queryData()
      checkRowKeys.value = []
    }
  })
}

let invoTypeOptions = ref([])

onMounted(() => {
  console.log('@@-初始化')
  checkRowKeys.value = []
  initAttMap()
  invoTypeOptions.value = JPGlobal.getDictByType('INVOICE_TYPE').map(item => ({
    label: item.label,
    value: item.value
  }))

  console.log(router.currentRoute.value.path)
  console.log(JPGlobal.pageButtonAuth('费用报销-发票审核', router.currentRoute.value.path))
  //初始化判断是否为发票管理员-费用报销-发票审核
  isInvoAdmin.value = JPGlobal.pageButtonAuth('费用报销-发票审核', router.currentRoute.value.path)

})
const initAttMap = () => {
  if (props.attMap.size === 1) {
    // 获取attMap中唯一的键值对
    const [attName, att] = Array.from(props.attMap.entries())[0]
    // 构造attMapData数据结构
    attMapData.value = [
      {
        attName: attName,
        att: att,
      },
    ]
    // 设置选中行
    selectSupplierInvosRows.value = [att]
    // 调用下载识别方法
    downloadInvosFromMmisInvoStock()
  }
}

const afterUpload = (files: Array<File>, modify: boolean = true) => {
  if (files && files.length > 0) {
    let count = 0
    let formData = new FormData()

    formData.append('invoFrom', props.invoFrom)
    if (queryForm.value.invoType) {
      formData.append('invoType', queryForm.value.invoType)
    }
    if (queryForm.value.createUser) {
      formData.append('createUser', queryForm.value.createUser)
    }
    let tfs: File[] = files
    for (let j = 0; j < tfs.length; j++) {
      formData.append('attFiles[' + count + ']', tfs[j])
      count++
    }
    if (count > 0) {
      //发票识别
      const loadingInstance = ElLoading.service({
        fullscreen: true,
        lock: true,
        text: '发票识别中...',
        background: 'rgba(0,0,0,0.7)',
      })
      ocrIdentify(formData)
          .then(res => {
            window.$message.success('发票上传成功')
            if (res.code == 200 && res.data.length > 0) {
              errMsg.value = res.data
              errMsgShow.value = true
            }
            //重新查询时间
            crudRef.value.queryData()
            // 上传成功后将发票类型置空
            queryForm.value.invoType = ''
          })
          .finally(() => {
            loadingInstance.close()
          })
    }
  }
}

//发票识别完成
const ocrComplete = (td: any) => {
  //重新查询
  crudRef.value.queryData()
}

//发票确认
const invoConfirm = () => {
}

//tab改变
const tabChange = (tab: JTab) => {
  queryForm.value.state = tab.name
  checkRowKeys.value = []
}

const getColumns = (name: string) => {
  if (name == '1') {
    return columns.value.slice(1)
  }
  return columns.value
}

const tabs = ref<JTab[]>([
  {
    name: '1',
    tab: '可报销',
    columns: columns.value,
    tabChange: tabChange,
  },
  {
    name: '2',
    tab: '已报销',
    columns: columns.value,
    tabChange: tabChange,
  },
  {
    name: '3',
    tab: '报销中',
    columns: columns.value,
    tabChange: tabChange,
  },
  {
    name: '4',
    tab: '识别失败',
    columns: columns.value,
    tabChange: tabChange,
  },
  {
    name: '5',
    tab: '作废',
    columns: columns.value,
    tabChange: tabChange,
  },
  {
    name: '0',
    tab: '其他',
    columns: columns.value,
    tabChange: tabChange,
  },
])

defineExpose({doChooseInvo, initAttMap})
</script>

<style lang="less" scoped></style>
