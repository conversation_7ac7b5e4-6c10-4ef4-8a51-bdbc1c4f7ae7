<template>
  <!-- {{ formulaVariablesMap }} -->
  <div class="formula-editor">
    <!-- 标题栏 -->
    <div class="editor-header" v-if="title">
      <span class="editor-title">{{ title }}</span>

      <div style="display: flex">
        <slot name="header"> </slot>
      </div>
    </div>

    <!-- 工具栏 -->
    <EditorToolbar @insert-operator="insertOperator" />

    <!-- 代码编辑器 -->
    <div class="editor-code-area">
      <Codemirror
        v-model="value"
        @ready="handleReady"
        class="formula-editor-codemirror single-line"
        :style="{ height: `${height}px`, minHeight: '200px', fontSize: '14px' }"
        :autofocus="false"
        :indent-with-tab="true"
        :tab-size="2"
        :extensions="extensions"
        :disabled="disabled"
        @blur="
          () => {
            // emit('getResult')
          }
        "
        :placeholder="placeholder || '请输入计算公式'"
      />
    </div>

    <!-- 底部面板区域 -->
    <div class="editor-bottom-panel">
      <n-split direction="horizontal" style="height: 100%" :default-size="0.55" :max="0.8" :min="0.4">
        <template #1>
          <!-- 左侧变量区域 -->
          <div class="variables-panel">
            <div class="panel-header">
              <h3 class="panel-title" style="margin-right: 10px">计算变量</h3>
              <div style="display: flex; align-items: center; gap: 8px; flex-grow: 1">
                <!-- <n-select
                  v-model:value="selectedViewMode"
                  :options="templateSourceOptions"
                  size="small"
                  style="min-width: 120px"
                /> -->
                <!-- <span style="font-size: 11px; color: #555; min-width: 50px">{{
                  selectedViewMode === 'current' ? '当前模板' : '外部模板'
                }}</span> -->
                <n-switch v-model:value="isExternalMode" size="small" style="min-width: 120px">
                  <template #checked> 外部模板 </template>
                  <template #unchecked> 当前模板 </template>
                </n-switch>

                <n-select
                  v-if="selectedViewMode === 'external'"
                  v-model:value="selectedExternalTemplateId"
                  :options="availableTemplateOptions"
                  placeholder="选择外部模板"
                  filterable
                  clearable
                  size="small"
                  style="min-width: 180px; flex-grow: 1"
                  :loading="externalTemplateLoading"
                  @update:value="loadExternalVariables"
                />
                <div class="search-wrapper" :style="{ marginLeft: selectedViewMode === 'current' ? 'auto' : '8px' }">
                  <input type="text" class="search-input" v-model="searchQuery" placeholder="搜索变量..." />
                  <button class="search-clear" v-if="searchQuery" @click="searchQuery = ''">×</button>
                </div>
              </div>
            </div>

            <!-- 搜索结果 -->
            <div v-if="searchQuery" class="search-results-container">
              <div class="search-result-count">找到 {{ searchResults.length }} 个结果</div>
              <div class="variable-list">
                <div
                  v-for="item in searchResults"
                  :key="item.value"
                  class="variable-item"
                  @click="handleVariableInsertion(item)"
                >
                  <div class="variable-labels">
                    <template v-for="(part, index) in item.label.split('.')" :key="index">
                      <span class="variable-tag">{{ part }}</span>
                      <span v-if="index < item.label.split('.').length - 1" class="dot">·</span>
                    </template>
                  </div>

                  <div class="variable-details">
                    <span class="variable-desc" v-if="item.desc">{{ item.desc }}</span>
                    <div class="test-value-group">
                      <span class="test-label">测试值:</span>
                      <input
                        type="number"
                        class="test-value-input"
                        :value="Number(item.mockData)"
                        @input="e => item.mockData = parseFloat((e.target as HTMLInputElement).value)"
                        @click.stop
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 使用 CategoryBrowser 组件显示变量 -->
            <div v-else-if="externalTemplateLoading" class="loading-indicator panel-content-area">
              正在加载外部模板变量...
            </div>

            <div v-else-if="selectedViewMode === 'external' && !selectedExternalTemplateId" class="panel-content-area">
              <div v-if="availableTemplateOptions.length > 0" class="template-card-grid-container">
                <div
                  v-for="template in availableTemplateOptions"
                  :key="template.value"
                  class="template-card"
                  @click="handleExternalTemplateCardClick(template.value)"
                >
                  {{ template.label }}
                </div>
              </div>
              <div v-else class="empty-indicator" style="padding-top: 20px"> 没有可供选择的外部模板。 </div>
            </div>

            <CategoryBrowser
              v-else-if="displayedRenderMap && Object.keys(displayedRenderMap).length > 0"
              class="panel-content-area"
              :formula-variables-render-map="displayedRenderMap"
              v-model:modelValueCategory="activeSelectedCategory"
              v-model:modelValueSubcategory="activeSelectedSubcategory"
              @insert-variable="handleVariableInsertion"
            />

            <div
              v-else-if="
                selectedViewMode === 'current' && (!displayedRenderMap || Object.keys(displayedRenderMap).length === 0)
              "
              class="empty-indicator panel-content-area"
              style="padding-top: 20px"
            >
              当前模板无分类变量数据。
            </div>
            <div
              v-else-if="
                selectedViewMode === 'external' &&
                selectedExternalTemplateId &&
                (!externalTemplateData ||
                  Object.keys(externalTemplateData.formulaVariablesRenderMap || {}).length === 0)
              "
              class="empty-indicator panel-content-area"
              style="padding-top: 20px"
            >
              所选外部模板无可用的分类变量数据。
            </div>

            <div v-else class="empty-indicator panel-content-area" style="padding-top: 20px">
              请在上方选择模板类型或进行搜索。
            </div>
          </div>
        </template>
        <template #2>
          <!-- 右侧函数区域 -->
          <div class="functions-panel">
            <div class="panel-header">
              <h3 class="panel-title">函数</h3>
              <div class="search-wrapper">
                <input type="text" class="search-input" v-model="functionSearchQuery" placeholder="搜索函数..." />
                <button class="search-clear" v-if="functionSearchQuery" @click="functionSearchQuery = ''">×</button>
              </div>
            </div>

            <div class="functions-container">
              <div class="functions-list scrollable">
                <div
                  v-for="(item, key) in filteredFunctions"
                  :key="key"
                  class="function-item"
                  @click="insertMaths(key as MathName)"
                  @mouseenter="activeMath = key as MathName"
                  :class="{ active: activeMath === key }"
                >
                  <span class="function-name">{{ key }}</span>
                  <span class="function-desc">{{ item.name }}</span>
                </div>
              </div>

              <div class="function-details">
                <h4 class="detail-title">函数说明</h4>
                <div class="detail-content">
                  <p class="detail-text">{{ MATH_DESC_LIST[activeMath].desc }}</p>
                  <p class="detail-usage">语法：{{ MATH_DESC_LIST[activeMath].usage }}</p>
                  <pre class="detail-example">示例：{{ MATH_DESC_LIST[activeMath].example }}</pre>
                </div>
              </div>
            </div>
          </div>
        </template>
      </n-split>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { Codemirror } from 'vue-codemirror'
  import { ref, computed, watch, type PropType, toRefs } from 'vue'
  import { MATH_DESC_LIST, type MathName } from '../utils/math'
  import { uuid } from '../utils/index'
  import type { VariableItem } from '../interfaces'
  import { storeToRefs } from 'pinia'
  import { onMounted } from 'vue'
  import { NSelect, NSplit, NSwitch } from 'naive-ui'
  import { useRoute } from 'vue-router'
  import { usePmsCalcFormula } from '../utils/pmsCalcFormulaHook'
  import CategoryBrowser from '../components/CategoryBrowser.vue' // 导入新组件
  import EditorToolbar from '../components/EditorToolbar.vue' // 导入工具栏组件
  import { useAwardTemplateStore } from '@/store/pms/awardTemplateStore'
  // Import store and new function
  // 导入 store 和新函数
  import { useAwardCalcStore } from '@/store/pms/awardCalcStore'
  import { useAwardCalcItemGroupPreviewStore } from '@/store/pms/awardCalcItemGroupPreviewStore.tsx'
  loadAndProcessFormulaVariablesForTemplate
  import type { VariableItem as FormulaVariableItem } from '../interfaces' // Ensure VariableItem is imported if needed for types
  import { loadAndProcessFormulaVariablesForTemplate } from '@/store/pms/util/useBuildFormulaVariableMapsLogic'
  // 确保在需要类型时导入 VariableItem

  const emit = defineEmits(['getResult'])
  const props = defineProps({
    title: {
      type: String,
      default: '',
    },
    isDark: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    variables: {
      type: Array as PropType<FormulaVariableItem[]>,
      default: () => [],
    },
    formulaErrorMessage: {
      type: String,
      default: '',
    },
    height: {
      type: Number,
      default: 150,
    },
    placeholder: {
      type: String,
      default: '',
    },
  })

  const awardTemplateStore = useAwardTemplateStore()
  const awardCalcStore = useAwardCalcStore()
  const awardCalcItemGroupPreviewStore = useAwardCalcItemGroupPreviewStore()
  const {
    formulaVariablesMap,
    formulaVariablesRenderMap: currentTemplateRenderMap,
    selectRow,
  } = storeToRefs(awardCalcItemGroupPreviewStore)
  const refreshKey = uuid()
  const refreshNum = ref(0)
  const variablesVals = computed(() => {
    return props.variables.map(item => item.value)
  })

  const { isDark } = toRefs(props)

  // 输出值
  const value = defineModel<string>({ default: '' })

  // --- 使用 Hook ---
  const { editor, extensions, handleReady, insertVariables, insertMaths, insertOperator, insertCrossTemplateVariable } =
    usePmsCalcFormula({
      isDark: isDark,
      valueModel: value,
      formulaVariablesMap: computed(() => {
        // Make formulaVariablesMap for hook reactive to current context
        // 使 hook 的 formulaVariablesMap 对当前上下文具有响应性
        if (selectedViewMode.value === 'external' && externalTemplateData.value?.formulaVariablesMap) {
          return externalTemplateData.value.formulaVariablesMap
        }
        return formulaVariablesMap.value // from awardCalcItemGroupPreviewStore (current template)
        // 来自 awardCalcItemGroupPreviewStore (当前模板)
      }),
      isEditor: true,
    })

  // 当前聚焦的math
  const activeMath = ref<MathName>('ABS')

  // 状态管理
  const searchQuery = ref('')
  const functionSearchQuery = ref('')
  const activeSelectedCategory = ref('') // Renamed from selectedCategory for clarity
  const activeSelectedSubcategory = ref<Record<string, string>>({}) // Renamed from selectedSubcategory
  // 为清晰起见，从 selectedCategory 重命名
  // 从 selectedSubcategory 重命名

  // --- New state for unified variable display ---
  // --- 用于统一变量显示的新状态 ---
  type ViewMode = 'current' | 'external'
  const selectedViewMode = ref<ViewMode>('current')
  const selectedExternalTemplateId = ref<string | null>(null)
  const externalTemplateData = ref<{
    formulaVariablesMap: Record<string, VariableItem[]>
    formulaVariablesRenderMap: Record<string, Record<string, VariableItem[]>>
  } | null>(null)
  const externalTemplateLoading = ref(false)

  const templateSourceOptions = [
    { label: '当前模板变量', value: 'current' },
    { label: '外部模板变量', value: 'external' },
  ]

  // Computed property to bridge n-switch boolean state with selectedViewMode string state
  // 计算属性，用于桥接 n-switch布尔状态和 selectedViewMode 字符串状态
  const isExternalMode = computed({
    get: () => selectedViewMode.value === 'external',
    set: newVal => {
      selectedViewMode.value = newVal ? 'external' : 'current'
    },
  })

  // Controls the CategoryBrowser's displayed data
  // 控制 CategoryBrowser 显示的数据
  const displayedRenderMap = computed(() => {
    if (selectedViewMode.value === 'external' && selectedExternalTemplateId.value && externalTemplateData.value) {
      return externalTemplateData.value.formulaVariablesRenderMap
    }
    return currentTemplateRenderMap.value // Default to current template's render map
    // 默认为当前模板的渲染映射
  })

  // 将传入的模板列表转换为 NSelect 需要的格式
  const route = useRoute()
  const currentRowId = route.query.rowId || route.params.rowId || null
  const availableTemplateOptions = computed(() => {
    return awardTemplateStore.availableTemplates
      .filter(t => String(t.id) !== String(currentRowId))
      .map(t => ({ label: `${t.name} (ID: ${t.id})`, value: String(t.id) }))
  })

  // 搜索结果 - now searches within displayedRenderMap categories
  // 搜索结果 - 现在在 displayedRenderMap 类别中搜索
  const searchResults = computed(() => {
    if (!searchQuery.value) return []
    const currentMap = displayedRenderMap.value
    if (!currentMap || typeof currentMap !== 'object') return []

    const query = searchQuery.value.toLowerCase()
    const results: FormulaVariableItem[] = []
    Object.keys(currentMap).forEach(category => {
      if (currentMap[category] && typeof currentMap[category] === 'object') {
        Object.keys(currentMap[category]).forEach(subcategory => {
          if (Array.isArray(currentMap[category][subcategory])) {
            currentMap[category][subcategory].forEach(item => {
              if (item.label.toLowerCase().includes(query) || (item.desc && item.desc.toLowerCase().includes(query))) {
                results.push(item)
              }
            })
          }
        })
      }
    })
    return results
  })

  const handleExternalTemplateCardClick = (templateId: string) => {
    if (templateId) {
      selectedExternalTemplateId.value = templateId
      loadExternalVariables(templateId)
    }
  }

  // --- Handler for variable insertion ---
  // --- 变量插入处理程序 ---
  const handleVariableInsertion = (variable: FormulaVariableItem) => {
    if (selectedViewMode.value === 'external' && selectedExternalTemplateId.value) {
      insertCrossTemplateVariable(selectedExternalTemplateId.value, variable)
    } else {
      insertVariables(variable)
    }
  }

  // 判断搜索匹配
  const isMatchedTerm = (text: string) => {
    if (!searchQuery.value) return false
    return text.toLowerCase().includes(searchQuery.value.toLowerCase())
  }

  // 函数搜索
  const filteredFunctions = computed(() => {
    if (!functionSearchQuery.value) return MATH_DESC_LIST

    const query = functionSearchQuery.value.toLowerCase()
    const result: Record<string, any> = {}

    Object.keys(MATH_DESC_LIST).forEach(key => {
      const item = MATH_DESC_LIST[key as MathName]
      if (
        key.toLowerCase().includes(query) ||
        item.name.toLowerCase().includes(query) ||
        item.desc.toLowerCase().includes(query)
      ) {
        result[key] = item
      }
    })

    return result
  })

  // --- Extracted Category Initialization Logic ---
  // --- 提取的类别初始化逻辑 ---
  function initializeActiveCategories(
    renderMapToUse: Record<string, Record<string, FormulaVariableItem[]>> | undefined,
    categoryTargetRef: typeof activeSelectedCategory,
    subcategoryTargetRef: typeof activeSelectedSubcategory,
    rowContext?: { classification?: string; itemName?: string }
  ) {
    categoryTargetRef.value = '' // Reset first
    subcategoryTargetRef.value = {} // Reset first
    // 首先重置
    // 首先重置

    if (renderMapToUse && typeof renderMapToUse === 'object') {
      const categories = Object.keys(renderMapToUse)
      let initialCategory = ''

      if (rowContext?.classification && renderMapToUse[rowContext.classification]) {
        initialCategory = rowContext.classification
      } else if (categories.length > 0) {
        initialCategory = categories[0]
      }
      categoryTargetRef.value = initialCategory

      if (initialCategory && renderMapToUse[initialCategory] && typeof renderMapToUse[initialCategory] === 'object') {
        const subcategories = Object.keys(renderMapToUse[initialCategory])
        let initialSubcategory = ''

        if (rowContext?.itemName && renderMapToUse[initialCategory][rowContext.itemName]) {
          initialSubcategory = rowContext.itemName
        } else if (subcategories.length > 0) {
          initialSubcategory = subcategories[0]
        }

        if (initialSubcategory) {
          subcategoryTargetRef.value = { ...subcategoryTargetRef.value, [initialCategory]: initialSubcategory }
        }
      } else if (categories.length > 0 && !initialCategory) {
        // Fallback if rowContext didn't match
        initialCategory = categories[0]
        categoryTargetRef.value = initialCategory
        if (renderMapToUse[initialCategory] && typeof renderMapToUse[initialCategory] === 'object') {
          const subcats = Object.keys(renderMapToUse[initialCategory])
          if (subcats.length > 0) {
            subcategoryTargetRef.value = { [initialCategory]: subcats[0] }
          }
        }
      }
    }
  }

  // 监听变量变化就行刷新
  watch(
    () => [variablesVals.value, formulaVariablesMap], // formulaVariablesMap is for current template context
    // formulaVariablesMap 用于当前模板上下文
    () => {
      refreshNum.value++
      emit('getResult', false)
    },
    {
      deep: true,
      immediate: true,
    }
  )

  // 在组件挂载时自动选择第一个分类和子分类
  onMounted(() => {
    // Ensure currentTemplateRenderMap.value is an object before trying to access its keys or properties
    // 确保 currentTemplateRenderMap.value 是一个对象，然后再尝试访问其键或属性
    initializeActiveCategories(
      currentTemplateRenderMap.value,
      activeSelectedCategory,
      activeSelectedSubcategory,
      selectRow.value && typeof selectRow.value === 'object' ? selectRow.value : undefined
    )
  })

  // --- New logic for loading external template data ---
  // --- 加载外部模板数据的新逻辑 ---
  async function loadExternalVariables(templateId: string | null) {
    if (!templateId) {
      externalTemplateData.value = null
      // When clearing external template, re-initialize categories for current displayed map (which would revert to current)
      // However, selectedViewMode change handles this better.
      // 清除外部模板时，为当前显示的映射重新初始化类别（这将恢复为当前）
      // 但是，selectedViewMode 更改可以更好地处理此问题。
      return
    }
    if (selectedViewMode.value !== 'external') return // Should not happen if UI is correct
    // 如果 UI 正确，则不应发生这种情况

    externalTemplateLoading.value = true
    externalTemplateData.value = null // Clear previous external data
    // 清除以前的外部数据
    try {
      const globalClassSet = awardCalcStore.awardCalcItemClassSet
      const globalConstList = awardCalcStore.awardConstCalcItemConfigList

      if (!globalClassSet || !globalConstList) {
        window.$message?.error('全局分类或常量列表未加载，无法获取外部模板变量。')
        externalTemplateLoading.value = false
        return
      }

      const result = await loadAndProcessFormulaVariablesForTemplate(templateId, globalClassSet, globalConstList)

      if (result && result.formulaVariablesRenderMap && result.formulaVariablesMap) {
        externalTemplateData.value = {
          formulaVariablesRenderMap: result.formulaVariablesRenderMap,
          formulaVariablesMap: result.formulaVariablesMap,
        }
      } else {
        window.$message?.info(`加载外部模板 [${templateId}] 变量数据为空或失败。`)
      }
    } catch (error) {
      console.error(`获取外部模板 [${templateId}] 变量失败:`, error)
      window.$message?.error(`获取外部模板 [${templateId}] 变量失败`)
    } finally {
      externalTemplateLoading.value = false
      // Initialize categories for the newly loaded external map, or clear if load failed
      initializeActiveCategories(
        externalTemplateData.value?.formulaVariablesRenderMap,
        activeSelectedCategory,
        activeSelectedSubcategory
        // No row context for external templates usually, unless we want to try and match
        // 通常外部模板没有行上下文，除非我们想尝试匹配
      )
    }
  }

  watch(selectedViewMode, async newMode => {
    searchQuery.value = '' // Clear search when mode changes
    // 模式更改时清除搜索
    if (newMode === 'current') {
      selectedExternalTemplateId.value = null // Clear external selection
      externalTemplateData.value = null // Clear external data
      // 清除外部选择
      // 清除外部数据
      initializeActiveCategories(
        currentTemplateRenderMap.value,
        activeSelectedCategory,
        activeSelectedSubcategory,
        selectRow.value && typeof selectRow.value === 'object' ? selectRow.value : undefined
      )
    } else if (newMode === 'external') {
      // If there's already an external template ID selected, load it. Otherwise, user needs to select one.
      // 如果已经选择了外部模板 ID，则加载它。否则，用户需要选择一个。
      if (selectedExternalTemplateId.value) {
        await loadExternalVariables(selectedExternalTemplateId.value)
      } else {
        // No external template selected yet, clear categories or show placeholder
        // 尚未选择外部模板，清除类别或显示占位符
        externalTemplateData.value = null
        initializeActiveCategories(undefined, activeSelectedCategory, activeSelectedSubcategory)
      }
    }
  })

  // Watch for changes in the current template's data (e.g., if user modifies it elsewhere)
  // 监视当前模板数据的更改（例如，如果用户在其他地方修改了它）
  watch(
    currentTemplateRenderMap,
    newMap => {
      if (selectedViewMode.value === 'current') {
        initializeActiveCategories(
          newMap,
          activeSelectedCategory,
          activeSelectedSubcategory,
          selectRow.value && typeof selectRow.value === 'object' ? selectRow.value : undefined
        )
      }
    },
    { deep: true }
  )

  defineOptions({
    name: 'FormulaEditor',
  })
</script>

<style lang="scss" scoped>
  @import './index.scss';
</style>
