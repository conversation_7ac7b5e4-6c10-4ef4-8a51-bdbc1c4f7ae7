<template>
  <el-form label-width="100px">
    <!-- {{ <PERSON><PERSON><PERSON>(elementBusinessObject.loopCharacteristics) }} -->

    <el-form-item label="规则类型" prop="candidateStrategy">
      <el-select
        v-model="userTaskForm.candidateStrategy"
        clearable
        style="width: 100%"
        @change="changeCandidateStrategy"
      >
        <el-option
          v-for="dict in getIntDictOptions(DICT_TYPE.BPM_TASK_CANDIDATE_STRATEGY)"
          :key="Number(dict.value)"
          :label="dict.label"
          :value="Number(dict.value)"
        />
      </el-select>
    </el-form-item>

    <!-- 指定归集角色 hrp-->
    <el-form-item v-if="userTaskForm.candidateStrategy == 10" label="指定归集角色" prop="candidateParam">
      <el-select
        v-model="userTaskForm.candidateParam"
        clearable
        filterable
        style="width: 100%"
        @change="updateElementTask()"
      >
        <el-option v-for="item in roleOptions" :key="item.id" :label="item.label" :value="item.value" />
      </el-select>
    </el-form-item>
    <!-- 指定部门 hrp-->
    <el-form-item
      v-if="
        userTaskForm.candidateStrategy == 21 || //部门负责人
        userTaskForm.candidateStrategy == 10 || //归集角色
        userTaskForm.candidateStrategy == 35 || //发起人自选
        userTaskForm.candidateStrategy == 22 //岗位
      "
      label="指定部门"
      prop="deptParam"
      span="24"
    >
      <el-tree-select
        :check-strictly="true"
        ref="treeRef"
        v-model="userTaskForm.deptParam"
        :data="deptTreeOptions"
        empty-text="加载中，请稍后"
        node-key="id"
        clearable
        filterable
        @change="updateElementTask"
      />{{ userTaskForm.deptParam }}
    </el-form-item>
    <!-- 指定post岗位 hrp（职务类别） jobCategoryOptions-->

    <el-form-item
      v-if="
        userTaskForm.candidateStrategy == 35 || //发起人自选
        userTaskForm.candidateStrategy == 22 //岗位
      "
      label="指定岗位"
      prop="candidateParam"
      span="24"
    >
      <el-select
        v-model="userTaskForm.candidateParam"
        clearable
        multiple
        style="width: 100%"
        @change="updateElementTask"
      >
        <el-option v-for="item in jobCategoryOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </el-form-item>
    <!-- 指定用户 hrp-->
    <el-form-item v-if="userTaskForm.candidateStrategy == 30" label="筛选用户部门" prop="deptParam" span="24">
      <el-tree-select
        v-model="userTaskForm.deptParam"
        :data="deptTreeOptions"
        empty-text="加载中，请稍后"
        node-key="id"
        clearable
        filterable
      />
      <!-- <j-bus-emp-search v-model:value="userTaskForm.candidateParam" multiple @changeValue="updateElementTask()" /> -->
    </el-form-item>
    <el-form-item v-if="userTaskForm.candidateStrategy == 30" label="指定用户" prop="candidateParam" span="24">
      <j-bus-emp-search
        v-model:value="userTaskForm.candidateParam"
        :dept="userTaskForm.deptParam"
        multiple
        @changeValue="updateElementTask()"
      />
    </el-form-item>
    <!-- 指定用户组 -->
    <el-form-item v-if="userTaskForm.candidateStrategy === 40" label="指定用户组" prop="candidateParam">
      <el-select
        v-model="userTaskForm.candidateParam"
        clearable
        multiple
        style="width: 100%"
        @change="updateElementTask"
      >
        <el-option v-for="item in userGroupOptions" :key="item.id" :label="item.name" :value="String(item.id)" />
      </el-select>
    </el-form-item>
    <!-- 流程表达式 -->
    <el-form-item v-if="userTaskForm.candidateStrategy === 60" label="流程表达式" prop="candidateParam">
      <el-input
        v-model="userTaskForm.candidateParam[0]"
        type="textarea"
        :rows="3"
        placeholder="请输入流程表达式"
        :style="{ width: '100%' }"
        class="expression-input"
        @change="updateElementTask"
      />

      <el-button style="margin-top: 1em" size="small" type="success" @click="openProcessExpressionDialog"
        >选择表达式</el-button
      >
      <!-- 选择弹窗 -->
      <ProcessExpressionDialog ref="processExpressionDialogRef" @select="selectProcessExpression" />
    </el-form-item>
    <el-divider />
    <!-- <el-form-item label="" prop="candidateParam" span="24" label-position="top"> -->
    <!-- {{ userStore?.userInfo }} -->
    <div v-if="[21, 10, 22, 35].includes(userTaskForm.candidateStrategy)">
      审批人预览：
      <j-bus-emp-search
        v-show="true"
        ref="userPreviewRef"
        :dept="userTaskForm.deptParam == '0' ? userDept : userTaskForm.deptParam"
        :restrictedEmpType="getRestrictedEmpType()"
        @changeValue="changeEmpOptions"
      />
    </div>

    <!-- {{ userTaskForm.deptParam }} -->
    <!-- {{ userTaskForm.candidateParam }} -->
    <!-- {{ empOptions }} -->
    <!-- <el-table
      v-if="[21, 10, 22].includes(userTaskForm.candidateStrategy)"
      style="width: 100%"
      :data="JSON.parse(JSON.stringify(userPreviewRef?.empOptions))"
    >
      <el-table-column prop="empCode" label="员工编号" width="100"></el-table-column>
      <el-table-column prop="empName" label="姓名" width="120"></el-table-column>
      <el-table-column prop="orgName" label="部门"></el-table-column>
      <el-table-column prop="phone" label="电话"></el-table-column>
    </el-table> -->
    <!-- </el-form-item> -->
  </el-form>
</template>

<script lang="ts" setup>
  import { getIntDictOptions, DICT_TYPE } from '@/utils/bpmAdapter/bpmDictAdapter'
  import { defaultProps, handleTree } from '@/utils/bpmAdapter/tree'
  import empSearch from '@/components/common/business/hrm/empSearch/index.vue'
  import { Codemirror } from 'vue-codemirror'

  // import * as RoleApi from '@/api/system/role'
  // import * as DeptApi from '@/api/system/dept'
  // import * as PostApi from '@/api/system/post'
  import * as RoleApi from '@/api/bpm/bpmAdapter/roleApi'
  import * as DeptApi from '@/api/bpm/bpmAdapter/deptApi'
  import * as PostApi from '@/api/bpm/bpmAdapter/postApi'
  import * as UserApi from '@/api/bpm/bpmAdapter/user'
  import * as UserGroupApi from '@/api/bpm/userGroup'
  import ProcessExpressionDialog from './ProcessExpressionDialog.vue'
  import { ProcessExpressionVO } from '@/api/bpm/processExpression'
  import { onMounted, reactive, ref, computed, toRaw, watch, nextTick, onBeforeUnmount, watchEffect } from 'vue'
  import { useMessage } from '@/components/common/bpm/bpmAdapter/useMessage'
  import { querySysCollRole } from '@/api/sys/collRole'
  import JPGlobal from '@/types/common/jglobal'
  import { queryOrg } from '@/api/hrm/hrmOrg'
  import { queryTreeDictByType } from '@/api/hrm/dictManage/treeSelectDict'
  import { number } from 'mathjs'
  import { useUserStore } from '@/store'

  defineOptions({ name: 'UserTask' })
  const props = defineProps({
    id: String,
    type: String,
    multiple: Boolean,
  })
  const userTaskForm = ref({
    candidateStrategy: undefined, // 分配规则
    candidateParam: [], // 分配选项
    deptParam: '0',
  })
  const bpmnElement = ref()
  const bpmnInstances = () => (window as any)?.bpmnInstances

  const roleOptions = ref<any[]>([]) // 归集角色列表
  const deptTreeOptions = ref() // 部门树
  const jobCategoryOptions = ref<any[]>([]) // 岗位列表
  // const userOptions = ref<UserApi.UserVO[]>([]) // 用户列表
  const userGroupOptions = ref<UserGroupApi.UserGroupVO[]>([]) // 用户组列表
  const userStore = useUserStore()

  const userDept = userStore?.userInfo?.hrmUser?.hrmOrgId
  const resetTaskForm = () => {
    const businessObject = bpmnElement.value.businessObject
    if (!businessObject) {
      return
    }
    if (businessObject.candidateStrategy != undefined) {
      userTaskForm.value.candidateStrategy = parseInt(businessObject.candidateStrategy) as any
    } else {
      userTaskForm.value.candidateStrategy = undefined
    }
    if (businessObject.candidateParam && businessObject.candidateParam.length > 0) {
      if (userTaskForm.value.candidateStrategy === 60) {
        // 特殊：流程表达式，只有一个 input 输入框
        userTaskForm.value.candidateParam = [businessObject.candidateParam]
      } else {
        userTaskForm.value.candidateParam = businessObject.candidateParam.split(',').map(item => item)
      }
    } else {
      userTaskForm.value.candidateParam = []
    }
    if (businessObject.candidateDept && businessObject.candidateDept.length > 0) {
      userTaskForm.value.deptParam = businessObject.candidateDept
    } else {
      userTaskForm.value.deptParam = ''
    }
  }

  /** 更新 candidateStrategy 字段时，需要清空 candidateParam，并触发 bpmn 图更新 */
  const changeCandidateStrategy = () => {
    userTaskForm.value.candidateParam = []
    updateElementTask()
  }

  // userTaskForm.candidateStrategy == 21 || //部门负责人
  //         userTaskForm.candidateStrategy == 10 || //归集角色
  //         userTaskForm.candidateStrategy == 22 //岗位
  /** 选中某个 options 时候，更新 bpmn 图  */

  const updateElementTask = () => {
    if ([21, 10, 22].includes(userTaskForm.value.candidateStrategy)) {
    }
    if (userTaskForm.value.candidateParam.length > 1) {
      useMessage().warning('如果审批人有多个应配置多实例会签，否则会随机选择一个')
    }
    bpmnInstances().modeling.updateProperties(toRaw(bpmnElement.value), {
      candidateStrategy: userTaskForm.value.candidateStrategy,
      candidateDept: userTaskForm.value.deptParam,
      candidateParam: userTaskForm.value.candidateParam.join(','),
    })
  }

  // 打开监听器弹窗
  const processExpressionDialogRef = ref()
  const openProcessExpressionDialog = async () => {
    processExpressionDialogRef.value.open()
  }
  const selectProcessExpression = (expression: ProcessExpressionVO) => {
    userTaskForm.value.candidateParam = [expression.expression]
    updateElementTask()
  }

  watch(
    () => props.id,
    () => {
      bpmnElement.value = bpmnInstances().bpmnElement
      nextTick(() => {
        resetTaskForm()
      })
    },
    { immediate: true }
  )

  onMounted(async () => {
    // 获取归集角色列表
    await queryCollRoleInfo()

    // 获得部门列表
    // const deptOptions = await DeptApi.getSimpleDeptList()
    await queryOrgTree()

    // 获得岗位（职务类别）列表
    const jobCategoryOptionsRes = await queryTreeDictByType({ codeType: 'RANK' })
    jobCategoryOptions.value = JPGlobal.getSelectOption(jobCategoryOptionsRes.data, 'codeLable', 'id')

    // 获得用户列表

    // 获得用户组列表
    userGroupOptions.value = await UserGroupApi.getUserGroupSimpleList()
  })

  onBeforeUnmount(() => {
    bpmnElement.value = null
  })

  // 职务类别

  //查询归集角色
  const queryCollRoleInfo = async () => {
    let res = await querySysCollRole({})
    roleOptions.value = JPGlobal.getSelectOption(res.data, 'dscr', 'id')
  }

  //查询部门树
  const queryOrgTree = async () => {
    let orgRes = await queryOrg({})
    let data = orgRes.data
    let treeData: any = []
    data.forEach(item => {
      if (!item['orgParentId']) {
        treeData.push(item)
      }
      getChildren(data, treeData, 'orgId', 'orgParentId', 'orgName')
    })
    treeData.sort((a: any, b: any) => {
      return a['orgId'] - b['orgId']
    })
    treeData.unshift({
      id: '0',
      label: '发起人部门',
      value: '0',
    })
    deptTreeOptions.value = treeData
  }
  function getChildren(data: Array<any>, treeData: Array<any>, id: string | number, parentId: string, label: string) {
    treeData.forEach(parent => {
      parent.id = parent[id]
      parent.label = parent[label] || ''
      parent.value = parent[id]

      let children: any = []
      data.forEach(child => {
        if (child[parentId] == parent[id]) {
          children.push(child)
        }
      })
      parent.lowNum = 0
      if (children.length > 0) {
        parent.children = children
        parent.lowNum = children.length
        parent.children.sort((a: any, b: any) => {
          return a[id] - b[id]
        })
        getChildren(data, children, id, parentId, label)
      }
    })
  }

  const userPreviewRef: any = ref()
  const empOptions = ref([])
  const changeEmpOptions = (empOptions: any) => {
    console.log('empOptions', empOptions)
    empOptions.value = empOptions
  }
  // watch(
  //   () => userPreviewRef,
  //   newVal => {
  //     if (newVal.hasOwnProperty('empOptions')) {
  //       empOptions.value = JSON.parse(JSON.stringify(toRaw(newVal.empOptions)))
  //     }
  //   }
  // )
  watchEffect(() => {
    console.log('userPreviewRef', userPreviewRef.value)
  })
  const getRestrictedEmpType = () => {
    if (userTaskForm.value.candidateStrategy == 21) {
      return [514, 515, 518]
    }
    if (userTaskForm.value.candidateStrategy == 22 || userTaskForm.value.candidateStrategy == 35) {
      return userTaskForm.value.candidateParam
    }
    return []
  }
</script>
