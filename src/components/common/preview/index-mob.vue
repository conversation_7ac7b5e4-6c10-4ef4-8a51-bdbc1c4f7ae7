<template>
  <div class="mobile-preview-container" v-if="show">
    <!-- 移动端全屏预览 -->
    <div class="mobile-preview-wrapper" :class="{ 'loading': loading }">
      <!-- 顶部工具栏 -->
      <div class="mobile-toolbar">
        <div class="toolbar-left">
          <n-button text @click="closeModal" class="close-btn">
            <template #icon>
              <n-icon size="20">
                <ArrowBackOutline />
              </n-icon>
            </template>
          </n-button>
          <div class="file-info" v-if="fileName">
            <span class="file-name">{{ fileName }}</span>
            <span class="file-index" v-if="fileNames.length > 1">
              ({{ currentIndex }}/{{ fileNames.length }})
            </span>
          </div>
        </div>
        <div class="toolbar-right">
          <!-- 下载按钮 -->
          <n-button 
            text 
            @click="downloadFile" 
            v-if="canDownload"
            class="action-btn"
          >
            <template #icon>
              <n-icon size="18">
                <DownloadOutline />
              </n-icon>
            </template>
          </n-button>
          <!-- 更多操作 -->
          <n-dropdown 
            :options="moreOptions" 
            @select="handleMoreAction"
            v-if="moreOptions.length > 0"
          >
            <n-button text class="action-btn">
              <template #icon>
                <n-icon size="18">
                  <EllipsisVerticalOutline />
                </n-icon>
              </template>
            </n-button>
          </n-dropdown>
        </div>
      </div>

      <!-- 加载提示 -->
      <div v-if="loading" class="loading-overlay">
        <n-spin size="large" />
        <p class="loading-text">加载中...</p>
      </div>

      <!-- 内容区域 -->
      <div class="content-area" v-show="!loading">
        <!-- PDF预览 -->
        <iframe 
          :src="pdfUrl" 
          class="preview-content pdf-content" 
          v-if="curType === 'pdf' && showPre" 
        />
        
        <!-- Excel预览 -->
        <vue-office-excel
          :src="excelUrl"
          class="preview-content excel-content"
          @rendered="renderedHandler"
          @error="errorHandler"
          v-if="curType === 'xlsx'"
          :options="excelOptions"
        />
        
        <!-- 图片预览 -->
        <div class="image-container" v-if="curType === 'image'">
          <div class="image-wrapper" ref="imageWrapperRef">
            <img 
              :src="imageUrl" 
              ref="imageRef" 
              class="preview-image"
              @load="onImageLoad"
              @error="onImageError"
            />
          </div>
          <!-- 图片操作工具栏 -->
          <div class="image-toolbar">
            <n-button-group>
              <n-button @click="rotateImage(-90)" size="small" type="primary">
                <template #icon>
                  <n-icon><RotateLeftOutlined /></n-icon>
                </template>
              </n-button>
              <n-button @click="rotateImage(90)" size="small" type="primary">
                <template #icon>
                  <n-icon><RotateRightOutlined /></n-icon> 
                </template>
              </n-button>
              <n-button @click="zoomImage(0.8)" size="small" type="primary">
                <template #icon>
                  <n-icon><ZoomOutOutlined /></n-icon>
                </template>
              </n-button>
              <n-button @click="zoomImage(1.2)" size="small" type="primary">
                <template #icon>
                  <n-icon><ZoomInOutlined /></n-icon>
                </template>
              </n-button>
              <n-button @click="resetImage" size="small" type="primary">
                <template #icon>
                  <n-icon><RefreshOutline /></n-icon>
                </template>
              </n-button>
            </n-button-group>
          </div>
        </div>

        <!-- 多图片轮播 -->
        <div class="carousel-container" v-if="curType === 'images'">
          <n-carousel 
            show-arrow 
            :show-dots="true"
            dot-placement="bottom"
            @update:current-index="onCarouselChange"
          >
            <img 
              v-for="(item, index) in urls" 
              :key="index"
              :src="item" 
              class="carousel-image" 
            />
          </n-carousel>
        </div>

        <!-- 多文件轮播 -->
        <div class="multi-file-container" v-if="curType === 'multi'">
          <n-carousel
            show-arrow
            :show-dots="false"
            @update:current-index="switchCarousel"
          >
            <div 
              v-for="(file, index) in multiFiles" 
              :key="index"
              class="multi-file-item"
            >
              <iframe 
                :src="file.url" 
                class="preview-content" 
                v-if="file.type === 'pdf'" 
              />
              <div class="image-wrapper" v-else>
                <img :src="file.url" :id="file.id" class="preview-image" />
              </div>
            </div>
          </n-carousel>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted, onUnmounted, watch } from 'vue'
import { IRes, PreviewType } from '@jtypes'
import { download, getOutsideChain } from '@/api/common/common'
import JPGlobal from '@jutil'
import VueOfficeExcel from '@vue-office/excel'
import '@vue-office/excel/lib/index.css'
import {
  ArrowBackOutline,
  DownloadOutline,
  EllipsisVerticalOutline,
  RefreshOutline,
  RemoveOutline,
  AddOutline,
} from '@vicons/ionicons5'

import { RotateLeftOutlined, RotateRightOutlined,ZoomInOutlined,ZoomOutOutlined } from '@ant-design/icons-vue'
// RotateLeftOutlined (向左旋转)
// RotateRightOutlined (向右旋转)
// Props定义
interface Props {
  show: boolean
  url?: string
  ossPath?: string
  ossPathName?: string
  urls?: string[]
  file?: File
  type?: PreviewType
  bucket?: string
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  type: PreviewType.PDF,
  urls: () => [],
})

// Emits定义
const emit = defineEmits<{
  'update:show': [value: boolean]
}>()

// 响应式数据
const loading = ref(false)
const showPre = ref(false)
const pdfUrl = ref('')
const fileName = ref('')
const imageUrl = ref('')
const currentIndex = ref(1)
const curType = ref<PreviewType>()
const fileNames = ref<string[]>([])
const multiFiles = ref<any[]>([])
const excelUrl = ref<string | ArrayBuffer>('')
const imageWrapperRef = ref<HTMLElement>()
const imageRef = ref<HTMLImageElement>()

// 图片变换状态
const imageTransform = ref({
  scale: 1,
  rotate: 0,
  translateX: 0,
  translateY: 0,
})

// Excel配置
const excelOptions = {
  xls: false,
  minColLength: 0,
  minRowLength: 0,
  widthOffset: 0,
  heightOffset: 5,
}

// 计算属性
const canDownload = computed(() => {
  return curType.value === 'xlsx' || curType.value === 'xls' || curType.value === 'image'
})

const moreOptions = computed(() => {
  const options = []
  if (curType.value === 'image') {
    options.push(
      { label: '打印', key: 'print' },
      { label: '分享', key: 'share' }
    )
  }
  return options
})

// PDF相关常量
const pdfJSPrefix = 'pdfjs/web/viewer.html?file='

// 方法定义
const closeModal = () => {
  emit('update:show', false)
}

const downloadFile = async () => {
  if (curType.value === 'xlsx' || curType.value === 'xls') {
    const path = props.ossPath?.split(',')[0]
    const name = props.ossPathName?.split(',')[0]
    if (path && name) {
      try {
        const res = await download({ bucket: props.bucket, path })
        JPGlobal.download(res.data, name)
      } catch (error) {
        console.error('下载失败:', error)
      }
    }
  } else if (curType.value === 'image' && imageUrl.value) {
    try {
      const blob = await fetch(imageUrl.value).then(r => r.blob())
      JPGlobal.download(blob, fileName.value || 'image')
    } catch (error) {
      console.error('下载失败:', error)
    }
  }
}

const handleMoreAction = (key: string) => {
  switch (key) {
    case 'print':
      window.print()
      break
    case 'share':
      // 实现分享功能
      if (navigator.share && imageUrl.value) {
        navigator.share({
          title: fileName.value || '图片',
          url: imageUrl.value,
        })
      }
      break
  }
}

// 图片操作方法
const rotateImage = (angle: number) => {
  imageTransform.value.rotate += angle
  updateImageTransform()
}

const zoomImage = (factor: number) => {
  const newScale = imageTransform.value.scale * factor
  if (newScale >= 0.3 && newScale <= 3) {
    imageTransform.value.scale = newScale
    updateImageTransform()
  }
}

const resetImage = () => {
  imageTransform.value = {
    scale: 1,
    rotate: 0,
    translateX: 0,
    translateY: 0,
  }
  updateImageTransform()
}

const updateImageTransform = () => {
  if (imageRef.value) {
    const { scale, rotate, translateX, translateY } = imageTransform.value
    imageRef.value.style.transform = 
      `scale(${scale}) rotate(${rotate}deg) translate(${translateX}px, ${translateY}px)`
  }
}

// 事件处理
const onImageLoad = () => {
  // 图片加载完成后的处理
  setupImageGestures()
}

const onImageError = () => {
  console.error('图片加载失败')
}

const onCarouselChange = (index: number) => {
  currentIndex.value = index + 1
}

const switchCarousel = (index: number) => {
  currentIndex.value = index + 1
  const file = multiFiles.value[index]
  if (file) {
    fileName.value = file.name || ''
  }
}

const renderedHandler = () => {
  // Excel渲染完成
}

const errorHandler = () => {
  // Excel渲染错误
}

// 手势处理
const setupImageGestures = () => {
  if (!imageWrapperRef.value || !imageRef.value) return

  let startDistance = 0
  let startScale = 1
  let startX = 0
  let startY = 0
  let startTranslateX = 0
  let startTranslateY = 0

  // 触摸开始
  const handleTouchStart = (e: TouchEvent) => {
    if (e.touches.length === 2) {
      // 双指缩放
      const touch1 = e.touches[0]
      const touch2 = e.touches[1]
      startDistance = Math.sqrt(
        Math.pow(touch2.clientX - touch1.clientX, 2) +
        Math.pow(touch2.clientY - touch1.clientY, 2)
      )
      startScale = imageTransform.value.scale
    } else if (e.touches.length === 1) {
      // 单指拖拽
      startX = e.touches[0].clientX
      startY = e.touches[0].clientY
      startTranslateX = imageTransform.value.translateX
      startTranslateY = imageTransform.value.translateY
    }
  }

  // 触摸移动
  const handleTouchMove = (e: TouchEvent) => {
    e.preventDefault()

    if (e.touches.length === 2) {
      // 双指缩放
      const touch1 = e.touches[0]
      const touch2 = e.touches[1]
      const currentDistance = Math.sqrt(
        Math.pow(touch2.clientX - touch1.clientX, 2) +
        Math.pow(touch2.clientY - touch1.clientY, 2)
      )
      const scale = startScale * (currentDistance / startDistance)
      if (scale >= 0.3 && scale <= 3) {
        imageTransform.value.scale = scale
        updateImageTransform()
      }
    } else if (e.touches.length === 1 && imageTransform.value.scale > 1) {
      // 单指拖拽（仅在放大状态下）
      const deltaX = e.touches[0].clientX - startX
      const deltaY = e.touches[0].clientY - startY
      imageTransform.value.translateX = startTranslateX + deltaX
      imageTransform.value.translateY = startTranslateY + deltaY
      updateImageTransform()
    }
  }

  // 绑定事件
  imageWrapperRef.value.addEventListener('touchstart', handleTouchStart, { passive: false })
  imageWrapperRef.value.addEventListener('touchmove', handleTouchMove, { passive: false })
}

// 生成PDF URL
const genPdf = async (url?: string) => {
  if (props.file) {
    fileName.value = props.file.name
    fileNames.value = [props.file.name]
    pdfUrl.value = pdfJSPrefix + URL.createObjectURL(props.file)
  } else if (url || props.url) {
    try {
      const response = await fetch(url || props.url!)
      const blob = await response.blob()
      pdfUrl.value = pdfJSPrefix + URL.createObjectURL(blob)
    } catch (error) {
      console.error('PDF加载失败:', error)
    }
  }
}

// 生成Excel URL
const genXLSX = (url?: string) => {
  if (props.file) {
    const fileReader = new FileReader()
    fileReader.readAsArrayBuffer(props.file)
    fileReader.onload = () => {
      excelUrl.value = fileReader.result!
    }
  } else if (url) {
    excelUrl.value = url
  }
}

// 初始化预览
const initPreview = async () => {
  loading.value = true
  showPre.value = true
  curType.value = props.type

  try {
    if (props.ossPath && props.bucket) {
      await handleOssFiles()
    } else {
      await handleDirectFiles()
    }
  } catch (error) {
    console.error('预览初始化失败:', error)
  } finally {
    loading.value = false
  }
}

// 处理OSS文件
const handleOssFiles = async () => {
  const paths = props.ossPath!.split(',')
  const names = props.ossPathName?.split(',')
  fileNames.value = names?.filter(name => name) as string[]

  const urls: string[] = []
  for (const path of paths) {
    if (path) {
      const res: IRes = await getOutsideChain({ path, bucket: props.bucket })
      urls.push(res.data)
    }
  }

  if (urls.length === 1) {
    await handleSingleFile(urls[0], names?.[0])
  } else if (urls.length > 1) {
    await handleMultipleFiles(urls, names)
  }
}

// 处理直接文件
const handleDirectFiles = async () => {
  switch (props.type) {
    case PreviewType.PDF:
      await genPdf()
      break
    case PreviewType.IMAGE:
      imageUrl.value = props.url || ''
      if (props.file) {
        fileName.value = props.file.name
        fileNames.value = [props.file.name]
        imageUrl.value = URL.createObjectURL(props.file)
      }
      await nextTick(() => {
        setupImageGestures()
      })
      break
    case PreviewType.IMAGES:
      // 多图片处理
      break
  }
}

// 处理单个文件
const handleSingleFile = async (url: string, name?: string) => {
  if (name) fileName.value = name

  const lowerUrl = url.toLowerCase()
  if (lowerUrl.includes('.pdf') || lowerUrl.includes('.ofd')) {
    curType.value = PreviewType.PDF
    await genPdf(url)
  } else if (lowerUrl.includes('.xlsx')) {
    curType.value = PreviewType.XLSX
    excelOptions.xls = false
    genXLSX(url)
  } else if (lowerUrl.includes('.xls')) {
    curType.value = PreviewType.XLSX
    excelOptions.xls = true
    genXLSX(url)
  } else {
    curType.value = PreviewType.IMAGE
    imageUrl.value = url
    await nextTick(() => {
      setupImageGestures()
    })
  }
}

// 处理多个文件
const handleMultipleFiles = async (urls: string[], names?: string[]) => {
  curType.value = PreviewType.MULTI
  if (names?.[0]) fileName.value = names[0]

  const files = urls.map((url, index) => {
    const lowerUrl = url.toLowerCase()
    return {
      url,
      type: lowerUrl.includes('.pdf') ? PreviewType.PDF : PreviewType.IMAGE,
      id: `image${index}`,
      name: names?.[index] || '',
    }
  })

  multiFiles.value = files
}

// 生命周期
onMounted(() => {
  if (props.show) {
    initPreview()
  }
})

// 监听show变化
watch(() => props.show, (newVal) => {
  if (newVal) {
    initPreview()
  } else {
    // 清理资源
    if (pdfUrl.value.startsWith('blob:')) {
      URL.revokeObjectURL(pdfUrl.value)
    }
    if (typeof imageUrl.value === 'string' && imageUrl.value.startsWith('blob:')) {
      URL.revokeObjectURL(imageUrl.value)
    }
  }
})

onUnmounted(() => {
  // 清理资源
  if (pdfUrl.value.startsWith('blob:')) {
    URL.revokeObjectURL(pdfUrl.value)
  }
  if (typeof imageUrl.value === 'string' && imageUrl.value.startsWith('blob:')) {
    URL.revokeObjectURL(imageUrl.value)
  }
})
</script>

<style scoped>
@reference "tailwindcss";

.mobile-preview-container {
  margin-top: 40px;
  @apply fixed inset-0 z-50 ;
}

.mobile-preview-wrapper {
  @apply h-full flex flex-col;
}

.mobile-preview-wrapper.loading {
  @apply items-center justify-center;
}

/* 顶部工具栏 */
.mobile-toolbar {
  @apply flex items-center justify-between px-4 py-3 bg-black/90 text-white;
  @apply fixed  left-0 right-0 z-50;
  top :40px;
  /* 适配安全区域，参考移动端布局 */
  padding-top: calc(12px + env(safe-area-inset-top));
  min-height: 40px;
  max-height: 40px;
}

.toolbar-left {
  @apply flex items-center flex-1;
}

.close-btn {
  @apply text-white mr-3;
}

.file-info {
}

.file-name {
  @apply text-sm font-medium  w-full;
}

.file-index {
  @apply text-xs text-gray-300;
}

.toolbar-right {
  @apply flex items-center gap-2;
}

.action-btn {
  @apply text-white;
}

/* 加载状态 */
.loading-overlay {
  @apply flex flex-col items-center justify-center h-full text-white;
}

.loading-text {
  @apply mt-4 text-sm;
}

/* 内容区域 */
.content-area {
  @apply flex-1 relative overflow-hidden;
  /* 避开顶部工具栏，参考移动端布局的margin-top计算 */
  margin-top: calc(60px + env(safe-area-inset-top));
}

.preview-content {
  @apply w-full h-full border-0;
}

.pdf-content {
  @apply bg-white;
}

.excel-content {
  @apply bg-white;
}

/* 图片预览 */
.image-container {
  @apply h-full flex flex-col;
}

.image-wrapper {
  @apply flex-1 flex items-center justify-center overflow-hidden;
  @apply bg-black;
}

.preview-image {
  @apply max-w-full max-h-full object-contain;
  @apply transition-transform duration-200;
  @apply touch-none; /* 禁用默认触摸行为 */
}

.image-toolbar {
  @apply absolute left-1/2 transform -translate-x-1/2;
  @apply rounded-lg p-2;
  /* 适配底部安全区域 */
  margin-bottom: 50px;
}

/* 轮播容器 */
.carousel-container {
  @apply h-full;
}

.carousel-image {
  @apply w-full h-full object-contain;
}

.multi-file-container {
  @apply h-full;
}

.multi-file-item {
  @apply h-full;
}

/* 响应式适配 */
@media (max-width: 480px) {
  .mobile-toolbar {
    @apply px-3;
    padding-top: calc(8px + env(safe-area-inset-top));
    min-height: 56px;
  }

  .file-name {
    @apply max-w-32;
  }

  .content-area {
    margin-top: calc(56px + env(safe-area-inset-top));
  }

  .image-toolbar {
    bottom: calc(12px + env(safe-area-inset-bottom));
  }
}

/* 安全区域适配 */
.safe-top {
  padding-top: env(safe-area-inset-top);
}

.safe-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .pdf-content,
  .excel-content {
    @apply bg-gray-900;
  }
}
</style>
