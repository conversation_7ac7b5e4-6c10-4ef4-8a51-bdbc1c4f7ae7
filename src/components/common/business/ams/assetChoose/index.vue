<template>
  <n-space justify="space-between">
    <div style="font-weight: bold">
      {{ `已选择资产数量：${modalData.length}` }}
    </div>
    <n-form-item :label="btnLabel" label-placement="left" v-tooltip="btnTooltip">
      <n-button type="info" @click="() => (showModal = true)">点击选择</n-button>
    </n-form-item>
  </n-space>
  <div class="mb-2">
    <j-n-data-table
      :columns="modalColumns"
      :data="modalData"
      size="small"
      striped
      :max-height="440"
      :bordered="true"
      :single-line="true"
    />
  </div>
  <n-modal
    v-model:show="showModal"
    :close-on-esc="false"
    :mask-closable="false"
    style="height: 700px; width: 80%"
    :z-index="11"
  >
    <n-card
      :content-style="{ width: '100%', height: '100%' }"
      title="选择要报废的资产"
      closable
      :close-on-esc="false"
      :on-close="() => (showModal = false)"
    >
      <template #header>
        <div style="display: flex; align-items: center">
          <n-popover trigger="hover">
            <template #trigger>
              <j-icon
                name="back"
                :width="22"
                :height="22"
                style="margin-right: 10px; cursor: pointer"
                @click="() => (showModal = false)"
              />
            </template>
            <span>返回</span>
          </n-popover>
          <span>
            {{ '选择要报废的资产' }}
          </span>
        </div>
      </template>
      <div style="height: 100%">
        <amsDetail
          v-model:checkedKey="checkedKeys"
          :extendParam="extendParam"
          :multiple="true"
          :customerQueryFn="customerQueryFn"
        />
      </div>

      <template #footer>
        <div style="display: flex; flex-direction: row-reverse">
          <n-button type="primary" @click="saveData">确定</n-button>
        </div>
      </template>
    </n-card>
  </n-modal>
</template>
<script setup lang="ts">
  import { h, reactive, ref } from 'vue'
  import { CRUDColumnInterface } from '@/types/comps/crud'
  import { ContainerValueType, IRes } from '@jtypes'
  import {
    queryAmsProperty,
    queryNotInIsScrapApplicationSuccess,
    queryNotInTransferAndAllocProperty,
  } from '@/api/ams/amsProperty/amsProperty'
  import { NPopover } from 'naive-ui'
  import { Icon as JIcon } from '@jcomponents'
  import JPGlobal from '@jutil'

  const props = defineProps({
    value: {
      type: Array<String>,
    },
    extendColumns: {
      type: Array<any>,
    },
    modalData: {
      type: Array<any>,
      default: () => [],
    },
    queryNotInIsScrapApplicationSuccess: {
      type: Boolean,
      default: false,
    },
    queryNotInTransferAndAllocProperty: {
      type: Boolean,
      default: false,
    },
  })

  const customerQueryFn = computed(() => {
    if (props.queryNotInIsScrapApplicationSuccess) {
      return queryNotInIsScrapApplicationSuccess
    }
    return queryNotInTransferAndAllocProperty
  })
  const extendParam = {
    isCanc: '0',
  }

  const emit = defineEmits(['update:value', 'select'])
  const queryForm = ref({})
  const showModal = ref(false)
  const checkedKeys = ref([])
  const modalData = ref<Array<any>>(props.modalData) // 选择的资产详情
  const modalColumns = reactive<Array<CRUDColumnInterface>>([
    { title: '序号', key: 'index', width: 80, render: (row: any, index: number) => h('span', index + 1) },
    { title: '固定资产码', key: 'faCode' },
    { title: '资产名称', key: 'assetName' },
    {
      title: '资产类别',
      key: 'assetTypeName',
    },
    {
      title: '新分类',
      key: 'assetTypeNName',
    },
    {
      title: '管理科室',
      key: 'deptName',
    },
    {
      title: '使用科室',
      key: 'deptUseName',
    },
    {
      title: '存放位置',
      key: 'storageAreaName',
      render: (row: any) => {
        const label = row.storageAreaName || row.storageArea
        return h('span', label)
      },
    },
    {
      title: '保存地点',
      key: 'storageLocation',
    },
    {
      title: '资产状态',
      key: 'assetStatus',
      type: ContainerValueType.SELECT,
      selection: JPGlobal.getDictByType('ASSET_STATUS'),
      dictType: 'ASSET_STATUS',
    },
    { title: '资产型号', key: 'assetMol' },
    ...props.extendColumns,
  ]) as any
  const saveData = () => {
    if (checkedKeys.value.length > 0) {
      queryAmsProperty({
        ids: checkedKeys.value,
        pageNum: 1,
        pageSize: 999999,
      }).then((res: IRes) => {
        if (res.data.records.length > 0) {
          res.data.records.forEach((item: any) => {
            if (modalData.value.findIndex((d: any) => item.faCode == d.faCode) == -1) {
              modalData.value.push(item)
            }
          })
        }
        emit('update:value', modalData.value)
        emit('select', modalData.value)

        showModal.value = false
        checkedKeys.value = []
      })
    } else {
      window.$message.error('请选择需要操作的资产')
    }
  }

  const getFaCode = () => {
    let result: Array<any> = []
    modalData.value.forEach((item: any) => {
      result.push(item.faCode)
    })
    return result
  }

  const btnLabel = computed(() => {
    if (props.queryNotInIsScrapApplicationSuccess) {
      return '选择报废资产'
    } else if (props.queryNotInTransferAndAllocProperty) {
      return '选择未转移划拨的资产'
    }
    return '选择资产'
  })

  const btnTooltip = computed(() => {
    if (props.queryNotInIsScrapApplicationSuccess) {
      return '不在转移和划拨流程中\n并且未报废申请成功的的资产）'
    } else if (props.queryNotInTransferAndAllocProperty) {
      return '未转移划拨的资产'
    }
    return '选择资产'
  })
</script>

<script lang="ts">
  import amsDetail from '@/views/modules/ams/amsChange/amsDetail/index.vue'
  import { Files } from '@element-plus/icons-vue'

  export default {
    name: 'index',
    components: { amsDetail },
  }
</script>
<style scoped></style>
