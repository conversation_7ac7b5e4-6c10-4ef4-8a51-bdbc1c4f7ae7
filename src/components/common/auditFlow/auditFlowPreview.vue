<template>
  <!-- 预览 -->
  <j-modal
    :title="audit ? auditTitle : auditProgressTitle"
    v-model:show="showPreview"
    :width="vertical ? '30%' : '90%'"
    :show-btn="false"
    @changeModel="change"
  >
    <template #content>
      <!-- 签名 -->
      <j-modal title="签名" v-model:show="showSign" width="600px" height="200px" :show-btn="false">
        <template #content>
          <j-sign :canvas-width="620" @done="signDone" />
        </template>
      </j-modal>

      <!-- 附件 -->
      <j-upload
        :show-button="false"
        v-model:show="showUpload"
        acceptProp=".pdf,.png"
        :maxAmount="10"
        @afterUpload="afterUpload"
      />

      <div>
        <slot name="otherForm" />
      </div>

      <!-- 拓展的表单 -->
      <n-form
        :model="extForm"
        :rules="extRules"
        label-placement="left"
        v-if="extFormItems && extFormItems.length > 0"
        ref="extFormRef"
      >
        <n-form-item v-for="(item, idx) in extFormItems" :key="idx" :label="String(item.title)" :path="item.key">
          <j-form-item :item="item" :action-form="extForm" />
        </n-form-item>
      </n-form>

      <div style="width: 100%; display: flex; flex-direction: row">
        <slot name="others" />
      </div>

      <!-- 提醒 -->
      <n-alert title="审核提醒" :type="tipType" v-if="!see && tip">
        <span v-html="audit ? tip : '由业务功能生成'"></span>
        <div style="width: 100%; display: flex; flex-direction: row-reverse">
          <slot name="files" />
        </div>
      </n-alert>

      <n-scrollbar style="max-height: 600px" x-scrollable>
        <!-- 步骤 -->
        <n-steps
          :current="(current as number)"
          :status="currentStatus as any"
          :vertical="vertical"
          style="padding: 20px; white-space: nowrap; box-sizing: border-box"
        >
          <n-step v-for="(item, idx) in curSteps" :key="idx" :style="{ width: vertical ? '100%' : '300px' }">
            <template #title>
              <div style="display: flex; flex-direction: column">
                <n-ellipsis style="max-width: 220px">
                  {{
                    `${item.title ?? ''} ${item.data.detail?.dscr ? '(' : ''}${item.data.detail?.dscr}${
                      item.data.detail?.dscr ? ')' : ''
                    }`
                  }}
                  <template #tooltip>
                    <div style="text-align: center">
                      {{
                        `${item.title} ${item.data.detail?.dscr ? '(' : ''}${item.data.detail?.dscr}${
                          item.data.detail?.dscr ? ')' : ''
                        }`
                      }}
                    </div>
                  </template>
                </n-ellipsis>
                <div style="margin-top: 10px">
                  <!-- 签名 -->
                  <!--                  <n-tag type="info" size="small" style="margin-left: 5px" v-if="item.sign">已签名</n-tag>-->
                  <!--                  <n-popover trigger="hover" v-if="item.sign">-->
                  <!--                    <template #trigger>-->
                  <!--                      <n-tag type="info" size="small" style="margin-left: 5px">已签名</n-tag>-->
                  <!--                    </template>-->
                  <!--                    <div style="width: 300px; height: 200px">-->
                  <!--                      <n-image :width="300" :height="200" :src="item.sign" :preview-disabled="true" />-->
                  <!--                    </div>-->
                  <!--                  </n-popover>-->

                  <!-- 附件 -->
                  <n-popselect
                    size="medium"
                    :on-update:value="changeAttachmentOption"
                    :options="item.attachmentPreviewOptions!"
                    v-if="item.attachment"
                    :render-label="renderAttachmentLabel"
                  >
                    <n-tag type="info" size="small" style="margin-left: 5px">已上传附件</n-tag>
                  </n-popselect>

                  <!-- 附件预览 -->
                  <j-preview
                    v-model:show="showAttachmentPreview"
                    :file="curAttachmentStepOption.value"
                    :url="curAttachmentStepOption.value"
                    :type="curAttachmentStepOption.type"
                    style="z-index: 2100"
                  />
                </div>
              </div>
            </template>
            <!-- 审核备注 -->
            <div v-if="item.data.detail?.chkRemarks">领导批示:</div>
            <n-ellipsis style="max-width: 160px">{{ item.data.detail?.chkRemarks }} </n-ellipsis>

            <!-- 签名/附件 -->
            <div class="step-tags" v-if="!see">
              <!-- 签名 -->
              <n-tag
                size="small"
                type="error"
                style="cursor: pointer"
                v-if="item.data?.sign === '1' && current === item.key + 1"
                @click="sign(item)"
                >{{ !item.sign ? '点击签名' : '重新签名' }}</n-tag
              >

              <!-- 上传附件 -->
              <n-tag
                size="small"
                type="error"
                style="cursor: pointer; margin-left: 5px"
                v-if="item.data?.attachment === '1' && current === item.key + 1"
                @click="attachment(item)"
                >{{ !item.attachment ? '点击上传附件' : '重新上传附件' }}</n-tag
              >
            </div>

            <!-- 审核时间 -->
            <div :style="{ justifyContent: item.data.detail?.chkTime ? 'space-between' : 'flex-end' }" class="sign">
              <n-popover v-if="item.data.detail?.chkTime">
                <template #trigger>
                  <n-tag size="small">{{ item.data.detail?.chkTime }}</n-tag>
                </template>
                审核时间
              </n-popover>

              <!-- 审核人 -->
              <!--              <n-image :width="120" :height="60" :src="('@/assets/images/a.png')" :preview-disabled="true" />-->
              <div v-if="item.data.detail?.chkTime">
                <template v-if="item.data.detail.sign && item.data.detail.sign.includes('|Split|')">
                  <div v-for="url in item.data.detail.sign.split('|Split|')" :key="url">
                    <n-image
                      :width="120"
                      :height="50"
                      :src="JPGlobal?.getRealOCUrl(url) || url"
                      :preview-disabled="true"
                    />
                    <!-- <component :is="item.titleTag" v-if="item.titleTag && !item.sign"></component> -->
                  </div>
                </template>
                <template v-else>
                  <n-image :width="120" :height="60" :src="item.data.detail.sign" :preview-disabled="true" />
                  <component :is="item.titleTag" v-if="item.titleTag && !item.sign"></component>
                </template>
              </div>
              <div v-else>
                <n-image :width="120" :height="60" :src="item.sign" :preview-disabled="true" v-if="item.sign" />
                <component :is="item.titleTag" v-if="item.titleTag && !item.sign"></component>
              </div>
            </div>
          </n-step>
        </n-steps>
      </n-scrollbar>
      <!-- 审核备注 -->
      <n-form :model="auditForm" label-placement="left" class="audit-space" v-if="!see">
        <n-form-item label="审核备注" path="remarks">
          <n-input v-model:value="auditForm.remarks" type="textarea" placeholder="最多200字..." maxlength="200" />
        </n-form-item>
      </n-form>

      <!-- 按钮 -->
      <div class="audit-buttons" v-if="!see && !stateError && isChk">
        <n-button type="error" @click="auditConfirm(false)">不同意</n-button>
        <n-button type="info" @click="auditConfirm(true)">同意</n-button>
      </div>
    </template>
  </j-modal>
</template>
<script lang="ts">
  import { computed, defineComponent, h, onMounted, PropType, ref, toRaw, watch } from 'vue'
  import { NPopover, NTag, SelectOption } from 'naive-ui'
  import { AuditProgress, ContainerValueType, CRUDColumnInterface, Option, Step } from '@jtypes'
  import JPGlobal from '@jutil'
  import { useUserStore } from '@/store'

  export default defineComponent({
    props: {
      show: {
        type: Boolean,
        default: false,
      },
      steps: {
        type: Array,
        default: () => [],
      },
      // 是否查看
      see: {
        type: Boolean,
        default: false,
      },
      // 审核提醒
      tip: {
        type: String,
      },
      //model Title
      auditTitle: {
        type: String,
        default: '审核',
      },
      auditProgressTitle: {
        type: String,
        default: '审核进度预览',
      },
      // 审核标识
      audit: {
        type: Boolean,
        default: false,
      },
      // 是否垂直显示
      vertical: {
        type: Boolean,
        default: false,
      },
      // 添加的表单数据
      extFormItems: {
        type: Array as PropType<CRUDColumnInterface[]>,
      },
    },
    setup(props, ctx) {
      const extFormRef = ref()
      const userStore = useUserStore()
      // onMounted(() => {
      //   userStore.getUserInfo.hrmUser.empCode ==
      // })
      let data = {
        isChk: ref(false),
        extForm: ref({}),
        extRules: ref<any>({}),
        curStep: ref<Step>(),
        curSignStep: ref<Step>(),
        curAttachmentStep: ref<Step>(),
        curAttachmentStepOption: ref<SelectOption | any>({}),
        auditForm: ref({
          remarks: '同意',
        }),
        auditTip: ref(''),
        current: ref(1),
        stateError: ref(false),
        showSign: ref(false),
        showPreview: ref(false),
        showUpload: ref(false),
        showAttachmentPreview: ref(false),
        finally: false,
        currentStatus: computed(() => {
          if (data.stateError.value) {
            return 'error'
          } else if (data.current.value > props.steps?.length!) {
            return 'finish'
          }
          return 'process'
        }),
        tipType: computed(() => {
          if (data.stateError.value) {
            return 'error'
          } else if (data.current.value > props.steps?.length!) {
            return 'success'
          }
          return 'info'
        }),
        curSteps: ref<Step[]>([]),
      }
      let methods = {
        change: () => {
          data.curStep.value = undefined
          data.curSignStep.value = undefined
          ctx.emit('update:show', data.showPreview.value)
        },
        changeCurrent: (current: number) => {
          // data.auditTip.value = (props.steps as Step[])[current - 1].data?.desc as string
        },
        // 审核确认
        auditConfirm: async (flag: boolean) => {
          const execute = async () => {
            if (props.audit) {
              let detail = data.curStep.value?.data?.detail
              // 审核预览时
              let d: AuditProgress = {
                id: detail.id,
                chkRemarks: data.auditForm.value.remarks,
                chkState: flag ? '1' : '0',
                finally: data.finally ? '1' : '0',
                messageId: detail.messageId ?? -1,
                auditBchno: detail.bchno,
              }
              if (flag) {
                if (data.curStep.value?.data?.attachment === '1' && data.curAttachmentStep.value === undefined) {
                  window.$message.warning('请上传附件')
                  return
                }
                d.attFiles = toRaw(data.curStep.value?.attachment) as File[]
                // if (data.curStep.value?.data?.sign === '1' && data.curSignStep.value === undefined) {
                //   window.$message.warning('请签名')
                //   return
                // }
                // let sign = JPGlobal.base64ToFile(data.curStep.value?.sign!) as File
                // if (sign) {
                //   d.signFile = sign
                // }

                if (data.curStep.value?.data?.sign === '1') {
                  if (data.curSignStep?.value?.sign) {
                    // await getFileFromUrl(data.curSignStep?.value?.sign).then(res => {
                    //   d.signFile = new File(
                    //     [res],
                    //     (userStore.getUserInfo.nickname ? userStore.getUserInfo.nickname : '签名') + '.png',
                    //     {
                    //       type: 'image/png',
                    //     }
                    //   )
                    // })

                    d.signUrl = data.curSignStep?.value?.sign
                  } else {
                    window.$message.warning('请签名')
                    return
                  }
                }
              } else {
                if (data.curStep.value?.data?.sign === '1') {
                  if (data.curSignStep?.value?.sign) {
                    d.signUrl = data.curSignStep?.value?.sign
                  } else {
                    window.$message.warning('请签名')
                    return
                  }
                }
                data.stateError.value = true
              }
              if (!data.auditForm.value.remarks) {
                window.$message.warning('请填写审核备注')
                return
              }
              let formData = new FormData()
              for (const key in d) {
                if (key === 'attFiles') {
                  d[key]?.forEach((file: File, idx: number) => {
                    formData.append('attFiles', file)
                  })
                } else {
                  formData.append(key, d[key])
                }
              }
              formData.append('extFormData', JSON.stringify(data.extForm.value))
              ctx.emit('auditConfirm', formData)
            } else {
              // 配置预览时
              if (flag) {
                data.current.value = data.current.value + 1
                if (data.current.value > props.steps?.length! + 1) {
                  data.current.value = 1
                }
                data.stateError.value = false
                data.auditForm.value.remarks = ''
              } else {
                data.stateError.value = true
              }
              if (!data.auditForm.value.remarks) {
                window.$message.warning('请填写审核备注')
                return
              }
              if (data.current.value <= props.steps?.length!) {
                methods.changeCurrent(data.current.value)
              }
            }
          }
          if (props.extFormItems && props.extFormItems.length > 0) {
            extFormRef.value.validate(async (errors: any) => {
              if (!errors) {
                await execute()
              }
            })
          } else {
            await execute()
          }
        },
        // 点击签名
        sign: (item: Step) => {
          // 发送验证码到手机
          // addVerifyCode({}).then(res => {
          //   window.$message.success('验证码已发送到手机')
          //   data.curSignStep.value = item
          //   data.showSign.value = true
          // })
          data.curSignStep.value = item
          data.showSign.value = true
        },
        // 签名完成(手动签名生成的图片)
        // signDone: (base64: string) => {
        //   if (data.curSignStep.value) data.curSignStep.value.sign = base64
        //   data.showSign.value = false
        // },
        signDone: (signPath: string) => {
          if (data.curSignStep.value) data.curSignStep.value.sign = JPGlobal.getRealOCUrl(signPath)
          data.showSign.value = false
        },
        // 点击附件
        attachment: (item: Step) => {
          data.curAttachmentStep.value = item
          data.showUpload.value = true
        },
        // 点击确认
        afterUpload: (files: File[]) => {
          if (data.curAttachmentStep.value) {
            data.curAttachmentStep.value.attachment = files
            let options: any[] = []
            files.forEach(file => {
              options.push({
                label: file.name,
                value: file.name.toLowerCase().includes('.pdf') ? (file as any) : URL.createObjectURL(file),
                type: file.name.toLowerCase().includes('.pdf') ? 'pdf' : 'image',
              })
            })
            data.curAttachmentStep.value.attachmentPreviewOptions = options
          }
        },
        // 渲染附件label
        renderAttachmentLabel: (option: SelectOption) => {
          return h(NTag, { size: 'small', style: { cursor: 'pointer' } }, () => option.label)
        },
        changeAttachmentOption(val: string, option: Option) {
          data.showAttachmentPreview.value = true
          data.curAttachmentStepOption.value = option
        },
        // 获取触发事件
        getTrigger: (type: any) => {
          if ([ContainerValueType.SELECT, ContainerValueType.TREE_SELECT].includes(type)) {
            return ['change', 'blur']
          } else {
            return ['change', 'input']
          }
        },
        // 获取列类型
        getColumnType: (column: CRUDColumnInterface) => {
          if (column.multiple === true) {
            return 'array'
          }
          if (column.dataType) {
            return column.dataType
          }
          return 'string'
        },
      }

      watch(
        () => props.show,
        show => {
          if (show) {
            methods.changeCurrent(data.current.value)
          }
          data.showPreview.value = show
        }
      )

      watch(
        () => props.steps,
        (steps: any) => {
          let csteps = [...steps]
          if (steps) {
            csteps.sort((a: any, b: any) => {
              if (a.data.detail && b.data.detail) {
                return a.data.detail.chkSeq - b.data.detail.chkSeq
              }
              return 1
            })
            let find = false
            for (let step of csteps) {
              if (step.data.detail) {
                // 审核失败
                if (step.data.detail.chkState === '0' && step.data.detail.chkTime !== undefined) {
                  data.current.value = step.data.detail.chkSeq
                  data.curStep.value = step
                  data.stateError.value = true
                  find = true
                  break
                }
                // 当前审核
                if (step.data.detail.chkState === '0' && step.data.detail.chkTime === undefined) {
                  data.current.value = step.data.detail.chkSeq
                  data.curStep.value = step
                  data.stateError.value = false
                  find = true
                  break
                }
              }
            }
            if (!find) {
              data.current.value = csteps.length + 1
            }
            if (data.current.value === csteps.length) {
              data.finally = true
            } else {
              data.finally = false
            }
          }
          data.curSteps.value = csteps
          //判断最新的审批人是否为自己
          if (csteps.length > 0) {
            data.isChk.value =
              csteps[Math.min(csteps.length - 1, data.current.value - 1)].data.chker ==
              userStore?.getUserInfo?.hrmUser.empCode
          }
        },
        { deep: true }
      )

      onMounted(() => {
        if (props.extFormItems) {
          props.extFormItems.forEach(item => {
            let placeholder = item.placeholder
            if (!placeholder) {
              placeholder =
                ([ContainerValueType.SELECT.toString(), ContainerValueType.TREE_SELECT.toString()].includes(
                  item.type?.toString()!
                )
                  ? '请选择'
                  : '请输入') + (item.realTitle !== undefined ? item.realTitle : item.title)
            }

            data.extRules.value[item.key] = {
              required: item.required,
              type: methods.getColumnType(item),
              trigger: methods.getTrigger(item.type),
              message: placeholder,
            }
          })
        }
      })
      return {
        JPGlobal,
        extFormRef,
        ...data,
        ...methods,
      }
    },
  })
</script>

<style scoped>
  .step-tags {
    width: 100%;
    display: flex;
    justify-content: flex-start;
  }
  .audit-buttons {
    width: 100%;
    display: flex;
    justify-content: flex-end;
  }
  .sign {
    display: flex;

    align-items: center;
    margin-top: 5px;
  }
  :deep(.n-button) {
    margin-left: 0.5rem;
  }
</style>
