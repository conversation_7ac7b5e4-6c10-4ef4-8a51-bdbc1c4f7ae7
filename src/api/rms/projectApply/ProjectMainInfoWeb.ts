import request from '@/utils/request'
import { ContentTypes, RequestType } from '@/types/enums/enums'
import { IPageRes, IRes } from '@jtypes'
import JPGlobal from '@jutil'
import { RmsProjectVO } from '@/types/modules/rms/project-management'
import { ProjectConclusion } from '@/types/modules/rms/project-conclusion'
import {ProjectDeclare} from "src/types/modules/rms/project-declare";
import {RmsProject, RmsProjectPerformanceGoal} from "@/types/modules/rms/entity/inex.ts";
import {ProjectChange} from "@/types/modules/rms/project-change";

/**
 * 新增
 * @param param
 */
export function addRmsProjectMainInfo(param: Object) {
  return request({
    url: 'rms/rmsProjectMainInfo/save',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 删除
 * @param param
 */
export function deleteRmsProjectMainInfo(param: Object) {
  return request({
    url: 'rms/rmsProjectMainInfo/delete',
    method: RequestType.DEL,
    data: param,
  })
}

/**
 * 修改
 * @param param
 */
export function updateRmsProjectMainInfo(param: Object) {
  return request({
    url: 'rms/rmsProjectMainInfo/update',
    method: RequestType.PUT,
    data: param,
  })
}

/**
 * 分页查询
 * @param param
 */
export function pageQueryRmsProjectMainInfo(param: Object): Promise<IPageRes<RmsProjectVO[]>> {
  return request({
    url: 'rms/rmsProjectMainInfo/pageList',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 查询
 * @param param
 */
export function queryRmsProjectMainInfo(param: Object): Promise<IRes<RmsProjectVO[]>> {
  return request({
    url: 'rms/rmsProjectMainInfo/list',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 查询项目详情
 * @param param
 */
export function queryProjectDetails(param: Object): Promise<IRes<RmsProjectVO>> {
  return request({
    url: 'rms/rmsProjectMainInfo/queryProjectDetails',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 补充项目信息（项目筹划阶段，保存草稿）
 * @param param
 */
export function projectInfoSupply(param: Object) {
  return request({
    url: 'rms/rmsProjectMainInfo/projectInfoSupply',
    method: RequestType.PUT,
    data: param,
  })
}

/**
 * 生成申报书
 * @param param 查询参数
 */
export function generateDeclaration(param: Object) {
  return request({
    url: 'rms/rmsProjectMainInfo/generateDeclaration',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 项目申报
 * @param param
 */
export function projectApply(param: Object): Promise<IRes> {
  return request({
    url: 'rms/rmsProjectMainInfo/projectApply',
    method: RequestType.PUT,
    data: param,
  })
}

// 定义递归函数处理需要转换的对象
const objectToFormData: (obj: Object, formData?: FormData, prefix?: string) => FormData = (
  obj: Object,
  formData: FormData = new FormData(),
  prefix: string = ''
) => {
  for (let key in obj) {
    // hasOwnProperty 是 JavaScript 中的一个内置方法，用于判断一个对象是否拥有某个指定的属性，而不是继承自原型链的属性
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      let value = obj[key]

      if (value instanceof Array) {
        JPGlobal.addArrToFormData(formData, value, key, 'att')
        value.forEach((item: any, index: number) => {
          formData.delete(`${key}[${index}].attFiles`)
          // formData.append(`${key}[${index}]`, item)
          // objectToFormData(item, formData, prefix + key + '[' + index + '].')
        })
      } else if (value instanceof Object) {
        objectToFormData(value, formData, prefix + key + '.')
      } else {
        formData.append(prefix + key, value)
      }
    }
  }
  return formData
}

/**
 * 立项确认（同意/驳回）
 * @param param
 */
export function approvalConfirm(param: Object): Promise<IRes> {
  return request({
    url: 'rms/rmsProjectMainInfo/approvalConfirm',
    method: RequestType.PUT,
    data: objectToFormData(param),
    contentType: ContentTypes.FORM_DATA,
  })
}

/**
 * 生成科研经费报销任务
 * @param param
 */
export function genFundingReimbursementTask(param: Object): Promise<IRes> {
  return request({
    url: 'rms/rmsProjectMainInfo/genFundingReimbursementTask',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 项目申报流程
 * @param param
 */
export function projectDeclare(param: Object): Promise<IRes> {
  return request({
    url: 'rms/rmsProjectMainInfo/projectDeclare',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 立项确认
 * @param param
 */
export const projectConfirm = (param: ProjectDeclare.ProjectConfirmForm): Promise<IRes> => {
  return request({
    url: 'rms/rmsProjectMainInfo/project/confirm',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 伦理审查批件提交
 * @param param
 */
export const submitEthics = (param: ProjectDeclare.EthicsForm): Promise<IRes> => {
  return request({
    url: 'rms/rmsProjectMainInfo/ethics/submit',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 任务书提交
 * @param param
 */
export const submitBrief = (param: Object): Promise<IRes> => {
  return request({
    url: 'rms/rmsProjectMainInfo/brief/submit',
    method: RequestType.POST,
    data: param,
  })
}

export const changeDeferrable = (param: Pick<RmsProject, 'id' | 'deferAllowed'>): Promise<IRes> => {
  return request({
    url: 'rms/rmsProjectMainInfo/deferrable',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 改变其他附件是否必填状态
 * @param param
 */
export const changeRequireAccConOtherAtt = (param: Pick<RmsProject, 'id' | 'requireAccConOtherAtt'>): Promise<IRes> => {
  return request({
    url: 'rms/rmsProjectMainInfo/requireAccConOtherAtt',
    method: RequestType.POST,
    data: param,
  })
}

export const stagingAcceptance = (param: ProjectConclusion.GenericForm): Promise<IRes> => {
  const data: Pick<RmsProject, 'id' | 'acceptanceSummary' | 'acceptanceAttachments' | 'acceptanceOtherAttachments'> & {
    rmsProjectPerformanceGoalData: Pick<RmsProjectPerformanceGoal, 'id' | 'conclusionAttachments'>[]
  } = {
    id: param.id,
    acceptanceSummary: param.summary,
    acceptanceAttachments: param.application,
    rmsProjectPerformanceGoalData: param.performanceAttachments,
    acceptanceOtherAttachments: param.otherAttachments,
  }
  return request({
    url: 'rms/rmsProjectMainInfo/acceptance/staging',
    method: RequestType.POST,
    data,
  })
}

/**
 * 项目验收提交
 * @param param 参数
 * @return Promise<IRes> 返回结果
 */
export const submitAcceptance = (param: ProjectConclusion.GenericForm): Promise<IRes> => {
  const data: Pick<RmsProject, 'id' | 'acceptanceSummary' | 'acceptanceAttachments' | 'acceptanceOtherAttachments'> & {
    rmsProjectPerformanceGoalData: Pick<RmsProjectPerformanceGoal, 'id' | 'conclusionAttachments'>[]
  } = {
    id: param.id,
    acceptanceSummary: param.summary,
    acceptanceAttachments: param.application,
    rmsProjectPerformanceGoalData: param.performanceAttachments,
    acceptanceOtherAttachments: param.otherAttachments,
  }
  return request({
    url: 'rms/rmsProjectMainInfo/acceptance/submit',
    method: RequestType.POST,
    data,
  })
}

/**
 * 项目结题暂存
 * @param param 参数
 * @return Promise<IRes> 返回结果
 */
export const stagingConclusion = (param: ProjectConclusion.GenericForm): Promise<IRes> => {
  const data: Pick<RmsProject, 'id' | 'acceptanceSummary' | 'acceptanceAttachments' | 'acceptanceOtherAttachments'> & {
    rmsProjectPerformanceGoalData: Pick<RmsProjectPerformanceGoal, 'id' | 'conclusionAttachments'>[]
  } = {
    id: param.id,
    acceptanceSummary: param.summary,
    acceptanceAttachments: param.application,
    rmsProjectPerformanceGoalData: param.performanceAttachments,
    acceptanceOtherAttachments: param.otherAttachments,
  }
  return request({
    url: 'rms/rmsProjectMainInfo/conclusion/staging',
    method: RequestType.POST,
    data,
  })
}

/**
 * 项目结题提交
 * @param param 参数
 * @return Promise<IRes> 返回结果
 */
export const submitConclusion = (param: ProjectConclusion.GenericForm): Promise<IRes> => {
  const data: Pick<RmsProject, 'id' | 'conclusionSummary' | 'conclusionAttachments' | 'conclusionOtherAttachments'> & {
    rmsProjectPerformanceGoalData: Pick<RmsProjectPerformanceGoal, 'id' | 'conclusionAttachments'>[]
  } = {
    id: param.id,
    conclusionSummary: param.summary,
    conclusionAttachments: param.application,
    rmsProjectPerformanceGoalData: param.performanceAttachments,
    conclusionOtherAttachments: param.otherAttachments,
  }
  return request({
    url: 'rms/rmsProjectMainInfo/conclusion/submit',
    method: RequestType.POST,
    data,
  })
}

/**
 * 查询项目负责人原始信息
 * @param param
 */
export const queryOriginalLeaderInfo = (param: Pick<RmsProject, 'id'>): Promise<IRes<ProjectChange.Leader.Form>> => {
  return request({
    url: 'rms/rmsProjectMainInfo/original/leader',
    method: RequestType.POST,
    data: param,
  })
}

/**
 * 查询项目成员原始信息
 * @param param
 */
export const queryOriginalMembersInfo = (param: Pick<RmsProject, 'id'>): Promise<IRes<ProjectChange.Member.Form>> => {
  return request({
    url: 'rms/rmsProjectMainInfo/original/members',
    method: RequestType.POST,
    data: param,
  })
}
