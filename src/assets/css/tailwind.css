@import "tailwindcss";

/* 移动端安全区域工具类 */
@layer utilities {
  /* 顶部安全区域 */
  .pt-safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .mt-safe-top {
    margin-top: env(safe-area-inset-top);
  }

  /* 底部安全区域 */
  .pb-safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .mb-safe-bottom {
    margin-bottom: env(safe-area-inset-bottom);
  }

  /* 左右安全区域 */
  .pl-safe-left {
    padding-left: env(safe-area-inset-left);
  }

  .pr-safe-right {
    padding-right: env(safe-area-inset-right);
  }

  /* 组合安全区域 */
  .p-safe {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  /* 移动端底栏高度 - 包含安全区域 */
  .h-mobile-bottom-nav {
    height: calc(70px + env(safe-area-inset-bottom));
  }

  /* 移动端顶栏高度 - 包含安全区域 */
  .h-mobile-header {
    height: calc(60px + env(safe-area-inset-top));
  }

  /* 移动端内容区域高度 - 避开顶部和底部 */
  .h-mobile-content {
    height: calc(100vh - 60px - 70px - env(safe-area-inset-top) - env(safe-area-inset-bottom));
  }

  /* 移动端内容区域最小高度 */
  .min-h-mobile-content {
    min-height: calc(100vh - 60px - 70px - env(safe-area-inset-top) - env(safe-area-inset-bottom));
  }

  /* 移动端分页区域底部间距 - 避免与底栏重叠 */
  .mb-mobile-nav {
    margin-bottom: calc(70px + env(safe-area-inset-bottom));
  }

  .pb-mobile-nav {
    padding-bottom: calc(70px + env(safe-area-inset-bottom));
  }

  /* 移动端容器内容区域 - 为分页留出空间 */
  .mobile-container-content {
    padding-bottom: calc(140px + env(safe-area-inset-bottom)); /* 分页高度120px + 底栏70px + 安全区域 */
  }

  /* 移动端分页固定定位 */
  .mobile-pagination-fixed {
    position: fixed;
    bottom: calc(70px + env(safe-area-inset-bottom));
    left: 0;
    right: 0;
    z-index: 999;
  }

  /* 移动端title组件sticky定位优化 */
  .mobile-sticky-title-container {
    /* 确保容器支持sticky定位 */
    position: relative;
    overflow-y: visible !important; /* 关键：允许sticky元素突破容器边界 */
  }

  .mobile-sticky-title {
    position: sticky !important;
    top: calc(52px + env(safe-area-inset-top)) !important; /* 顶部导航栏高度 + 安全区域 */
    z-index: 100 !important; /* 确保在内容之上，但低于导航栏 */
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px) saturate(180%) !important;
    -webkit-backdrop-filter: blur(20px) saturate(180%) !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08) !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    width: 100% !important;
    box-sizing: border-box !important;
    margin: 0 !important;
    transform: translateZ(0) !important; /* 硬件加速 */
    will-change: transform !important; /* 优化性能 */
  }
}