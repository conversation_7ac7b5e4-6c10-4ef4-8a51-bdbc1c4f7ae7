import { ContainerValueType, DataType } from '@/types/enums/enums'
import JPGlobal from '@jutil'
import { CRUDColumnInterface, FormInterface } from '@jtypes'
import { h } from 'vue'
import { useAmsDict } from '@/types/modules/ams/amsClassify'

// let propertyOptions: Record<string, [any]>

export const renderSelectOptions = (row: any, index: number, title: string, key: string, renderText = false) => {
  try {
    const amsDict = useAmsDict()

    const label = amsDict.dict[title].find(item => item.value == row[key]).label
    if (renderText) {
      return label
    }
    return h('span', label)
  } catch (e) {
    if (renderText) {
      return row[key]
    }
    return h('span', row[key])
  }
}
const property: Array<FormInterface> = [
  {
    title: '固定资产码',
    key: 'faCode',
    realKey: 'fa_code',
    type: ContainerValueType.INPUT,
    disabled: true,
    placeholder: '系统自动生成',
  },
  {
    title: '资产编码',
    key: 'assetCode',
    realKey: 'asset_code',
    type: ContainerValueType.INPUT,
    disabled: true,
    placeholder: '系统自动生成',
  },
  { title: '资产名称', key: 'assetName', realKey: 'asset_name', type: ContainerValueType.INPUT },
  {
    title: '资金性质',
    key: 'fundingNature',
    realKey: 'funding_ature',
    type: ContainerValueType.SELECT,
    optionKey: '资金性质',
  },

  { title: '序列号', key: 'serialNumber', realKey: 'serial_number', type: ContainerValueType.INPUT },
  {
    title: '资产类型',
    key: 'assetType',
    realKey: 'asset_type',
    type: ContainerValueType.TREE_SELECT,
    optionKey: '资产类型',
    treeProp: { label: 'assetTypeName', key: 'assetTypeCode' },
    clearable: true,
    // readOnly: true,
  },
  {
    title: '设备类别',
    key: 'assetCategory',
    realKey: 'asset_category',
    type: ContainerValueType.TREE_SELECT,
    optionKey: '设备类别',
    treeProp: { label: 'ccmName', key: 'ccmCode' },
  },
  {
    title: '资产类型(新分类)',
    key: 'assetTypeN',
    realKey: 'asset_type_n',
    type: ContainerValueType.TREE_SELECT,
    optionKey: '资产类型(新分类)',
    treeProp: { label: 'assetTypeName', key: 'assetTypeCode' },
  },
  // { title: '类型代码', key: 'assetType', realKey: 'asset_type', type: ContainerValueType.SELECT },

  // { title: '新分类代码', key: 'assetTypeN', type: ContainerValueType.SELECT },
  {
    title: '取得方式',
    key: 'obtainWay',
    realKey: 'obtain_way',
    type: ContainerValueType.SELECT,
    optionKey: '取得方式',
  },
  {
    title: '价值类型',
    key: 'valueType',
    realKey: 'value_type',
    type: ContainerValueType.SELECT,
    optionKey: '价值类型',
  },
  { title: '取得日期', key: 'obtainDate', realKey: 'obtain_date', type: ContainerValueType.DATE },
  {
    title: '资产状态',
    key: 'assetStatus',
    realKey: 'asset_status',
    type: ContainerValueType.SELECT,
    // optionKey: '资产状态',
    dictType: 'ASSET_STATUS',
  },
  { title: '资产型号', key: 'assetMol', realKey: 'asset_mol', type: ContainerValueType.INPUT },
  { title: '资产原值', key: 'assetNav', realKey: 'asset_nav', type: ContainerValueType.NUMBER, min: 0 },
  {
    title: '增加方式',
    key: 'incrWay',
    realKey: 'incr_way',
    type: ContainerValueType.SELECT,
    optionKey: '增加方式',
  },
  { title: '资金来源', key: 'source', realKey: 'source', type: ContainerValueType.SELECT, optionKey: '资金来源' },
  // { title: '财政补助金', key: 'financial_assistance_funds', realKey: 'financial_assistance_funds', type: ContainerValueType.NUMBER},
  // { title: '自有资金', key: 'own_funds', realKey: 'own_funds', type: ContainerValueType.NUMBER },
  { title: '折旧方式', key: 'dm', realKey: 'dm', type: ContainerValueType.SELECT, optionKey: '折旧方式' },
  { title: '月折旧率', key: 'deprratMon', realKey: 'deprrat_mon', type: ContainerValueType.NUMBER },
  { title: '月折旧额', key: 'deprMon', realKey: 'depr_mon', type: ContainerValueType.NUMBER },
  { title: '残值率', key: 'resr', realKey: 'resr', type: ContainerValueType.NUMBER },
  { title: '预计使用年限', key: 'ul', realKey: 'ul', type: ContainerValueType.NUMBER },
  { title: '累计折旧', key: 'dep', realKey: 'dep', type: ContainerValueType.NUMBER },
  { title: '资产净值', key: 'nbv', realKey: 'nbv', type: ContainerValueType.NUMBER, min: 0 },
  { title: '资产残值', key: 'rv', realKey: 'rv', type: ContainerValueType.NUMBER },
  { title: '通用名', key: 'genname', realKey: 'genname', type: ContainerValueType.INPUT },
  { title: '规格', key: 'spec', realKey: 'spec', type: ContainerValueType.INPUT },
  { title: '其他编号', key: 'otherRef', realKey: 'other_ref', type: ContainerValueType.INPUT },
  { title: '供应商', key: 'supplier', realKey: 'supplier', type: ContainerValueType.INPUT },
  { title: '供应商联系人', key: 'supplierConer', realKey: 'supplier_coner', type: ContainerValueType.INPUT },
  { title: '供应商电话', key: 'supplierTel', realKey: 'supplier_tel', type: ContainerValueType.INPUT },
  { title: '售后工程师', key: 'fse', realKey: 'fse', type: ContainerValueType.INPUT },
  { title: '售后电话', key: 'cs', realKey: 'cs', type: ContainerValueType.INPUT },
  { title: '品牌', key: 'brand', realKey: 'brand', type: ContainerValueType.INPUT },
  { title: '生产厂家', key: 'manufacturer', realKey: 'manufacturer', type: ContainerValueType.INPUT },
  { title: '计量单位', key: 'unit', realKey: 'unit', type: ContainerValueType.INPUT },
  { title: '配置描述', key: 'describe', realKey: 'describe', type: ContainerValueType.INPUT },
  {
    title: '使用科室',
    key: 'deptUse',
    realKey: 'dept_use',
    type: ContainerValueType.ORG,
    // checkStrategy: 'all',
    multiple: false,
    showInOneLine: true,
  },
  {
    title: '管理科室',
    key: 'dept',
    realKey: 'dept',
    type: ContainerValueType.ORG,
    // checkStrategy: 'all',
  },
  {
    title: '存放位置',
    key: 'storageArea',
    realKey: 'storage_area',
    optionKey: 'storageArea',
    type: ContainerValueType.TREE_SELECT,
    treeProp: { label: 'storageArea', key: 'storageAreaCode' },
  },
  {
    title: '保存地点',
    key: 'storageLocation',
    realKey: 'storage_location',
    type: ContainerValueType.INPUT,
  },
  { title: '位置', key: 'location', realKey: 'location', type: ContainerValueType.INPUT },
  { title: '建议使用年限', key: 'exp', realKey: 'exp', type: ContainerValueType.NUMBER },
  { title: '已使用年限', key: 'usedExp', realKey: 'used_exp', type: ContainerValueType.NUMBER, min: 0, disabled: true },
  { title: '采购单价', key: 'unitPrice', realKey: 'unit_price', type: ContainerValueType.NUMBER },
  { title: '维保状态', key: 'amStatus', realKey: 'am_status', type: ContainerValueType.INPUT },
  { title: '脱保日期', key: 'oowDate', realKey: 'oow_date', type: ContainerValueType.DATE },
  { title: '注册证号', key: 'rcn', realKey: 'rcn', type: ContainerValueType.INPUT },
  { title: '出厂编号', key: 'sn', realKey: 'sn', type: ContainerValueType.INPUT },
  { title: '保修期限', key: 'wp', realKey: 'wp', type: ContainerValueType.DATE },
  { title: '合同编号', key: 'cntrCode', realKey: 'cntr_code', type: ContainerValueType.INPUT },
  { title: '合同名称', key: 'cntrName', realKey: 'cntr_name', type: ContainerValueType.INPUT },
  { title: '发票号', key: 'invono', realKey: 'invono', type: ContainerValueType.INPUT },
  { title: '原厂保修', key: 'oemw', realKey: 'oemw', type: ContainerValueType.INPUT },
  { title: '采购日期', key: 'purcDate', realKey: 'purc_date', type: ContainerValueType.DATE },
  { title: '安装日期', key: 'instDate', realKey: 'inst_date', type: ContainerValueType.DATE },
  {
    title: '开始使用日期',
    key: 'openingDate',
    realKey: 'opening_date',
    type: ContainerValueType.DATE,
  },
  { title: '入库日期', key: 'stoinDate', realKey: 'stoin_date', type: ContainerValueType.DATE },
  { title: '验收日期', key: 'acpDate', realKey: 'acp_date', type: ContainerValueType.DATE },
  { title: '出库日期', key: 'stooutDate', realKey: 'stoout_date', type: ContainerValueType.DATE },
  { title: '设备用途', key: 'assetUsed', realKey: 'asset_used', type: ContainerValueType.INPUT },
  { title: '财政拨款', key: 'gf', realKey: 'gf', type: ContainerValueType.NUMBER },
  { title: '非财政拨款', key: 'unGf', realKey: 'un_gf', type: ContainerValueType.NUMBER },
  { title: '自筹金额', key: 'oop', realKey: 'oop', type: ContainerValueType.NUMBER },
  {
    title: '科研经费',
    key: 'researchFunding',
    realKey: 'research_funding',
    type: ContainerValueType.INPUT,
  },
  { title: '教学经费', key: 'tef', realKey: 'tef', type: ContainerValueType.INPUT },
  {
    title: '是否成本核算',
    key: 'adjact',
    realKey: 'adjact',
    type: ContainerValueType.SELECT,
    dictType: 'YES_OR_NO',
    options: JPGlobal.getDictByType('YES_OR_NO'),
  },
  {
    title: '是否进口',
    key: 'imp',
    realKey: 'imp',
    type: ContainerValueType.SELECT,
    dictType: 'YES_OR_NO',
    options: JPGlobal.getDictByType('YES_OR_NO'),
  },
  {
    title: '二维码',
    key: 'uid',
    realKey: 'uid',
    type: ContainerValueType.INPUT,
    disabled: true,
    placeholder: '系统自动生成',
  },
  { title: '第三方编号', key: 'tpn', realKey: 'tpn', type: ContainerValueType.INPUT },
  { title: '在保类型', key: 'wts', realKey: 'wts', type: ContainerValueType.INPUT },
  { title: '买保开始日期', key: 'ipd', realKey: 'ipd', type: ContainerValueType.INPUT },
  { title: '买保结束日期', key: 'epd', realKey: 'epd', type: ContainerValueType.INPUT },
  { title: '生产日期', key: 'pd', realKey: 'pd', type: ContainerValueType.INPUT },
  { title: '负责人', key: 'resper', realKey: 'resper', type: ContainerValueType.INPUT },
  { title: '便用状态', key: 'us', realKey: 'us', type: ContainerValueType.INPUT },
  { title: '财务分类', key: 'fc', realKey: 'fc', type: ContainerValueType.INPUT },
  { title: '医械分类', key: 'mdr', realKey: 'mdr', type: ContainerValueType.INPUT },
  { title: '风险等级', key: 'rsl', realKey: 'rsl', type: ContainerValueType.INPUT },
  { title: '管理分类', key: 'cm', realKey: 'cm', type: ContainerValueType.INPUT },
  {
    title: '生命支持',
    key: 'ls',
    realKey: 'ls',
    type: ContainerValueType.SELECT,
    dictType: 'YES_OR_NO',
    options: JPGlobal.getDictByType('YES_OR_NO'),
  },
  {
    title: '是否应急设备',
    key: 'ed',
    realKey: 'ed',
    type: ContainerValueType.SELECT,
    dictType: 'YES_OR_NO',
    options: JPGlobal.getDictByType('YES_OR_NO'),
  },
  { title: '中医诊疗', key: 'tcm', realKey: 'tcm', type: ContainerValueType.INPUT },
  {
    title: '检验设备',
    key: 'te',
    realKey: 'te',
    type: ContainerValueType.SELECT,
    dictType: 'YES_OR_NO',
    options: JPGlobal.getDictByType('YES_OR_NO'),
  },
  {
    title: '特种设备',
    key: 'se',
    realKey: 'se',
    type: ContainerValueType.SELECT,
    dictType: 'YES_OR_NO',
    options: JPGlobal.getDictByType('YES_OR_NO'),
  },
  {
    title: '辐射设备',
    key: 'rs',
    realKey: 'rs',
    type: ContainerValueType.SELECT,
    dictType: 'YES_OR_NO',
    options: JPGlobal.getDictByType('YES_OR_NO'),
  },
  { title: '辅助分类', key: 'ac', realKey: 'ac', type: ContainerValueType.INPUT },
  { title: '附属设备', key: 'ae', realKey: 'ae', type: ContainerValueType.INPUT },
  { title: '出厂日期', key: 'manuDate', realKey: 'manu_date', type: ContainerValueType.DATE },
  {
    title: '是否计量设备',
    key: 'me',
    realKey: 'me',
    type: ContainerValueType.SELECT,
    dictType: 'YES_OR_NO',
    options: JPGlobal.getDictByType('YES_OR_NO'),
  },
  { title: '计量编码', key: 'mc', realKey: 'mc', type: ContainerValueType.INPUT },
  { title: '数量', key: 'cnt', realKey: 'cnt', type: ContainerValueType.NUMBER, readOnly: true },
  { title: '建筑面积(㎡)', key: 'area', realKey: 'area', type: ContainerValueType.NUMBER },
  {
    title: '其中:取暖面积(㎡)',
    key: 'heatingArea',
    realKey: 'heating_area',
    type: ContainerValueType.NUMBER,
  },
  {
    title: '其中:危房面积(㎡)',
    key: 'dangerArea',
    realKey: 'danger_area',
    type: ContainerValueType.NUMBER,
  },
  {
    title: '发证日期',
    key: 'issucertDate',
    realKey: 'issucert_date',
    type: ContainerValueType.DATE,
  },
  {
    title: '权属证明',
    key: 'proofOfTitle',
    realKey: 'proof_of_title',
    type: ContainerValueType.INPUT,
  },
  {
    title: '权属证书编号',
    key: 'certificateNum',
    realKey: 'certificate_num',
    type: ContainerValueType.INPUT,
  },
  { title: '权属年限', key: 'tenure', realKey: 'tenure', type: ContainerValueType.NUMBER },
  { title: '坐落位置', key: 'loc', realKey: 'loc', type: ContainerValueType.INPUT },
  {
    title: '土地使用权类型',
    key: 'landUseRightsType',
    realKey: 'land_use_rights_type',
    type: ContainerValueType.INPUT,
  },
  {
    title: '产权形式',
    key: 'propertyForm',
    realKey: 'property_form',
    type: ContainerValueType.SELECT,
    optionKey: '产权形式',
  },
  { title: '已使用月份', key: 'usedMon', realKey: 'used_mon', type: ContainerValueType.NUMBER },
  { title: '车牌照号', key: 'lpn', realKey: 'lpn', type: ContainerValueType.INPUT },
  { title: '减少方式', key: 'redcWay', realKey: 'redc_way', type: ContainerValueType.SELECT, optionKey: '减少方式' },
  { title: '录入人', key: 'inpter', realKey: 'inpter', type: ContainerValueType.INPUT },
  { title: '录入日期', key: 'inptDate', realKey: 'inpt_date', type: ContainerValueType.DATE },
  {
    title: '是否已编制凭证',
    key: 'isPrepareVouchers',
    realKey: 'is_prepare_vouchers',
    type: ContainerValueType.INPUT,
  },
  { title: '是否已审核', key: 'isChk', realKey: 'is_chk', type: ContainerValueType.INPUT },
  { title: '审核人', key: 'chker', realKey: 'chker', type: ContainerValueType.INPUT },
  { title: '备注', key: 'memo', realKey: 'memo', type: ContainerValueType.INPUT },
  { title: '土地面积(㎡)', key: 'landArea', realKey: 'land_area', type: ContainerValueType.NUMBER },
  { title: '权属证书所有权人', key: 'coOwner', realKey: 'co_owner', type: ContainerValueType.INPUT },
  { title: '土地证载明面积(㎡)', key: 'zkc', realKey: 'zkc', type: ContainerValueType.NUMBER },
  { title: '入账形式', key: 'entryForm', realKey: 'entry_form', type: ContainerValueType.INPUT },
  { title: '土地来源', key: 'landSouc', realKey: 'land_souc', type: ContainerValueType.INPUT },
  { title: '使用方向', key: 'usage', realKey: 'usage', type: ContainerValueType.INPUT },
  {
    title: '权属性质',
    key: 'propertyChar',
    realKey: 'property_char',
    type: ContainerValueType.SELECT,
    optionKey: '权属性质',
  },
  { title: '权属证书发证时间', key: 'coDate', realKey: 'co_date', type: ContainerValueType.DATE },
  {
    title: '权属所有人',
    key: 'propertyOwner',
    realKey: 'property_owner',
    type: ContainerValueType.INPUT,
  },
  {
    title: '建筑结构',
    key: 'buildingStructure',
    realKey: 'building_structure',
    type: ContainerValueType.SELECT,
    optionKey: '建筑结构',
  },
  { title: '使用状况', key: 'use', realKey: 'use', type: ContainerValueType.INPUT },
  { title: '发动机号', key: 'engineNo', realKey: 'engine_no', type: ContainerValueType.INPUT },
  {
    title: '资产大类',
    key: 'assetClassification',
    realKey: 'asset_classification',
    // type: ContainerValueType.INPUT,
    placeholder: '系统自动生成',

    disabled: true,
    type: ContainerValueType.TREE_SELECT,
    optionKey: '资产类型',
    treeProp: { label: 'assetTypeName', key: 'assetTypeCode' },
    readOnly: true,
  },
  {
    title: '土地使用权面积(㎡)',
    key: 'landUsageArea',
    realKey: 'land_usage_area',
    type: ContainerValueType.NUMBER,
  },
  { title: '土地使用权人', key: 'landUser', realKey: 'land_user', type: ContainerValueType.INPUT },
  { title: '地类(用途)', key: 'landUsed', realKey: 'land_used', type: ContainerValueType.INPUT },
  {
    title: '房屋所有权人',
    key: 'ownerOfHouse',
    realKey: 'owner_of_house',
    type: ContainerValueType.INPUT,
  },
  { title: '记账凭证号', key: 'avn', realKey: 'avn', type: ContainerValueType.INPUT },
  { title: '设备用途', key: 'devUsed', realKey: 'dev_used', type: ContainerValueType.INPUT },
  { title: '车辆识别代码', key: 'vin', realKey: 'vin', type: ContainerValueType.INPUT },
  { title: '是否拆分卡片', key: 'isSplit', realKey: 'is_split', type: ContainerValueType.INPUT },
  {
    title: '已注销',
    key: 'isCanc',
    realKey: 'is_canc',

    type: ContainerValueType.SELECT,
    dictType: 'YES_OR_NO',
    options: JPGlobal.getDictByType('YES_OR_NO'),
  },
  { title: '注销人', key: 'cancPsn', realKey: 'canc_psn', type: ContainerValueType.INPUT },
  { title: '注销日期', key: 'cancDate', realKey: 'canc_date', type: ContainerValueType.DATE },
  { title: '证书号', key: 'certNo', realKey: 'cert_no', type: ContainerValueType.INPUT },
  { title: '已折旧月份', key: 'deprM', realKey: 'depr_m', type: ContainerValueType.NUMBER },
  {
    title: '财务入账状态',
    key: 'entryStatus',
    realKey: 'entry_status',
    type: ContainerValueType.INPUT,
  },
  { title: '财务入账日期', key: 'entryDate', realKey: 'entry_date', type: ContainerValueType.DATE },
  { title: '采购组织形式', key: 'pof', realKey: 'pof', type: ContainerValueType.INPUT },
  { title: '会计凭证号', key: 'jvn', realKey: 'jvn', type: ContainerValueType.INPUT },
  {
    title: '竣工日期',
    key: 'completedDate',
    realKey: 'completed_date',
    type: ContainerValueType.INPUT,
  },
  { title: '设计用途', key: 'dsgnUsed', realKey: 'dsgn_used', type: ContainerValueType.INPUT },
  { title: '持证人', key: 'holder', realKey: 'holder', type: ContainerValueType.INPUT },
  { title: '折旧状态', key: 'deprStatus', realKey: 'depr_status', type: ContainerValueType.INPUT },
  { title: '闲置面积(㎡)', key: 'idleArea', realKey: 'idle_area', type: ContainerValueType.NUMBER },
  { title: '自用面积(㎡)', key: 'selfArea', realKey: 'self_area', type: ContainerValueType.NUMBER },
  { title: '出借面积(㎡)', key: 'lendArea', realKey: 'lend_area', type: ContainerValueType.NUMBER },
  { title: '出租面积(㎡)', key: 'hireArea', realKey: 'hire_area', type: ContainerValueType.NUMBER },
  { title: '其他面积(㎡)', key: 'otherArea', realKey: 'other_area', type: ContainerValueType.NUMBER },
  {
    title: '行驶证登记日期',
    key: 'regisDate',
    realKey: 'regis_date',
    type: ContainerValueType.DATE,
  },
  {
    title: '车辆产地',
    key: 'vehicleOrigin',
    realKey: 'vehicle_origin',
    type: ContainerValueType.INPUT,
  },
  {
    title: '车辆品牌',
    key: 'vehicleBrand',
    realKey: 'vehicle_brand',
    type: ContainerValueType.INPUT,
  },
  { title: '排气量', key: 'displacement', realKey: 'displacement', type: ContainerValueType.INPUT },
  {
    title: '编制情况',
    key: 'compilationStatus',
    realKey: 'compilation_status',
    type: ContainerValueType.INPUT,
  },
  {
    title: '车辆用途',
    key: 'vehicleUsage',
    realKey: 'vehicle_usage',
    type: ContainerValueType.INPUT,
  },
  { title: '出版社', key: 'press', realKey: 'press', type: ContainerValueType.INPUT },
  {
    title: '出版日期',
    key: 'publicationDate',
    realKey: 'publication_date',
    type: ContainerValueType.DATE,
  },
  { title: '档案号', key: 'fileNo', realKey: 'file_no', type: ContainerValueType.INPUT },
  {
    title: '保存年限(档案)',
    key: 'storagePeriod',
    realKey: 'storage_period',
    type: ContainerValueType.INPUT,
  },
  { title: '文物等级', key: 'cocr', realKey: 'cocr', type: ContainerValueType.INPUT },
  { title: '来源地(文物)', key: 'origin', realKey: 'origin', type: ContainerValueType.INPUT },
  {
    title: '藏品年代',
    key: 'collectionAge',
    realKey: 'collection_age',
    type: ContainerValueType.INPUT,
  },
  { title: '栽种年龄', key: 'plantingAge', realKey: 'planting_age', type: ContainerValueType.NUMBER },
  {
    title: '栽种年份',
    key: 'plantingYear',
    realKey: 'planting_year',
    type: ContainerValueType.INPUT,
  },
  { title: '纲属科', key: 'genus', realKey: 'genus', type: ContainerValueType.INPUT },
  { title: '产地(动植物)', key: 'producer', realKey: 'producer', type: ContainerValueType.INPUT },
  { title: '分摊面积', key: 'sharedArea', realKey: 'shared_area', type: ContainerValueType.NUMBER },
  {
    title: '独用面积',
    key: 'exclusiveArea',
    realKey: 'exclusive_area',
    type: ContainerValueType.NUMBER,
  },
  { title: '土地级次', key: 'landLevel', realKey: 'land_level', type: ContainerValueType.INPUT },
]
const propertySon: Array<CRUDColumnInterface> = [
  { title: '固定资产码', key: 'faCode', realKey: 'fa_code', type: ContainerValueType.INPUT, width: 110 },
  { title: '资产名称', key: 'assetName', realKey: 'asset_name', type: ContainerValueType.INPUT, width: 150 },
  { title: '资产编码', key: 'assetCode', realKey: 'asset_code', type: ContainerValueType.INPUT, width: 100 },
  { title: '父项固定资产码', key: 'saCode', realKey: 'sa_code', type: ContainerValueType.INPUT, width: 140 },
  { title: '使用科室', key: 'deptUseName', realKey: 'dept_use', type: ContainerValueType.INPUT, width: 100 },
  { title: '管理科室', key: 'deptName', realKey: 'dept', type: ContainerValueType.INPUT, width: 100 },
  {
    title: '存放位置',
    key: 'storageArea',
    realKey: 'storage_area',
    optionKey: 'storageArea',
    type: ContainerValueType.TREE_SELECT,
    treeProp: { label: 'storageArea', key: 'storageAreaCode' },
    width: 100,
  },
  { title: '二维码', key: 'uid', realKey: 'uid', type: ContainerValueType.INPUT, width: 100 },
  { title: '资产型号', key: 'assetMol', realKey: 'asset_mol', type: ContainerValueType.INPUT, width: 100 },
  {
    title: '资产状态',
    key: 'assetStatus',
    realKey: 'asset_status',
    type: ContainerValueType.SELECT,
    // optionKey: '资产状态',
    dictType: 'ASSET_STATUS',
  },
]

const propertyColumn: Array<CRUDColumnInterface> = [
  { title: '固定资产码', key: 'faCode', realKey: 'fa_code', type: ContainerValueType.INPUT, width: 110, fixed: 'left' },
  {
    title: '资产名称',
    key: 'assetName',
    realKey: 'asset_name',
    type: ContainerValueType.INPUT,
    width: 150,
    fixed: 'left',
  },
  {
    title: '通用名',
    key: 'genname',
    realKey: 'genname',
    type: ContainerValueType.INPUT,
    width: 100,
  },
  { title: '资产类型', key: 'assetTypeName', realKey: 'asset_type', type: ContainerValueType.SELECT, width: 100 },
  { title: '类型代码', key: 'assetType', realKey: 'asset_type', type: ContainerValueType.SELECT, width: 100 },
  { title: '资产类型(新分类)', key: 'assetTypeNName', type: ContainerValueType.SELECT, width: 160 },
  { title: '新分类代码', key: 'assetTypeN', type: ContainerValueType.SELECT, width: 160 },
  {
    title: '设备类别',
    key: 'assetCategory',
    realKey: 'asset_category',
    type: ContainerValueType.SELECT,
    width: 100,
    render: (row: any, index: number) => {
      return renderSelectOptions(row, index, '设备类别', 'assetCategory')
    },
  },
  {
    title: '资产状态',
    key: 'assetStatus',
    realKey: 'asset_status',
    type: ContainerValueType.SELECT,
    width: 100,
    dictType: 'ASSET_STATUS',
  },
  { title: '使用科室', key: 'deptUseName', realKey: 'dept_use', type: ContainerValueType.INPUT, width: 100 },
  { title: '管理科室', key: 'deptName', realKey: 'dept', type: ContainerValueType.INPUT, width: 100 },
  {
    title: '存放位置',
    key: 'storageAreaName',
    realKey: 'storageAreaName',
    type: ContainerValueType.INPUT,
    width: 100,
    render: (row: any, index: number) => {
      const label = row.storageAreaName || row.storageArea
      return h('span', label)
    },
  },
  { title: '资金性质', key: 'fundingNature', realKey: 'funding_nature', type: ContainerValueType.INPUT },

  {
    title: '保存地点',
    key: 'storageLocation',
    realKey: 'storage_location',
    optionKey: 'storageArea',
    type: ContainerValueType.INPUT,
    treeProp: { label: 'storageArea', key: 'storageAreaCode' },
    width: 100,
  },
  { title: '资产型号', key: 'assetMol', realKey: 'asset_mol', type: ContainerValueType.INPUT, width: 100 },
  { title: '资产原值', key: 'assetNav', realKey: 'asset_nav', type: ContainerValueType.INPUT, width: 100 },
  { title: '累计折旧', key: 'dep', realKey: 'dep', type: ContainerValueType.INPUT, width: 100 },
  { title: '资产净值', key: 'nbv', realKey: 'nbv', type: ContainerValueType.INPUT, width: 100, min: 0 },
  { title: '资产残值', key: 'rv', realKey: 'rv', type: ContainerValueType.INPUT, width: 100 },
  { title: '已折月份', key: 'deprM', realKey: 'depr_m', type: ContainerValueType.INPUT, width: 100 },
  { title: '规格', key: 'spec', realKey: 'spec', type: ContainerValueType.INPUT, width: 100 },
  {
    title: '增加方式',
    key: 'incrWay',
    realKey: 'incr_way',
    type: ContainerValueType.INPUT,
    width: 100,
    render: (row: any, index: number) => {
      return renderSelectOptions(row, index, '增加方式', 'incrWay')
    },
    realRender: (row: any, index: number) => {
      return renderSelectOptions(row, index, '增加方式', 'incrWay', true)
    },
  },
  {
    title: '资金来源',
    key: 'source',
    realKey: 'source',
    type: ContainerValueType.SELECT,
    width: 100,
    optionKey: '资金来源',
    render: (row: any, index: number) => {
      return renderSelectOptions(row, index, '资金来源', 'source')
    },
    realRender: (row: any, index: number) => {
      return renderSelectOptions(row, index, '资金来源', 'source', true)
    },
  },
  // {
  //   title: '财政补助金',
  //   key: 'financialAssistanceFunds',
  //   realKey: 'source',
  //   type: ContainerValueType.NUMBER,
  //   width: 120,
  // },
  // { title: '自有资金', key: 'ownFunds', realKey: 'source', type: ContainerValueType.NUMBER, width: 100 },
  {
    title: '其他编号',
    key: 'otherRef',
    realKey: 'other_ref',
    type: ContainerValueType.INPUT,
    width: 100,
  },
  { title: '资产编码', key: 'assetCode', realKey: 'asset_code', type: ContainerValueType.INPUT, width: 100 },
  { title: '序列号', key: 'serialNumber', realKey: 'serial_number', type: ContainerValueType.INPUT, width: 100 },
  { title: '供应商', key: 'supplier', realKey: 'supplier', type: ContainerValueType.INPUT, width: 100 },
  {
    title: '供应商联系人',
    key: 'supplierConer',
    realKey: 'supplier_coner',
    type: ContainerValueType.INPUT,
    width: 120,
  },
  {
    title: '供应商电话',
    key: 'supplierTel',
    realKey: 'supplier_tel',
    type: ContainerValueType.INPUT,
    width: 120,
  },
  { title: '售后工程师', key: 'fse', realKey: 'fse', type: ContainerValueType.INPUT, width: 130 },
  { title: '售后电话', key: 'cs', realKey: 'cs', type: ContainerValueType.INPUT, width: 100 },
  { title: '品牌', key: 'brand', realKey: 'brand', type: ContainerValueType.INPUT, width: 100 },
  { title: '生产厂家', key: 'manufacturer', realKey: 'manufacturer', type: ContainerValueType.INPUT, width: 100 },
  { title: '计量单位', key: 'unit', realKey: 'unit', type: ContainerValueType.INPUT, width: 100 },
  { title: '配置描述', key: 'describe', realKey: 'describe', type: ContainerValueType.INPUT, width: 100 },
  { title: '位置', key: 'location', realKey: 'location', type: ContainerValueType.INPUT, width: 100 },
  { title: '建议使用年限', key: 'exp', realKey: 'exp', type: ContainerValueType.INPUT, width: 140 },
  { title: '已使用年限', key: 'usedExp', realKey: 'used_exp', type: ContainerValueType.INPUT, width: 130 },
  { title: '采购单价', key: 'unitPrice', realKey: 'unit_price', type: ContainerValueType.INPUT, width: 100 },
  { title: '折旧方式', key: 'dm', realKey: 'dm', type: ContainerValueType.INPUT, width: 100 },
  { title: '预计使用年限', key: 'ul', realKey: 'ul', type: ContainerValueType.INPUT, width: 140 },
  { title: '维保状态', key: 'amStatus', realKey: 'am_status', type: ContainerValueType.INPUT, width: 100 },
  { title: '脱保日期', key: 'oowDate', realKey: 'oow_date', type: ContainerValueType.INPUT, width: 100 },
  { title: '注册证号', key: 'rcn', realKey: 'rcn', type: ContainerValueType.INPUT, width: 100 },
  { title: '出厂编号', key: 'sn', realKey: 'sn', type: ContainerValueType.INPUT, width: 100 },
  { title: '保修期限', key: 'wp', realKey: 'wp', type: ContainerValueType.INPUT, width: 100 },
  { title: '合同编号', key: 'cntrCode', realKey: 'cntr_code', type: ContainerValueType.INPUT, width: 100 },
  { title: '合同名称', key: 'cntrName', realKey: 'cntr_name', type: ContainerValueType.INPUT, width: 100 },
  { title: '发票号', key: 'invono', realKey: 'invono', type: ContainerValueType.INPUT, width: 100 },
  { title: '原厂保修', key: 'oemw', realKey: 'oemw', type: ContainerValueType.INPUT, width: 100 },
  { title: '采购日期', key: 'purcDate', realKey: 'purc_date', type: ContainerValueType.INPUT, width: 100 },
  { title: '安装日期', key: 'instDate', realKey: 'inst_date', type: ContainerValueType.INPUT, width: 100 },
  {
    title: '开始使用日期',
    key: 'openingDate',
    realKey: 'opening_date',
    type: ContainerValueType.INPUT,
    width: 140,
  },
  { title: '入库日期', key: 'stoinDate', realKey: 'stoin_date', type: ContainerValueType.INPUT, width: 100 },
  { title: '验收日期', key: 'acpDate', realKey: 'acp_date', type: ContainerValueType.INPUT, width: 100 },
  { title: '出库日期', key: 'stooutDate', realKey: 'stoout_date', type: ContainerValueType.INPUT, width: 100 },
  { title: '用途', key: 'assetUsed', realKey: 'asset_used', type: ContainerValueType.INPUT, width: 100 },
  { title: '自筹金额', key: 'oop', realKey: 'oop', type: ContainerValueType.INPUT, width: 100 },
  { title: '财政拨款', key: 'gf', realKey: 'gf', type: ContainerValueType.INPUT, width: 100 },
  {
    title: '科研经费',
    key: 'researchFunding',
    realKey: 'research_funding',

    type: ContainerValueType.INPUT,
    width: 100,
  },
  {
    title: '教学经费',
    key: 'tef',
    realKey: 'tef',
    type: ContainerValueType.INPUT,
    width: 100,
  },
  { title: '是否成本核算', key: 'adjact', realKey: 'adjact', type: ContainerValueType.INPUT, width: 140 },
  { title: '是否进口', key: 'imp', realKey: 'imp', type: ContainerValueType.INPUT, width: 100 },
  { title: '二维码', key: 'uid', realKey: 'uid', type: ContainerValueType.INPUT, width: 100 },
  { title: '第三方编号', key: 'tpn', realKey: 'tpn', type: ContainerValueType.INPUT, width: 130 },
  { title: '在保类型', key: 'wts', realKey: 'wts', type: ContainerValueType.INPUT, width: 100 },
  { title: '买保开始日期', key: 'ipd', realKey: 'ipd', type: ContainerValueType.INPUT, width: 140 },
  { title: '买保结束日期', key: 'epd', realKey: 'epd', type: ContainerValueType.INPUT, width: 140 },
  { title: '生产日期', key: 'pd', realKey: 'pd', type: ContainerValueType.INPUT, width: 100 },
  { title: '负责人', key: 'resper', realKey: 'resper', type: ContainerValueType.INPUT, width: 100 },
  { title: '便用状态', key: 'us', realKey: 'us', type: ContainerValueType.INPUT, width: 100 },
  {
    title: '财务分类',
    key: 'fc',
    realKey: 'fc',
    type: ContainerValueType.INPUT,
    width: 100,
  },
  {
    title: '医械分类',
    key: 'mdr',
    realKey: 'mdr',
    type: ContainerValueType.INPUT,
    width: 100,
  },
  {
    title: '风险等级',
    key: 'rsl',
    realKey: 'rsl',
    type: ContainerValueType.INPUT,
    width: 100,
  },
  { title: '管理分类', key: 'cm', realKey: 'cm', type: ContainerValueType.INPUT, width: 100, tableColumnHidden: true },
  { title: '生命支持', key: 'ls', realKey: 'ls', type: ContainerValueType.INPUT, width: 100, tableColumnHidden: true },
  { title: '应急设备', key: 'ed', realKey: 'ed', type: ContainerValueType.INPUT, width: 200, tableColumnHidden: true },
  {
    title: '中医诊疗',
    key: 'tcm',
    realKey: 'tcm',
    type: ContainerValueType.INPUT,
    width: 100,
  },
  { title: '检验设备', key: 'te', realKey: 'te', type: ContainerValueType.INPUT, width: 100, tableColumnHidden: true },
  {
    title: '特种设备',
    key: 'se',
    realKey: 'se',
    type: ContainerValueType.INPUT,
    width: 100,
    dictType: 'YES_OR_NO',
    options: JPGlobal.getDictByType('YES_OR_NO'),
  },
  {
    title: '辐射设备',
    key: 'rs',
    realKey: 'rs',
    type: ContainerValueType.INPUT,
    width: 100,
    dictType: 'YES_OR_NO',
    options: JPGlobal.getDictByType('YES_OR_NO'),
  },
  { title: '辅助分类', key: 'ac', realKey: 'ac', type: ContainerValueType.INPUT, width: 100 },
  { title: '附属设备', key: 'ae', realKey: 'ae', type: ContainerValueType.INPUT, width: 100 },
  { title: '出厂日期', key: 'manuDate', realKey: 'manu_date', type: ContainerValueType.INPUT, width: 100 },
  {
    title: '是否计量设备',
    key: 'me',
    realKey: 'me',
    type: ContainerValueType.INPUT,
    width: 140,
    dictType: 'YES_OR_NO',
    options: JPGlobal.getDictByType('YES_OR_NO'),
  },
  { title: '计量编码', key: 'mc', realKey: 'mc', type: ContainerValueType.INPUT, width: 100 },
  { title: '数量', key: 'cnt', realKey: 'cnt', type: ContainerValueType.INPUT, width: 100 },
  { title: '残值率', key: 'resr', realKey: 'resr', type: ContainerValueType.INPUT, width: 100 },
  {
    title: '建筑面积',
    key: 'area',
    realKey: 'area',
    type: ContainerValueType.INPUT,
    width: 100,
  },
  {
    title: '发证日期',
    key: 'issucertDate',
    realKey: 'issucert_date',
    type: ContainerValueType.INPUT,
    width: 100,
  },
  {
    title: '权属证明',
    key: 'proofOfTitle',
    realKey: 'proof_of_title',
    type: ContainerValueType.INPUT,
    width: 100,
  },
  {
    title: '权属证书编号',
    key: 'certificateNum',
    realKey: 'certificate_num',
    type: ContainerValueType.INPUT,
    width: 150,
  },
  { title: '权属年限', key: 'tenure', realKey: 'tenure', type: ContainerValueType.INPUT, width: 100 },
  {
    title: '坐落位置',
    key: 'loc',
    realKey: 'loc',
    type: ContainerValueType.INPUT,
    width: 100,
  },
  {
    title: '土地使用权类型',
    key: 'landUseRightsType',
    realKey: 'land_use_rights_type',
    type: ContainerValueType.INPUT,
    width: 200,
  },
  {
    title: '产权形式',
    key: 'propertyForm',
    realKey: 'property_form',
    type: ContainerValueType.INPUT,
    width: 100,
    render: (row: any, index: number) => {
      return renderSelectOptions(row, index, '产权形式', 'propertyForm')
    },
    realRender: (row: any, index: number) => {
      return renderSelectOptions(row, index, '产权形式', 'propertyForm', true)
    },
  },
  {
    title: '医疗机构编码',
    key: 'hospitalId',
    realKey: 'hospital_id',
    type: ContainerValueType.INPUT,
    width: 200,
  },
  { title: '月折旧率', key: 'deprratMon', realKey: 'deprrat_mon', type: ContainerValueType.INPUT, width: 100 },
  { title: '月折旧额', key: 'deprMon', realKey: 'depr_mon', type: ContainerValueType.INPUT, width: 100 },
  { title: '已使用月份', key: 'usedMon', realKey: 'used_mon', type: ContainerValueType.INPUT, width: 130 },
  { title: '车牌照号', key: 'lpn', realKey: 'lpn', type: ContainerValueType.INPUT, width: 100 },
  {
    title: '减少方式',
    key: 'redcWay',
    realKey: 'redc_way',
    type: ContainerValueType.SELECT,
    width: 100,
    optionKey: '减少方式',
    render: (row: any, index: number) => {
      return renderSelectOptions(row, index, '减少方式', 'redcWay')
    },
    realRender: (row: any, index: number) => {
      return renderSelectOptions(row, index, '减少方式', 'redcWay', true)
    },
  },
  { title: '录入人', key: 'inpter', realKey: 'inpter', type: ContainerValueType.INPUT, width: 100 },
  { title: '录入日期', key: 'inptDate', realKey: 'inpt_date', type: ContainerValueType.INPUT, width: 100 },
  {
    title: '是否已编制凭证',
    key: 'isPrepareVouchers',
    realKey: 'is_prepare_vouchers',
    type: ContainerValueType.INPUT,
    width: 160,
  },
  {
    title: '是否已审核',
    key: 'isChk',
    realKey: 'is_chk',
    type: ContainerValueType.INPUT,
    width: 130,
    dictType: 'CHECK_STATUS',
    options: JPGlobal.getDictByType('CHECK_STATUS'),
  },
  { title: '审核人', key: 'chker', realKey: 'chker', type: ContainerValueType.INPUT, width: 100 },
  { title: '备注', key: 'memo', realKey: 'memo', type: ContainerValueType.INPUT, width: 100 },
  {
    title: '土地面积（㎡）',
    key: 'landArea',
    realKey: 'land_area',
    type: ContainerValueType.NUMBER,
    dataType: DataType.NUMBER,
    width: 130,
  },
  {
    title: '权属证书所有权人',
    key: 'coOwner',
    realKey: 'co_owner',
    type: ContainerValueType.INPUT,
    width: 200,
  },
  {
    title: '土地证载明面积（㎡）',
    key: 'zkc',
    realKey: 'zkc',
    type: ContainerValueType.NUMBER,
    dataType: DataType.NUMBER,
    width: 200,
  },
  { title: '入账形式', key: 'entryForm', realKey: 'entry_form', type: ContainerValueType.INPUT, width: 100 },
  {
    title: '土地来源',
    key: 'landSouc',
    realKey: 'land_souc',
    type: ContainerValueType.INPUT,
    width: 100,
  },
  { title: '使用方向', key: 'usage', realKey: 'usage', type: ContainerValueType.INPUT, width: 100 },
  {
    title: '权属性质',
    key: 'propertyChar',
    realKey: 'property_char',
    type: ContainerValueType.INPUT,
    width: 100,
    render: (row: any, index: number) => {
      return renderSelectOptions(row, index, '权属性质', 'propertyChar')
    },
  },
  {
    title: '权属证书发证时间',
    key: 'coDate',
    realKey: 'co_date',
    type: ContainerValueType.INPUT,
    width: 200,
  },
  {
    title: '权属所有人',
    key: 'propertyOwner',
    realKey: 'property_owner',
    type: ContainerValueType.INPUT,

    width: 130,
  },
  {
    title: '建筑结构',
    key: 'buildingStructure',
    realKey: 'building_structure',
    type: ContainerValueType.INPUT,
    width: 100,
    render: (row: any, index: number) => {
      return renderSelectOptions(row, index, '建筑结构', 'buildingStructure')
    },
  },
  { title: '使用状况', key: 'use', realKey: 'use', type: ContainerValueType.INPUT, width: 100 },
  {
    title: '价值类型',
    key: 'valueType',
    realKey: 'value_type',
    type: ContainerValueType.INPUT,
    width: 100,
    render: (row: any, index: number) => {
      return renderSelectOptions(row, index, '价值类型', 'valueType')
    },
  },
  { title: '发动机号', key: 'engineNo', realKey: 'engine_no', type: ContainerValueType.INPUT, width: 100 },
  {
    title: '资产大类',
    key: 'assetClassification',
    realKey: 'asset_classification',
    type: ContainerValueType.INPUT,
    disabled: true,
    width: 100,
  },
  {
    title: '土地使用权面积',
    key: 'landUsageArea',
    realKey: 'land_usage_area',
    type: ContainerValueType.INPUT,
    width: 150,
  },
  { title: '土地使用权人', key: 'landUser', realKey: 'land_user', type: ContainerValueType.INPUT, width: 150 },
  { title: '地类(用途)', key: 'landUsed', realKey: 'land_used', type: ContainerValueType.INPUT, width: 150 },
  {
    title: '房屋所有权人',
    key: 'ownerOfHouse',
    realKey: 'owner_of_house',
    type: ContainerValueType.INPUT,
    width: 150,
  },
  { title: '记账凭证号', key: 'avn', realKey: 'avn', type: ContainerValueType.INPUT, width: 140 },
  { title: '设备用途', key: 'devUsed', realKey: 'dev_used', type: ContainerValueType.INPUT, width: 100 },
  { title: '车辆识别代码', key: 'vin', realKey: 'vin', type: ContainerValueType.INPUT, width: 140 },
  { title: '是否拆分卡片', key: 'isSplit', realKey: 'is_split', type: ContainerValueType.INPUT, width: 140 },
  {
    title: '已注销',
    key: 'isCanc',
    realKey: 'is_canc',
    type: ContainerValueType.SELECT,
    dictType: 'YES_OR_NO',
    options: JPGlobal.getDictByType('YES_OR_NO'),
    width: 100,
  },
  { title: '注销人', key: 'cancPsn', realKey: 'canc_psn', type: ContainerValueType.INPUT, width: 100 },
  { title: '注销日期', key: 'cancDate', realKey: 'canc_date', type: ContainerValueType.INPUT, width: 100 },
  { title: '证书号', key: 'certNo', realKey: 'cert_no', type: ContainerValueType.INPUT, width: 100 },
  {
    title: '取得方式',
    key: 'obtainWay',
    realKey: 'obtain_way',
    type: ContainerValueType.INPUT,
    width: 100,
    render: (row: any, index: number) => {
      return renderSelectOptions(row, index, '取得方式', 'obtainWay')
    },
  },
  { title: '取得日期', key: 'obtainDate', realKey: 'obtain_date', type: ContainerValueType.INPUT, width: 100 },
  {
    title: '财务入账状态',
    key: 'entryStatus',
    realKey: 'entry_status',
    type: ContainerValueType.INPUT,
    width: 130,
  },
  { title: '财务入账日期', key: 'entryDate', realKey: 'entry_date', type: ContainerValueType.INPUT, width: 130 },
  { title: '采购组织形式', key: 'pof', realKey: 'pof', type: ContainerValueType.INPUT, width: 130 },
  { title: '会计凭证号', key: 'jvn', realKey: 'jvn', type: ContainerValueType.INPUT, width: 130 },
  {
    title: '竣工日期',
    key: 'completedDate',
    realKey: 'completed_date',
    type: ContainerValueType.INPUT,

    width: 100,
  },
  {
    title: '设计用途',
    key: 'dsgnUsed',
    realKey: 'dsgn_used',
    type: ContainerValueType.INPUT,
    width: 100,
  },
  {
    title: '持证人',
    key: 'holder',
    realKey: 'holder',

    type: ContainerValueType.INPUT,
    width: 100,
    render: (row: any, index: number) => {
      return renderSelectOptions(row, index, '持证人', 'holder')
    },
  },
  { title: '折旧状态', key: 'deprStatus', realKey: 'depr_status', type: ContainerValueType.INPUT, width: 100 },
  {
    title: '其中:取暖面积',
    key: 'heatingArea',

    realKey: 'heating_area',
    type: ContainerValueType.INPUT,
    width: 200,
  },
  {
    title: '其中:危房面积',

    key: 'dangerArea',
    realKey: 'danger_area',
    type: ContainerValueType.INPUT,
    width: 200,
  },
  { title: '非财政拨款', key: 'unGf', realKey: 'un_gf', type: ContainerValueType.INPUT, width: 140 },
  {
    title: '闲置面积',
    key: 'idleArea',
    realKey: 'idle_area',
    type: ContainerValueType.INPUT,
    width: 100,
  },
  {
    title: '自用面积',
    key: 'selfArea',
    realKey: 'self_area',
    type: ContainerValueType.INPUT,
    width: 100,
  },
  {
    title: '出借面积',
    key: 'lendArea',
    realKey: 'lend_area',
    type: ContainerValueType.INPUT,

    width: 100,
  },
  {
    title: '出租面积',
    key: 'hireArea',
    realKey: 'hire_area',
    type: ContainerValueType.INPUT,

    width: 100,
  },
  {
    title: '其他面积',
    key: 'otherArea',
    realKey: 'other_area',
    type: ContainerValueType.INPUT,

    width: 100,
  },
  {
    title: '行驶证登记日期',
    key: 'regisDate',

    realKey: 'regis_date',
    type: ContainerValueType.INPUT,
    width: 200,
  },
  {
    title: '车辆产地',
    key: 'vehicleOrigin',
    realKey: 'vehicle_origin',

    type: ContainerValueType.INPUT,
    width: 100,
  },
  {
    title: '车辆品牌',
    key: 'vehicleBrand',

    realKey: 'vehicle_brand',
    type: ContainerValueType.INPUT,
    width: 100,
  },
  {
    title: '排气量',
    key: 'displacement',
    realKey: 'displacement',

    type: ContainerValueType.INPUT,
    width: 100,
  },
  {
    title: '编制情况',
    key: 'compilationStatus',
    realKey: 'compilation_status',
    type: ContainerValueType.INPUT,
    width: 100,
  },
  {
    title: '车辆用途',
    key: 'vehicleUsage',
    realKey: 'vehicle_usage',
    type: ContainerValueType.INPUT,

    width: 100,
  },
  {
    title: '出版社',
    key: 'press',
    realKey: 'press',
    type: ContainerValueType.INPUT,
    width: 100,
  },
  {
    title: '出版日期',
    key: 'publicationDate',

    realKey: 'publication_date',
    type: ContainerValueType.INPUT,
    width: 100,
  },
  {
    title: '档案号',
    key: 'fileNo',
    realKey: 'file_no',
    type: ContainerValueType.INPUT,
    width: 100,
  },
  {
    title: '保存年限(档案)',
    key: 'storagePeriod',
    realKey: 'storage_period',
    type: ContainerValueType.INPUT,

    width: 200,
  },
  {
    title: '文物等级',
    key: 'cocr',
    realKey: 'cocr',
    type: ContainerValueType.INPUT,
    width: 100,
  },
  {
    title: '来源地(文物)',
    key: 'origin',
    realKey: 'origin',
    type: ContainerValueType.INPUT,
    width: 150,
  },
  {
    title: '藏品年代',
    key: 'collectionAge',

    realKey: 'collection_age',
    type: ContainerValueType.INPUT,
    width: 100,
  },
  {
    title: '栽种年龄',
    key: 'plantingAge',
    realKey: 'planting_age',
    type: ContainerValueType.INPUT,
    width: 100,
  },
  {
    title: '栽种年份',
    key: 'plantingYear',
    realKey: 'planting_year',
    type: ContainerValueType.INPUT,

    width: 100,
  },
  {
    title: '纲属科',
    key: 'genus',
    realKey: 'genus',
    type: ContainerValueType.INPUT,
    width: 100,
  },
  {
    title: '产地(动植物)',
    key: 'producer',
    realKey: 'producer',
    type: ContainerValueType.INPUT,
    width: 140,
  },
  {
    title: '分摊面积',
    key: 'sharedArea',
    realKey: 'shared_area',
    type: ContainerValueType.INPUT,
    width: 100,
  },
  {
    title: '独用面积',
    key: 'exclusiveArea',
    realKey: 'exclusive_area',
    type: ContainerValueType.INPUT,
    width: 100,
  },
  {
    title: '土地级次',
    key: 'landLevel',
    realKey: 'land_level',
    type: ContainerValueType.INPUT,
    width: 100,
  },
  {
    title: '资产分类',
    key: 'type',
    realKey: 'type',
    type: ContainerValueType.SELECT,
    width: 100,
    show: false,
    selection: [
      {
        value: '1',
        label: '固定资产',
      },
      {
        value: '2',
        label: '非固定资产资产',
      },
      {
        value: '3',
        label: '资产子项',
      },
    ],
  },
]

export { property, propertyColumn, propertySon }
