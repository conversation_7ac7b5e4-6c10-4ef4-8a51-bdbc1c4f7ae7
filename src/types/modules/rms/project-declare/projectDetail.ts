import { FormattedValueDatetimeRange } from '@/views/modules/rms/projectApplication/types/interface.ts'
import {
  RmsCooperativeUnits,
  RmsFundsExpense,
  RmsFundsSource,
  RmsInstrumentsEquipment,
  RmsOrganizationalStructure,
  RmsPerformanceGoals,
  RmsProjectInfo,
  RmsProjectPerformanceGoal,
  RmsResearcher,
  RmsResearchIndex,
  RmsResearchIndexSupply,
  RmsRichText,
} from '@/types/modules/rms/entity/inex.ts'

/**
 * 申报表单校验key
 */
export enum DeclarationValidateKey {
  // 草稿
  DRAFT = 'draft',
  // 储备申报
  PREPARATION = 'preparation',
  // 任务书
  BRIEF = 'brief',
}

/**
 * 项目详情表单实例
 */
export interface ProjectDetailInst {
  // 校验表单
  validateForm: (keys: DeclarationValidateKey[]) => Promise<boolean>
  // 提交任务书
  handleSubmitBrief: () => Promise<boolean>
  // 保存草稿
  saveDraft: () => Promise<boolean>
  // 表单数据
  saveForm: any
}

export enum ProjectOperationType {
  // 新建项目
  ADD = 1,
  // 修改项目
  EDIT = 2,
  // 填报任务书
  FILL_BRIEF = 3,
  // 项目详情，只读
  DETAILS = 4,
  // 储备申报
  PREPARATION_DECLARATION = 5,
  // 立项申报
  PROJECT_DECLARATION = 6,
  // 项目流程详情，只读，仅当用于流程详情时使用
  PROCESS_DETAILS = 7,
  // 23年项目申报
  APPLY_2023 = 8,
}

/**
 * 项目详情属性
 */
export interface ProjectDetailProps {
  // 项目ID
  projectId?: number | null
  // 是否只读
  readonly?: boolean
  // 是否显示申报承诺导航条
  showPromiseAnchor?: boolean
  // 是否流程详情
  isFlowDetail?: boolean
  // 操作类型
  operationType?: ProjectOperationType
  // 是否是23年项目申报
  isProject2023?: boolean
  // 是否隐藏承诺书
  hidePromise?: boolean
  // 技术查新导航栏
  techQuery?: boolean
  // 伦理审批导航栏
  ethicsApproval?: boolean
  // 申报书备案导航栏
  applicationDeclaration?: boolean
}

/**
 * 知识产权信息及经费预算
 */
export interface IntellectualProperty {
  //发明专利项数
  inventPatentCnt: number | null
  //实用型新型专利项数
  utilPatentCnt: number | null
  //其他专利项数
  otherPatentNum: number | null
}

/**
 * 项目信息
 */
export interface ProjectInfo extends RmsProjectInfo {
  // 知识产权
  intellectualProperty?: IntellectualProperty | null
}

/**
 * 项目绩效目标
 */
export interface PerformanceGoal extends RmsPerformanceGoals {}

/**
 * 分年度研究内容和考核指标
 */
export interface ResearchIndex extends RmsResearchIndex {
  // 序号
  seq: number | null
  // 起止时间(仅用于页面中时间范围选择器)
  timeRange?: FormattedValueDatetimeRange | null
}

/**
 * 研究内容与考核指标的补充说明与预期目标
 */
export interface ResearchIndexSupply extends RmsResearchIndexSupply {}

/**
 * 研究仪器设备登记
 */
export interface InstrumentsEquipment extends RmsInstrumentsEquipment {}

/**
 * 项目经费来源
 */
export interface FundingSource extends RmsFundsSource {}

/**
 * 项目支出经费
 */
export interface Expenditure extends RmsFundsExpense {
  // 概算科目名称
  fundingName?: string | null
}

export interface ExpenditureTreeNode extends Expenditure {
  // 父节点ID
  parentFundingId?: number
  // 子节点
  children?: ExpenditureTreeNode[]
  // 子节点数量
  lowNum?: number
}

/**
 * 项目组织结构与团队概况
 */
export interface OrganizationalStructure extends RmsOrganizationalStructure {}

/**
 * 合作单位
 */
export interface CooperativeUnit extends RmsCooperativeUnits {}

/**
 * 课题组主要成员
 */
export interface MainMember extends RmsResearcher {
  // 是否内部人员
  internal: boolean | null
}

/**
 * 富文本信息
 */
export interface RichText extends RmsRichText {}
