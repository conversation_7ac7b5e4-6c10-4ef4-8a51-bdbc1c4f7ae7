<template>
  <div class="mobile-layout">
    <!-- 顶部标题栏 - 固定在顶部 -->
    <div class="mobile-header">
      <!-- 返回按钮 -->
      <button
        v-if="showBackButton"
        @click="handleBack"
        class="flex items-center justify-center w-8 h-8 rounded-full hover:bg-gray-100 transition-colors"
      >
        <n-icon size="20" class="text-gray-600">
          <ArrowBackOutline />
        </n-icon>
      </button>

      <!-- 页面标题 -->
      <div class="flex-1 text-center">
        <h1 class="text-lg font-medium text-gray-900 truncate">
          {{ currentPageTitle }}
        </h1>
      </div>

      <!-- 右侧操作区域 -->
      <div class="flex items-center gap-2">
        <!-- 消息图标 -->
        <button
          @click="handleMessageClick"
          class="flex items-center justify-center w-8 h-8 rounded-full hover:bg-gray-100 transition-colors relative"
        >
          <el-badge
            :value="messageNotification.unreadCount.value"
            :show-zero="false"
            :show="messageNotification.shouldShowMobileBadge.value"
            :offset="[7, 5]"
            size="small"
            type="danger"
          >
            <n-icon size="20" class="text-gray-600">
              <NotificationsOutline />
            </n-icon>
          </el-badge>
        </button>
        <slot name="header-actions"></slot>
      </div>
    </div>

    <!-- 面包屑导航（可选） - 固定在顶部标题栏下方 -->
    <div v-if="showBreadcrumb && breadcrumbItems.length > 1" class="mobile-breadcrumb">
      <n-breadcrumb class="text-sm">
        <n-breadcrumb-item
          v-for="(item, index) in breadcrumbItems"
          :key="index"
          :clickable="index < breadcrumbItems.length - 1"
          @click="handleBreadcrumbClick(item, index)"
        >
          {{ item.title }}
        </n-breadcrumb-item>
      </n-breadcrumb>
    </div>

    <!-- 主内容区域 - 自适应高度 -->
    <div
      class="mobile-main"
      v-if="viewportInfo"
      :style="{
        marginTop: `calc(${headerHeight}px + env(safe-area-inset-top))`,
        maxHeight: viewportInfo
          ? `${Math.max(
              viewportInfo.visualHeight - headerHeight - viewportInfo.safeArea.top - viewportInfo.safeArea.bottom - 44,
              200
            )}px`
          : `calc(var(--dynamic-vh, 100vh) - ${
              headerHeight + 70
            }px - env(safe-area-inset-top) - env(safe-area-inset-bottom))`,
      }"
    >
      <router-view v-slot="{ Component }">
        <!-- <transition :name="transitionName" mode="out-in" @before-enter="onBeforeEnter" @after-enter="onAfterEnter"> -->
        <KeepAlive :include="includes">
          <component :is="Component" />
        </KeepAlive>
        <!-- </transition> -->
      </router-view>
    </div>

    <!-- 底部导航栏 - 固定在底部 -->
    <div class="mobile-bottom-nav">
      <div class="flex items-center justify-around py-2">
        <button
          v-for="item in bottomNavItems"
          :key="item.key"
          @click="handleBottomNavClick(item)"
          :class="[
            'flex flex-col items-center justify-center py-4 px-3 rounded-lg transition-all duration-200 relative',
            item.key === activeBottomNav
              ? 'text-green-700 bg-blue-50'
              : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50',
          ]"
        >
          <!-- 普通图标 -->
          <n-icon :size="item.key === activeBottomNav ? 24 : 20" class="mb-1">
            <component :is="item.icon" />
          </n-icon>
          <span class="text-xs font-medium">{{ item.title }}</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  import { NIcon, NBreadcrumb, NBreadcrumbItem, NBadge } from 'naive-ui'
  import {
    ArrowBackOutline,
    HomeOutline,
    NotificationsOutline,
    PersonOutline,
    GridOutline,
    CheckmarkCircleOutline,
  } from '@vicons/ionicons5'
  import { useGlobalMessageNotification } from '@/composables/useMessageNotification'
  import { useSysStore } from '@/store'
  import { addViewportListener, removeViewportListener, type ViewportInfo } from '@/utils/viewport'

  // 路由相关
  const router = useRouter()
  const route = useRoute()

  // 消息通知管理
  const messageNotification = useGlobalMessageNotification()

  // 响应式数据
  const showBackButton = ref(true)
  const showBreadcrumb = ref(false)
  const currentPageTitle = ref('系统首页')
  const activeBottomNav = ref('home')
  const transitionName = ref('slide-left')
  const includes = ref<string[]>([])
  const sysStore = useSysStore()

  // 视口管理相关
  const viewportInfo = ref<ViewportInfo | null>(null)
  const dynamicLayoutStyle = ref<Record<string, string>>({})

  watch(
    () => sysStore.getCacheNames,
    names => {
      includes.value = names
    },
    { deep: true }
  )
  // 动态计算顶部区域高度
  const headerHeight = computed(() => {
    const baseHeaderHeight = 40 // 基础标题栏高度
    const breadcrumbHeight = showBreadcrumb.value ? 40 : 0 // 面包屑高度
    return baseHeaderHeight + breadcrumbHeight
  })

  // 面包屑数据
  interface BreadcrumbItem {
    title: string
    path?: string
  }

  const breadcrumbItems = ref<BreadcrumbItem[]>([{ title: '首页', path: '/' }])

  // 底部导航配置
  interface BottomNavItem {
    key: string
    title: string
    icon: any
    path: string
  }

  const bottomNavItems: BottomNavItem[] = [
    {
      key: 'home',
      title: '首页',
      icon: HomeOutline,
      path: '/home', // 指向App菜单
    },
    {
      key: 'gateway',
      title: '门户',
      icon: GridOutline,
      path: '/gateway', // 返回系统选择
    },
    {
      key: 'approval',
      title: '审批',
      icon: CheckmarkCircleOutline,
      path: '/approval',
    },
    {
      key: 'profile',
      title: '我的',
      icon: PersonOutline,
      path: '/personCenter',
    },
  ]

  // 计算属性
  const isHomePage = computed(() => {
    return route.path === '/' || route.path === '/gateway' || route.path === '/home'
  })

  // 方法
  const handleBack = () => {
    console.log('window.history.length', window.history.length)
    if (window.history.length > 1) {
      router.go(-1)
    } else {
      router.push('/gateway')
    }
  }

  const handleBottomNavClick = (item: BottomNavItem) => {
    if (item.key === 'home') {
      // 如果重复点击首页且当前激活的就是首页
      if (activeBottomNav.value === 'home') {
        // 如果当前不在/home页面，则跳转到/home
        if (route.path !== '/home') {
          router.push('/home')
        }
        return
      }
    }

    if (item.key !== activeBottomNav.value) {
      activeBottomNav.value = item.key
      router.push(item.path)
    }
  }

  const handleMessageClick = () => {
    router.push('/message')
  }

  const handleBreadcrumbClick = async (item: BreadcrumbItem, index: number) => {
    console.log('item', item)
    console.log('index', index)
    // 只允许点击第一个节点（首页）
    let isFirstNode = index === 0
    const isSecondNode = index === 1
    // 如果不是第一个节点，直接返回，不执行任何操作
    if (!isFirstNode && !isSecondNode) {
      isFirstNode = true
    }

    // 只有第一个节点可以点击，且必须有路径
    if ((isFirstNode || isSecondNode) && item.path) {
      try {
        // 第一个节点通常是首页，跳转到gateway
        if (item.path === '/gateway' || item.path === '/' || item.title === '首页') {
          router.push('/gateway')
          return
        }

        // 如果第一个节点是其他路径，也进行跳转
        const resolved = router.resolve(item.path)
        if (resolved.name !== 'NotFound' && resolved.matched.length > 0) {
          router.push('/home')
        } else {
          // 路径无效时跳转到首页
          console.warn(`面包屑路径无效: ${item.path}，跳转到首页`)
          router.push('/gateway')
        }
      } catch (error) {
        console.error('面包屑导航失败:', error)
        // 出错时跳转到首页
        router.push('/gateway')
      }
    }
  }

  // 页面转场动画
  const onBeforeEnter = () => {
    // 转场开始前的处理
  }

  const onAfterEnter = () => {
    // 转场完成后的处理
  }

  // 更新页面标题和导航状态
  const updatePageInfo = () => {
    // 根据路由更新页面标题
    const routeMeta = route.meta as any
    currentPageTitle.value = routeMeta?.displayName || routeMeta?.title || (route.name as string) || '页面'

    // 更新底部导航激活状态
    const currentPath = route.path
    const matchedNavItem = bottomNavItems.find(
      item => currentPath === item.path || currentPath.startsWith(item.path + '/')
    )
    if (matchedNavItem) {
      activeBottomNav.value = matchedNavItem.key
    }

    // 更新返回按钮显示状态
    showBackButton.value = !isHomePage.value

    // 更新面包屑
    // updateBreadcrumb()
  }

  const updateBreadcrumb = () => {
    const pathSegments = route.path.split('/').filter(Boolean)
    const items: BreadcrumbItem[] = [{ title: '首页', path: '/gateway' }]

    // 特殊路径处理
    if (route.path === '/gateway' || route.path === '/') {
      breadcrumbItems.value = [{ title: '首页', path: '/gateway' }]
      showBreadcrumb.value = false
      return
    }

    if (route.path === '/home') {
      breadcrumbItems.value = [
        { title: '首页', path: '/gateway' },
        { title: '工作台', path: '/home' },
      ]
      showBreadcrumb.value = false
      return
    }

    // 构建面包屑路径
    let currentPath = ''
    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`

      // 根据路径段生成更友好的标题
      let title = segment
      if (segment === 'oa') {
        title = 'OA系统'
      } else if (segment === 'home') {
        title = '工作台'
      } else if (/^\d+$/.test(segment)) {
        // 如果是纯数字，可能是组织ID，显示为"组织"
        title = `组织${segment}`
      }

      // 只有当路径是有效路由时才添加到面包屑
      try {
        const resolved = router.resolve(currentPath)
        if (resolved.matched.length > 0 && resolved.name !== 'NotFound') {
          items.push({
            title,
            path: currentPath,
          })
        } else if (index === pathSegments.length - 1) {
          // 如果是最后一个段且路由无效，仍然显示但不可点击
          items.push({
            title,
            path: undefined,
          })
        }
      } catch (error) {
        // 路由解析失败，跳过此段
        console.warn(`面包屑路径解析失败: ${currentPath}`)
      }
    })

    breadcrumbItems.value = items
    // showBreadcrumb.value = items.length > 1
    showBreadcrumb.value = false
  }

  // 监听路由变化
  watch(
    route,
    () => {
      updatePageInfo()
    },
    { immediate: true }
  )

  // 移动端适配样式注入
  const injectMobileStyles = () => {
    const styleId = 'mobile-layout-global-styles'

    // 检查是否已经注入过样式
    if (document.getElementById(styleId)) {
      return
    }

    const style = document.createElement('style')
    style.id = styleId
    style.innerHTML = `
.businessFormComponent .n-grid {
  display: block !important;
}
textarea {
  height: calc(2em * 6) !important; /* 假设一行高度为2em，rows为8 */
}

/* 禁止移动端缩放 */
html,
body {
  touch-action: manipulation !important;
  -webkit-user-select: none !important;
  -webkit-touch-callout: none !important;
  user-zoom: fixed !important;
  -webkit-text-size-adjust: 100% !important;
  -ms-text-size-adjust: 100% !important;
  /* 移动端滚动优化 */
  -webkit-overflow-scrolling: touch !important;
  overscroll-behavior: contain !important;
  scroll-behavior: smooth !important;
  -webkit-scroll-behavior: smooth !important;
  /* 动态视口高度支持 */
  height: var(--dynamic-vh, 100vh) !important;
  min-height: var(--dynamic-vh, 100vh) !important;
}

* {
  touch-action: manipulation !important;
  /* 移动端滚动优化 */
  -webkit-overflow-scrolling: touch !important;
  overscroll-behavior: contain !important;
}

/* 阻止双击缩放 */
body {
  -webkit-tap-highlight-color: transparent !important;
  -webkit-touch-callout: none !important;
  -webkit-user-select: none !important;
  -khtml-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
  /* 移动端滚动优化 */
  overscroll-behavior-y: contain !important;
  -webkit-overflow-scrolling: touch !important;
}

.n-card > .n-card__content:first-child,
.n-card > .n-card__footer:first-child {
  padding: 0 15px 15px 0 !important;
}
.n-col {
  width: 100% !important;
}
.n-grid {
  display: inline;
}
.n-descriptions--bordered {
  display: inline-grid !important;
  width: 100% !important;
}
.n-descriptions--bordered > div > table > tbody > tr {
  display: inline-grid !important;
  width: 100% !important;
}
.n-descriptions--bordered > div > table > tbody > tr > th {
  display: inline-grid !important;
  width: 100% !important;
  padding: 0px 10%;
}
.n-descriptions--bordered > div > table > tbody > tr > td {
  display: inline-grid !important;
  margin-left: 1rem;
}

    `
    document.head.appendChild(style)
  }

  // 移除移动端适配样式
  const removeMobileStyles = () => {
    const styleElement = document.getElementById('mobile-layout-global-styles')
    if (styleElement) {
      styleElement.remove()
    }
  }

  // 视口变化处理函数
  const handleViewportChange = (info: ViewportInfo) => {
    viewportInfo.value = info

    // 更新动态布局样式
    const headerHeightValue = headerHeight.value
    const bottomNavHeight = 70 // 底部导航栏高度

    dynamicLayoutStyle.value = {
      '--dynamic-vh': `${info.visualHeight}px`,
      '--safe-content-height': `${Math.max(
        info.visualHeight - headerHeightValue - bottomNavHeight - info.safeArea.top - info.safeArea.bottom,
        200
      )}px`,
      '--header-height': `${headerHeightValue}px`,
      '--bottom-nav-height': `${bottomNavHeight}px`,
    }

    // 应用到根元素
    const root = document.documentElement
    Object.entries(dynamicLayoutStyle.value).forEach(([key, value]) => {
      root.style.setProperty(key, value)
    })
  }

  // 组件挂载
  onMounted(() => {
    updatePageInfo()
    injectMobileStyles()

    // 添加视口变化监听器
    addViewportListener(handleViewportChange)
  })

  // 组件卸载
  onUnmounted(() => {
    removeMobileStyles()

    // 移除视口变化监听器
    removeViewportListener(handleViewportChange)
  })
</script>

<!-- 移动端适配全局样式现在通过 JavaScript 动态注入和移除 -->
<style scoped>
  /* 页面转场动画 */
  .slide-left-enter-active,
  .slide-left-leave-active {
    transition: transform 0.3s ease-in-out;
  }

  .slide-left-enter-from {
    transform: translateX(100%);
  }

  .slide-left-leave-to {
    transform: translateX(-100%);
  }

  .slide-right-enter-active,
  .slide-right-leave-active {
    transition: transform 0.3s ease-in-out;
  }

  .slide-right-enter-from {
    transform: translateX(-100%);
  }

  .slide-right-leave-to {
    transform: translateX(100%);
  }

  /* 安全区域适配 - 现在使用 TailwindCSS 工具类 */
  /* .safe-bottom 类已被 pb-safe-bottom 工具类替代 */

  /* 移动端布局 - 固定顶部和底部 */
  .mobile-layout {
    height: var(--dynamic-vh, 100vh); /* 使用动态视口高度 */
    background-color: #f9fafb;
    display: flex;
    flex-direction: column;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
    /* 确保在Android设备上正确处理导航栏 */
    max-height: var(--dynamic-vh, 100vh);
  }

  /* 顶部标题栏 - 固定在顶部 */
  .mobile-header {
    background-color: white;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid #e5e7eb;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    min-height: 40px;
    max-height: 40px;
    padding-top: calc(12px + env(safe-area-inset-top));
    /* padding-bottom: 12px; */
  }

  /* 面包屑导航 - 固定在顶部标题栏下方 */
  .mobile-breadcrumb {
    background-color: white;
    border-bottom: 1px solid #e5e7eb;
    padding: 8px 16px;
    position: fixed;
    top: calc(40px + env(safe-area-inset-top));
    left: 0;
    right: 0;
    z-index: 999;
    height: 40px;
    display: flex;
    align-items: center;
  }

  /* 主内容区域 - 自适应高度，避开顶部和底部 */
  .mobile-main {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    margin-bottom: calc(env(safe-area-inset-bottom)); /* 底部栏高度 + 安全区域 */
    position: relative;
    /* marginTop 和 minHeight 通过内联样式动态设置 */
  }

  /* 底部导航栏 - 固定在底部 */
  .mobile-bottom-nav {
    background-color: white;
    border-top: 1px solid #e5e7eb;
    position: fixed;
    bottom: env(safe-area-inset-bottom, 0); /* 使用安全区域底部边距 */
    left: 0;
    right: 0;
    z-index: 1000;
    height: auto;
    min-height: calc(env(safe-area-inset-bottom, 0)); /* 确保最小高度包含安全区域 */
    padding-bottom: env(safe-area-inset-bottom, 0);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    /* 确保在所有设备上都能正确显示 */
    box-sizing: border-box;
  }

  /* 触摸反馈 */
  button {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
  }

  button:active {
    transform: scale(0.95);
  }
</style>
