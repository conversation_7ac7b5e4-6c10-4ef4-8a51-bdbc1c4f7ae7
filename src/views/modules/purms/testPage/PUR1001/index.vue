<template>
  <div class="p-4">
    <n-card title="PUR1001 接口测试：获取二级库采购单详情" class="mb-4">
      <n-tabs>
        <n-tab-pane name="1" tab="接口测试">
          <n-space vertical>
            <n-space>
              <n-form-item label="起始时间">
                <n-date-picker
                  v-model:value="formState.startTime"
                  type="datetime"
                  format="yyyy-MM-dd HH:mm:ss"
                  placeholder="选择开始日期时间"
                  style="width: 220px"
                  clearable
                />
              </n-form-item>
              <n-form-item label="结束时间">
                <n-date-picker
                  v-model:value="formState.endTime"
                  type="datetime"
                  format="yyyy-MM-dd HH:mm:ss"
                  placeholder="选择结束日期时间"
                  style="width: 220px"
                  clearable
                />
              </n-form-item>
              <n-space>
                <n-button type="primary" @click="handleSearch" :loading="loading">
                  <template #icon>
                    <n-icon><SearchOutlined /></n-icon>
                  </template>
                  查询
                </n-button>
                <n-button @click="handleReset">
                  <template #icon>
                    <n-icon><ReloadOutlined /></n-icon>
                  </template>
                  重置
                </n-button>
                <n-button type="info" @click="showRawData = !showRawData">
                  <template #icon>
                    <n-icon>
                      <EyeOutlined v-if="!showRawData" />
                      <EyeInvisibleOutlined v-else />
                    </n-icon>
                  </template>
                  {{ showRawData ? '隐藏原始数据' : '显示原始数据' }}
                </n-button>
              </n-space>
            </n-space>

            <n-space>
              <n-button size="small" @click="setQuickTime('today')">今天</n-button>
              <n-button size="small" @click="setQuickTime('week')">本周</n-button>
              <n-button size="small" @click="setQuickTime('month')">本月</n-button>
              <n-button size="small" @click="setQuickTime('year')">今年</n-button>
              <n-button size="small" @click="setQuickTime('last7days')">最近7天</n-button>
              <n-button size="small" @click="setQuickTime('last30days')">最近30天</n-button>
            </n-space>

            <n-alert type="info" v-if="formState.startTime && formState.endTime">
              <template #header>查询时间范围</template>
              {{ formatTimeDisplay(formState.startTime) }} 至 {{ formatTimeDisplay(formState.endTime) }}
            </n-alert>
          </n-space>

          <n-divider />

          <n-grid :cols="2" :x-gap="16">
            <n-grid-item>
              <n-card title="请求参数" size="small">
                <n-code :code="formatJson(requestData)" language="json" show-line-numbers />
              </n-card>
            </n-grid-item>
            <n-grid-item>
              <n-card title="响应结果" size="small">
                <template #header-extra>
                  <n-tag v-if="responseData" :type="responseData.code === 200 ? 'success' : 'error'">
                    {{ responseData.code === 200 ? '成功' : '失败' }}
                  </n-tag>
                </template>
                <div v-if="loading" class="loading-container">
                  <n-spin size="medium" />
                  <span>请求中...</span>
                </div>
                <n-code v-else-if="responseData" :code="formatJson(responseData)" language="json" show-line-numbers />
                <n-empty v-else description="暂无数据，请点击查询按钮" />
              </n-card>
            </n-grid-item>
          </n-grid>
        </n-tab-pane>

        <n-tab-pane name="2" tab="接口文档">
          <div class="p-4 text-sm">
            <n-h3>PUR1001：获取二级库采购单详情</n-h3>

            <n-h4>功能描述</n-h4>
            <p>获取所有与二级库物资（aset_type 以'03'开头）相关的采购单明细，每条明细包含采购单主信息和物资主数据。</p>

            <n-h4>请求URL</n-h4>
            <n-code inline>/purms/externalApi/gateway</n-code>

            <n-h4>请求方式</n-h4>
            <n-tag type="info">POST</n-tag>

            <n-h4>请求参数示例</n-h4>
            <n-code :code="requestExample" language="json" show-line-numbers />

            <n-h4>请求参数说明</n-h4>
            <j-n-data-table
              :columns="requestFieldColumns"
              :data="requestFieldData"
              :pagination="false"
              size="small"
              striped
            />

            <n-h4>返回数据示例</n-h4>
            <n-code :code="responseExample" language="json" show-line-numbers />

            <n-h4>响应体字段说明</n-h4>
            <j-n-data-table
              :columns="responseFieldColumns"
              :data="responseFieldData"
              :pagination="false"
              size="small"
              striped
            />
          </div>
        </n-tab-pane>

        <n-tab-pane name="3" tab="对比字段">
          <div class="p-4 text-sm">
            <n-h3>PUR1001 vs M1002 字段对比分析 🔍</n-h3>
            <n-alert type="info" class="mb-4">
              <template #header>接口说明</template>
              <div>
                <p><strong>PUR1001：</strong>获取二级库采购单详情 - 采购系统接口，返回已签收的采购单明细信息</p>
                <p><strong>M1002：</strong>获取物资项目列表 - 物资管理系统接口，返回物资主数据和库存信息</p>
              </div>
            </n-alert>

            <n-grid :cols="1" :y-gap="16">
              <!-- 字段对应关系表 -->
              <n-grid-item>
                <n-card title="🔗 字段对应关系" size="small">
                  <j-n-data-table
                    :columns="fieldComparisonColumns"
                    :data="fieldComparisonData"
                    :pagination="false"
                    size="small"
                    striped
                  />
                </n-card>
              </n-grid-item>

              <!-- 示例数据对比 -->
              <n-grid-item>
                <n-card title="📊 示例数据对比" size="small">
                  <template #header-extra>
                    <n-space>
                      <n-button size="small" type="primary" @click="loadM1002Data" :loading="m1002Loading">
                        <template #icon>
                          <n-icon><SearchOutlined /></n-icon>
                        </template>
                        获取M1002数据
                      </n-button>
                    </n-space>
                  </template>

                  <n-grid :cols="2" :x-gap="16">
                    <n-grid-item>
                      <n-card title="PUR1001 示例数据" size="small">
                        <n-code :code="pur1001ExampleData" language="json" show-line-numbers />
                      </n-card>
                    </n-grid-item>
                    <n-grid-item>
                      <n-card title="M1002 示例数据" size="small">
                        <template #header-extra>
                          <n-tag v-if="m1002ResponseData" :type="m1002ResponseData.code === 200 ? 'success' : 'error'">
                            {{ m1002ResponseData.code === 200 ? '成功' : '失败' }}
                          </n-tag>
                        </template>
                        <div v-if="m1002Loading" class="loading-container">
                          <n-spin size="medium" />
                          <span>请求中...</span>
                        </div>
                        <n-code
                          v-else-if="m1002DataSource.length > 0"
                          :code="formatJson(m1002DataSource.slice(0, 3))"
                          language="json"
                          show-line-numbers
                        />
                        <n-empty v-else description="点击上方按钮获取M1002数据" />
                      </n-card>
                    </n-grid-item>
                  </n-grid>
                </n-card>
              </n-grid-item>

              <!-- 集成建议 -->
              <n-grid-item>
                <n-card title="💡 集成建议" size="small">
                  <n-alert type="warning" class="mb-3">
                    <template #header>数据关联建议</template>
                    基于字段对比分析，建议通过以下方式关联两个接口的数据：
                  </n-alert>

                  <n-ol class="space-y-2">
                    <n-li
                      ><strong>✅ 字段已统一：</strong>PUR1001.tripartiteConsumableId ↔ M1002.tripartiteConsumableId
                      完全一致</n-li
                    >
                    <n-li><strong>✅ 编码已统一：</strong>PUR1001.code ↔ M1002.code 完全一致</n-li>
                    <n-li><strong>✅ 名称已统一：</strong>PUR1001.name ↔ M1002.name 完全一致</n-li>
                    <n-li><strong>✅ 价格已统一：</strong>PUR1001.cost ↔ M1002.cost 完全一致</n-li>
                    <n-li
                      ><strong>✅ 分类已统一：</strong>PUR1001.tripartiteConsumableTypeId ↔
                      M1002.tripartiteConsumableTypeId 完全一致</n-li
                    >
                    <n-li
                      ><strong>🔗 关键连接：</strong
                      >tripartiteConsumableId来源于mmis_aset_info_assist.mat_unique_code字段</n-li
                    >
                    <n-li
                      ><strong>库存增强：</strong
                      >可利用M1002的stockNum(库存数量)和theoryNum(可用库存)补充采购决策信息</n-li
                    >
                  </n-ol>
                </n-card>
              </n-grid-item>
            </n-grid>
          </div>
        </n-tab-pane>
      </n-tabs>
    </n-card>

    <n-card title="查询结果" :loading="loading">
      <template #header-extra>
        <n-space v-if="dataSource.length > 0">
          <n-statistic label="总记录数" :value="dataSource.length" />
          <n-statistic label="已签收数" :value="getSignedCount()" />
          <n-statistic label="总金额" :value="getTotalAmount().toFixed(2)" :precision="2">
            <template #prefix>¥</template>
          </n-statistic>
        </n-space>
      </template>

      <j-n-data-table
        v-if="dataSource.length > 0"
        :columns="columns"
        :data="dataSource"
        :pagination="false"
        :scroll-x="1800"
        striped
        :row-key="record => record.detailId || record.id || Math.random()"
      />
      <n-empty v-else description="暂无数据，请选择时间范围并点击查询" />
    </n-card>

    <n-modal v-model:show="detailModalVisible" title="采购单详情" :style="{ width: '800px' }">
      <n-descriptions bordered :column="2" size="small">
        <!-- 采购单基本信息 -->
        <n-descriptions-item label="采购单号" :span="2">{{ currentDetail?.itemNo }}</n-descriptions-item>
        <n-descriptions-item label="采购项目名称" :span="2">{{ currentDetail?.requestItemName }}</n-descriptions-item>
        <n-descriptions-item label="采购类型">{{ currentDetail?.requestTypeName }}</n-descriptions-item>
        <n-descriptions-item label="采购状态">{{ currentDetail?.requestStatusName }}</n-descriptions-item>
        <n-descriptions-item label="采购单总金额">{{ currentDetail?.totalAmountRequest }}元</n-descriptions-item>
        <n-descriptions-item label="采购单创建时间">{{ currentDetail?.requestCreateTime }}</n-descriptions-item>
        <n-descriptions-item label="采购单备注" :span="2">{{
          currentDetail?.requestRemark || '无'
        }}</n-descriptions-item>

        <!-- 申请信息 -->
        <n-descriptions-item label="申请科室">{{ currentDetail?.applyOrgName }}</n-descriptions-item>
        <n-descriptions-item label="申请人">{{ currentDetail?.applicantName }}</n-descriptions-item>
        <n-descriptions-item label="申请人账号">{{ currentDetail?.applicant }}</n-descriptions-item>
        <n-descriptions-item label="申请科室ID">{{ currentDetail?.applyOrgId }}</n-descriptions-item>

        <!-- 物资详细信息 -->
        <n-descriptions-item label="物资名称(明细)">{{ currentDetail?.name }}</n-descriptions-item>
        <n-descriptions-item label="物资名称(主数据)">{{ currentDetail?.materialNameMaster }}</n-descriptions-item>
        <n-descriptions-item label="物资分类">{{ currentDetail?.tripartiteConsumableTypeId }}</n-descriptions-item>
        <n-descriptions-item label="物资编码">{{ currentDetail?.code }}</n-descriptions-item>
        <n-descriptions-item label="物资唯一编码" :span="2">{{
          currentDetail?.tripartiteConsumableId
        }}</n-descriptions-item>
        <n-descriptions-item label="规格">{{ currentDetail?.specification }}</n-descriptions-item>
        <n-descriptions-item label="型号">{{ currentDetail?.model }}</n-descriptions-item>
        <n-descriptions-item label="生产厂商" :span="2">{{ currentDetail?.manufacturer }}</n-descriptions-item>

        <!-- 数量价格信息 -->
        <n-descriptions-item label="单位">{{ currentDetail?.unit }}</n-descriptions-item>
        <n-descriptions-item label="申请数量">{{ currentDetail?.quantity }}</n-descriptions-item>
        <n-descriptions-item label="申请单价">{{ currentDetail?.cost }}元</n-descriptions-item>
        <n-descriptions-item label="明细总金额">{{ currentDetail?.totalAmountDetail }}元</n-descriptions-item>

        <!-- 明细信息 -->
        <n-descriptions-item label="明细创建时间">{{ currentDetail?.detailCreateTime }}</n-descriptions-item>
        <n-descriptions-item label="明细备注">{{ currentDetail?.detailRemark || '无' }}</n-descriptions-item>

        <!-- 签收信息 -->
        <n-descriptions-item label="签收时间">{{ currentDetail?.confirmTime }}</n-descriptions-item>
        <n-descriptions-item label="签收人编码">{{ currentDetail?.confirmPerson }}</n-descriptions-item>
      </n-descriptions>
    </n-modal>

    <n-drawer v-model:show="showRawData" :width="800" placement="right" title="原始数据">
      <n-code :code="formatJson(dataSource)" language="json" show-line-numbers />
    </n-drawer>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted, h } from 'vue'
  import { useMessage } from 'naive-ui'
  import dayjs, { Dayjs } from 'dayjs'
  import {
    callPUR1001Api,
    defaultPUR1001Params,
    IntegrationPurReqDetailVO,
    PUR1001RequestParams,
  } from '@/api/purms/testPage/purcTestPage'
  import {
    callM1002Api,
    defaultM1002Params,
    ConsumableDTO,
    M1002RequestParams,
  } from '@/api/purms/testPage/purcTestPage'
  import { SearchOutlined, ReloadOutlined, EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons-vue'

  const message = useMessage()

  // 表单状态
  const formState = reactive({
    startTime: dayjs().startOf('year').startOf('day').valueOf(), // 转换为时间戳
    endTime: dayjs().endOf('month').endOf('day').valueOf(), // 转换为时间戳
  })

  // 列表状态
  const loading = ref<boolean>(false)
  const dataSource = ref<IntegrationPurReqDetailVO[]>([])
  const requestData = ref(defaultPUR1001Params)
  const responseData = ref<any>(null)
  const showRawData = ref<boolean>(false)

  // M1002 物资项目数据状态
  const m1002Loading = ref<boolean>(false)
  const m1002DataSource = ref<ConsumableDTO[]>([])
  const m1002ResponseData = ref<any>(null)

  // 详情弹窗
  const detailModalVisible = ref<boolean>(false)
  const currentDetail = ref<IntegrationPurReqDetailVO>()

  // 列配置 - 修复类型问题
  const columns = [
    {
      title: '采购单号',
      key: 'itemNo',
      width: 150,
      fixed: 'left' as const,
      render: (row: any) => row.itemNo,
    },
    {
      title: '物资名称',
      key: 'name',
      width: 150,
      render: (row: any) => row.name,
    },
    {
      title: '物资分类',
      key: 'tripartiteConsumableTypeId',
      width: 100,
      render: (row: any) => h('span', { style: { color: '#1890ff' } }, row.tripartiteConsumableTypeId),
    },
    {
      title: '物资编码',
      key: 'code',
      width: 120,
      render: (row: any) => row.code,
    },
    {
      title: '物资唯一编码',
      key: 'tripartiteConsumableId',
      width: 150,
      render: (row: any) => row.tripartiteConsumableId,
    },
    {
      title: '规格',
      key: 'specification',
      width: 120,
      render: (row: any) => row.specification,
    },
    {
      title: '型号',
      key: 'model',
      width: 120,
      render: (row: any) => row.model,
    },
    {
      title: '单位',
      key: 'unit',
      width: 80,
      render: (row: any) => row.unit,
    },
    {
      title: '数量',
      key: 'quantity',
      width: 80,
      render: (row: any) => row.quantity,
    },
    {
      title: '单价(元)',
      key: 'cost',
      width: 100,
      render: (row: any) => row.cost,
    },
    {
      title: '金额(元)',
      key: 'totalAmountDetail',
      width: 100,
      render: (row: any) => row.totalAmountDetail,
    },
    {
      title: '生产厂商',
      key: 'manufacturer',
      width: 120,
      render: (row: any) => row.manufacturer,
    },
    {
      title: '采购类型',
      key: 'requestTypeName',
      width: 100,
      render: (row: any) => row.requestTypeName,
    },
    {
      title: '申请科室',
      key: 'applyOrgName',
      width: 120,
      render: (row: any) => row.applyOrgName,
    },
    {
      title: '申请人',
      key: 'applicantName',
      width: 100,
      render: (row: any) => row.applicantName,
    },
    {
      title: '状态',
      key: 'requestStatusName',
      width: 100,
      render: (row: any) => {
        const statusMap: Record<string, string> = {
          待审核: 'warning',
          审核通过: 'success',
          审核驳回: 'error',
          已取消: 'default',
          已完成: 'info',
        }
        return h('span', { style: { color: getStatusColor(row.requestStatusName) } }, row.requestStatusName)
      },
    },
    {
      title: '签收时间',
      key: 'confirmTime',
      width: 150,
      render: (row: any) => row.confirmTime,
    },
    {
      title: '签收人',
      key: 'confirmPerson',
      width: 100,
      render: (row: any) => row.confirmPerson,
    },
    {
      title: '创建时间',
      key: 'requestCreateTime',
      width: 150,
      render: (row: any) => row.requestCreateTime,
    },
    {
      title: '操作',
      key: 'operation',
      fixed: 'right' as const,
      width: 100,
      render: (row: any) =>
        h(
          'button',
          {
            onClick: () => viewDetail(row),
            style: { color: '#1890ff', cursor: 'pointer', border: 'none', background: 'none' },
          },
          '详情'
        ),
    },
  ]

  // 获取状态色彩
  const getStatusColor = (status: string) => {
    const statusMap: Record<string, string> = {
      待审核: '#faad14',
      审核通过: '#52c41a',
      审核驳回: '#ff4d4f',
      已取消: '#d9d9d9',
      已完成: '#1890ff',
    }
    return statusMap[status] || '#666'
  }

  // 字段对比表格列配置
  const fieldComparisonColumns = [
    {
      title: '功能分类',
      key: 'category',
      width: 120,
      render: (row: any) => h('span', { style: { fontWeight: 'bold', color: '#1890ff' } }, row.category),
    },
    {
      title: 'PUR1001 字段',
      key: 'pur1001Field',
      width: 180,
      render: (row: any) => h('code', {}, row.pur1001Field),
    },
    {
      title: 'PUR1001 含义',
      key: 'pur1001Desc',
      width: 200,
    },
    {
      title: 'M1002 字段',
      key: 'm1002Field',
      width: 180,
      render: (row: any) => h('code', {}, row.m1002Field),
    },
    {
      title: 'M1002 含义',
      key: 'm1002Desc',
      width: 200,
    },
    {
      title: '对应关系',
      key: 'relationship',
      width: 120,
      render: (row: any) => {
        const typeMap: Record<string, { color: string; text: string }> = {
          exact: { color: '#52c41a', text: '完全对应' },
          similar: { color: '#faad14', text: '相似对应' },
          partial: { color: '#ff7875', text: '部分对应' },
          none: { color: '#d9d9d9', text: '无对应' },
          extend: { color: '#1890ff', text: '扩展字段' },
        }
        const type = typeMap[row.relationship] || typeMap['none']
        return h('span', { style: { color: type.color, fontWeight: 'bold' } }, type.text)
      },
    },
    {
      title: '备注',
      key: 'remark',
      width: 250,
    },
  ]

  // 字段对比数据
  const fieldComparisonData = [
    {
      key: '1',
      category: '物资标识',
      pur1001Field: 'tripartiteConsumableId',
      pur1001Desc: '物资唯一编码(来自mmis_aset_info_assist.mat_unique_code)',
      m1002Field: 'tripartiteConsumableId',
      m1002Desc: '物资ID/物资唯一码',
      relationship: 'exact',
      remark: '✅ 已统一：完全对应的关联字段，来源于assist表',
    },
    {
      key: '2',
      category: '物资标识',
      pur1001Field: 'code',
      pur1001Desc: '物资编码',
      m1002Field: 'code',
      m1002Desc: '物资编码',
      relationship: 'exact',
      remark: '✅ 已统一：完全对应的备用关联字段',
    },
    {
      key: '3',
      category: '基本信息',
      pur1001Field: 'name',
      pur1001Desc: '物资名称(明细)',
      m1002Field: 'name',
      m1002Desc: '物资名称',
      relationship: 'exact',
      remark: '✅ 已统一：完全对应的名称字段',
    },
    {
      key: '4',
      category: '基本信息',
      pur1001Field: 'specification',
      pur1001Desc: '规格',
      m1002Field: 'specification',
      m1002Desc: '规格型号',
      relationship: 'exact',
      remark: '✅ 已统一：规格信息完全对应',
    },
    {
      key: '5',
      category: '基本信息',
      pur1001Field: 'unit',
      pur1001Desc: '单位',
      m1002Field: 'unit',
      m1002Desc: '计量单位',
      relationship: 'exact',
      remark: '✅ 已统一：计量单位完全对应',
    },
    {
      key: '6',
      category: '价格信息',
      pur1001Field: 'cost',
      pur1001Desc: '申请单价',
      m1002Field: 'cost',
      m1002Desc: '参考成本/单价',
      relationship: 'exact',
      remark: '✅ 已统一：价格字段完全对应',
    },
    {
      key: '7',
      category: '分类信息',
      pur1001Field: 'tripartiteConsumableTypeId',
      pur1001Desc: '物资类别',
      m1002Field: 'tripartiteConsumableTypeId',
      m1002Desc: '所属物资分类ID',
      relationship: 'exact',
      remark: '✅ 已统一：分类ID完全对应',
    },
    {
      key: '8',
      category: '库存信息',
      pur1001Field: '-',
      pur1001Desc: '无库存信息',
      m1002Field: 'stockNum',
      m1002Desc: '当前库存数量',
      relationship: 'extend',
      remark: 'M1002独有，可补充采购决策信息',
    },
    {
      key: '9',
      category: '库存信息',
      pur1001Field: '-',
      pur1001Desc: '无可用库存信息',
      m1002Field: 'theoryNum',
      m1002Desc: '理论库存量(可分配)',
      relationship: 'extend',
      remark: 'M1002独有，用于库存可用性分析',
    },
    {
      key: '10',
      category: '采购信息',
      pur1001Field: 'quantity',
      pur1001Desc: '申请数量',
      m1002Field: '-',
      m1002Desc: '无对应',
      relationship: 'none',
      remark: 'PUR1001独有采购业务字段',
    },
    {
      key: '11',
      category: '采购信息',
      pur1001Field: 'itemNo',
      pur1001Desc: '采购单号',
      m1002Field: '-',
      m1002Desc: '无对应',
      relationship: 'none',
      remark: 'PUR1001独有采购单标识',
    },
    {
      key: '12',
      category: '状态信息',
      pur1001Field: 'requestStatusName',
      pur1001Desc: '采购单状态',
      m1002Field: 'isPutaway',
      m1002Desc: '是否上架状态',
      relationship: 'partial',
      remark: '不同类型的状态信息',
    },
  ]

  // PUR1001示例数据
  const pur1001ExampleData = `{
  "detailId": 1001,
  "itemNo": "PR202405100001",
  "name": "医用手套",
  "specification": "M号",
  "model": "一次性",
  "unit": "盒",
  "quantity": 100,
  "cost": 25.5,
  "totalAmountDetail": 2550,
  "code": "MMIS2024001",
  "tripartiteConsumableId": "1001",
  "tripartiteConsumableTypeId": "0301",
  "confirmTime": "2024-04-15 10:30:00",
  "applyOrgName": "内科",
  "applicantName": "张医生",
  "requestStatusName": "已完成"
}`

  // 示例数据
  const requestExample = `{
  "hospitalId": "zjxrmyy",
  "encryptKey": "57B836BBDD4B75000EB231EE5C5B52F5CC3D3B1D904C277E",
  "infno": "PUR1001",
  "input": {
    "startTime": "2024-01-01 00:00:00",
    "endTime": "2024-05-31 23:59:59"
  }
}`

  const responseExample = `{
  "code": 200,
  "message": "成功",
  "data": [
    {
      "detailId": 1001,
      "itemNo": "PR202405100001",
      "name": "医用手套",
      "specification": "M号",
      "model": "一次性",
      "unit": "盒",
      "quantity": 100,
      "cost": 25.5,
      "totalAmountDetail": 2550,
      "code": "MMIS2024001",
      "detailCreateTime": "2024-04-10 09:00:00",
      "detailRemark": "紧急采购",
      "confirmTime": "2024-04-15 10:30:00",
      "confirmPerson": "EMP001",
      "tripartiteConsumableId": "1001",
      "tripartiteConsumableTypeId": "0301",
      "materialNameMaster": "医用手套（主数据）",
      "manufacturer": "某医疗器械公司",
      "requestTypeName": "物资采购",
      "applyOrgId": "ORG001",
      "applyOrgName": "内科",
      "applicant": "zhangsan",
      "applicantName": "张医生",
      "requestItemName": "内科月度物资采购",
      "totalAmountRequest": 25500,
      "requestStatusName": "已完成",
      "requestCreateTime": "2024-04-10 09:00:00",
      "requestRemark": "月度常规采购"
    }
  ]
}`

  const requestFieldColumns = [
    {
      title: '参数名',
      key: 'name',
    },
    {
      title: '必选',
      key: 'required',
    },
    {
      title: '类型',
      key: 'type',
    },
    {
      title: '说明',
      key: 'description',
    },
  ]

  const requestFieldData = [
    {
      key: '1',
      name: 'hospitalId',
      required: '是',
      type: 'string',
      description: '医院ID，固定值：zjxrmyy',
    },
    {
      key: '2',
      name: 'encryptKey',
      required: '是',
      type: 'string',
      description: '加密密钥，固定值：57B836BBDD4B75000EB231EE5C5B52F5CC3D3B1D904C277E',
    },
    {
      key: '3',
      name: 'infno',
      required: '是',
      type: 'string',
      description: '接口编号，固定值：PUR1001',
    },
    {
      key: '4',
      name: 'input',
      required: '是',
      type: 'object',
      description: '输入参数',
    },
    {
      key: '5',
      name: 'input.startTime',
      required: '是',
      type: 'string',
      description: '开始时间，格式：YYYY-MM-DD HH:mm:ss',
    },
    {
      key: '6',
      name: 'input.endTime',
      required: '是',
      type: 'string',
      description: '结束时间，格式：YYYY-MM-DD HH:mm:ss',
    },
  ]

  const responseFieldColumns = [
    {
      title: '参数名',
      key: 'name',
    },
    {
      title: '类型',
      key: 'type',
    },
    {
      title: '说明',
      key: 'description',
    },
  ]

  const responseFieldData = [
    {
      key: '1',
      name: 'code',
      type: 'number',
      description: '响应码，200表示成功，其他表示失败',
    },
    {
      key: '2',
      name: 'message',
      type: 'string',
      description: '响应消息，描述响应结果',
    },
    {
      key: '3',
      name: 'data',
      type: 'array',
      description: '响应数据数组，包含采购单明细信息列表',
    },
    {
      key: '4',
      name: 'data[].detailId',
      type: 'number',
      description: '采购明细ID，唯一标识',
    },
    {
      key: '5',
      name: 'data[].itemNo',
      type: 'string',
      description: '采购单号（项目编号）',
    },
    {
      key: '6',
      name: 'data[].name',
      type: 'string',
      description: '物资名称（来自明细）',
    },
    {
      key: '7',
      name: 'data[].specification',
      type: 'string',
      description: '物资规格',
    },
    {
      key: '8',
      name: 'data[].model',
      type: 'string',
      description: '物资型号',
    },
    {
      key: '9',
      name: 'data[].unit',
      type: 'string',
      description: '计量单位',
    },
    {
      key: '10',
      name: 'data[].quantity',
      type: 'number',
      description: '申请数量',
    },
    {
      key: '11',
      name: 'data[].cost',
      type: 'number',
      description: '申请单价',
    },
    {
      key: '12',
      name: 'data[].totalAmountDetail',
      type: 'number',
      description: '申请总金额（明细）',
    },
    {
      key: '13',
      name: 'data[].code',
      type: 'string',
      description: '物资编码（明细关联）',
    },
    {
      key: '14',
      name: 'data[].detailCreateTime',
      type: 'string',
      description: '明细创建时间',
    },
    {
      key: '15',
      name: 'data[].detailRemark',
      type: 'string',
      description: '明细备注',
    },
    {
      key: '16',
      name: 'data[].confirmTime',
      type: 'string',
      description: '签收时间，格式：YYYY-MM-DD HH:mm:ss',
    },
    {
      key: '17',
      name: 'data[].confirmPerson',
      type: 'string',
      description: '签收人编码',
    },
    {
      key: '18',
      name: 'data[].tripartiteConsumableId',
      type: 'string',
      description: '物资唯一编码(来自mmis_aset_info_assist.mat_unique_code)，与M1002接口完全一致',
    },
    {
      key: '19',
      name: 'data[].tripartiteConsumableTypeId',
      type: 'string',
      description: '物资类别，与M1002接口完全一致',
    },
    {
      key: '20',
      name: 'data[].materialNameMaster',
      type: 'string',
      description: '物资名称（物资主数据）',
    },
    {
      key: '21',
      name: 'data[].manufacturer',
      type: 'string',
      description: '生产厂商',
    },
    {
      key: '22',
      name: 'data[].requestTypeName',
      type: 'string',
      description: '采购类型名称（零星采购/大型设备采购/物资采购）',
    },
    {
      key: '23',
      name: 'data[].applyOrgId',
      type: 'string',
      description: '申请科室ID',
    },
    {
      key: '24',
      name: 'data[].applyOrgName',
      type: 'string',
      description: '申请科室名称',
    },
    {
      key: '25',
      name: 'data[].applicant',
      type: 'string',
      description: '申请人账号',
    },
    {
      key: '26',
      name: 'data[].applicantName',
      type: 'string',
      description: '申请人名称',
    },
    {
      key: '27',
      name: 'data[].requestItemName',
      type: 'string',
      description: '采购项目名称',
    },
    {
      key: '28',
      name: 'data[].totalAmountRequest',
      type: 'number',
      description: '采购单总金额',
    },
    {
      key: '29',
      name: 'data[].requestStatusName',
      type: 'string',
      description: '采购单状态名称（待审核/审核通过/审核驳回/已取消等）',
    },
    {
      key: '30',
      name: 'data[].requestCreateTime',
      type: 'string',
      description: '采购单创建时间',
    },
    {
      key: '31',
      name: 'data[].requestRemark',
      type: 'string',
      description: '采购单备注',
    },
  ]

  // 加载M1002数据的方法
  const loadM1002Data = async () => {
    m1002Loading.value = true
    try {
      const response = await callM1002Api(defaultM1002Params)
      console.log('M1002 API响应:', response)

      m1002ResponseData.value = response.data

      if (response.data && response.data.code === 200) {
        m1002DataSource.value = response.data.data || []
        message.success(`成功获取 ${m1002DataSource.value.length} 个物资项目`)
      } else {
        m1002DataSource.value = []
        message.error(`获取M1002数据失败: ${response.data?.message || '接口异常'}`)
      }
    } catch (error) {
      console.error('M1002 API调用异常:', error)
      message.error('M1002接口调用异常，请查看控制台')
      m1002ResponseData.value = { code: 500, message: 'M1002接口调用异常', data: null }
      m1002DataSource.value = []
    } finally {
      m1002Loading.value = false
    }
  }

  // 查询数据
  const handleSearch = async () => {
    if (!formState.startTime || !formState.endTime) {
      message.warning('请选择开始和结束日期')
      return
    }

    loading.value = true
    try {
      // 构建请求参数
      const requestParams: PUR1001RequestParams = {
        ...defaultPUR1001Params,
        input: {
          startTime: dayjs(formState.startTime).format('YYYY-MM-DD HH:mm:ss'),
          endTime: dayjs(formState.endTime).format('YYYY-MM-DD HH:mm:ss'),
        },
      }

      // 记录请求数据
      requestData.value = requestParams

      // 调用接口
      const response = await callPUR1001Api(requestParams)
      console.log('API响应:', response)

      // 记录响应数据
      responseData.value = response.data

      if (response.data) {
        // 处理稀疏数组问题 - 转换为标准数组
        const originalArray = response.data || []
        // 创建一个包含所有非空元素的数组
        const validData: IntegrationPurReqDetailVO[] = []

        // 使用 Object.keys 获取所有索引
        Object.keys(originalArray).forEach(key => {
          if (originalArray[key] && typeof originalArray[key] === 'object') {
            validData.push(originalArray[key])
          }
        })

        console.log('处理后的数据长度:', validData.length)
        dataSource.value = validData
        message.success(`查询成功，共获取 ${validData.length} 条记录`)
      } else {
        dataSource.value = []
        message.error(`查询失败: ${response.data?.message || '接口异常'}`)
      }
    } catch (error) {
      console.error('API调用异常:', error)
      message.error('接口调用异常，请查看控制台')
      responseData.value = { code: 500, message: '接口调用异常', data: null }
      dataSource.value = []
    } finally {
      loading.value = false
    }
  }

  // 重置表单
  const handleReset = () => {
    formState.startTime = dayjs().startOf('year').startOf('day').valueOf()
    formState.endTime = dayjs().endOf('month').endOf('day').valueOf()
    dataSource.value = []
    responseData.value = null
  }

  // 查看详情
  const viewDetail = (record: IntegrationPurReqDetailVO) => {
    currentDetail.value = record
    detailModalVisible.value = true
  }

  // 格式化JSON
  const formatJson = (obj: any) => {
    if (!obj) return ''
    try {
      return JSON.stringify(obj, null, 2)
    } catch (e) {
      return String(obj)
    }
  }

  // 快速设置时间
  const setQuickTime = (type: string) => {
    const now = dayjs()

    switch (type) {
      case 'today':
        formState.startTime = now.startOf('day').valueOf()
        formState.endTime = now.endOf('day').valueOf()
        break
      case 'week':
        formState.startTime = now.startOf('week').valueOf()
        formState.endTime = now.endOf('week').valueOf()
        break
      case 'month':
        formState.startTime = now.startOf('month').valueOf()
        formState.endTime = now.endOf('month').valueOf()
        break
      case 'year':
        formState.startTime = now.startOf('year').valueOf()
        formState.endTime = now.endOf('year').valueOf()
        break
      case 'last7days':
        formState.startTime = now.startOf('day').subtract(7, 'days').valueOf()
        formState.endTime = now.endOf('day').valueOf()
        break
      case 'last30days':
        formState.startTime = now.startOf('day').subtract(30, 'days').valueOf()
        formState.endTime = now.endOf('day').valueOf()
        break
      default:
        break
    }

    const typeMap: Record<string, string> = {
      today: '今天',
      week: '本周',
      month: '本月',
      year: '今年',
      last7days: '最近7天',
      last30days: '最近30天',
    }

    message.info(`已设置时间范围为：${typeMap[type] || type}`)
  }

  // 时间显示格式化
  const formatTimeDisplay = (timestamp: number) => {
    return dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss')
  }

  // 统计函数
  const getSignedCount = () => {
    return dataSource.value.filter(item => item.confirmTime).length
  }

  const getTotalAmount = () => {
    return dataSource.value.reduce((sum, item) => sum + (item.totalAmountDetail || 0), 0)
  }

  // 初始化加载
  onMounted(() => {
    // 页面加载时可以默认查询，也可以不查询
    // handleSearch();
  })
</script>

<style scoped>
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
    gap: 8px;
  }

  .space-y-2 > * + * {
    margin-top: 0.5rem;
  }

  .mb-3 {
    margin-bottom: 0.75rem;
  }

  .mb-4 {
    margin-bottom: 1rem;
  }
</style>
