<template>
  <j-crud
    :query-method="pageQueryRmsProjectMainInfo"
    :tabs="tabs"
    :default-check-tab="route.query?.operation ? '3' : '0'"
    :queryForm="queryForm"
    name="我参与的项目"
    showAddButton
    ref="crudRef"
    :showOperationButton="false"
    :sorter="false"
    @queryComplete="handleQueryComplete"
  >
    <template #extendFormItems>
      <n-form-item label="项目名称">
        <n-input v-model:value="queryForm.projectName" clearable />
      </n-form-item>
      <n-form-item label="申报时间">
        <n-date-picker
          v-model:formatted-value="createTimeRange"
          value-format="yyyy-MM-dd"
          @update-formatted-value="createDateChange"
          :shortcuts="rangeShortcut.createTimeRange"
          :update-value-on-close="true"
          type="daterange"
          clearable
        />
      </n-form-item>
      <n-form-item label="申报级别">
        <n-select v-model:value="queryForm.projectLevel" :options="options.PROJECT_LEVEL" clearable />
      </n-form-item>
      <n-form-item label="课题类型">
        <n-select v-model:value="queryForm.topicCategory" :options="options.TOPIC_CATEGORY" clearable />
      </n-form-item>
      <n-form-item label="研究类型">
        <n-select v-model:value="queryForm.researchType" :options="options.RESEARCH_TYPE" clearable />
      </n-form-item>
      <n-form-item label="是否合作">
        <n-select v-model:value="queryForm.externalCooperation" :options="options.EXTERNAL_COOPERATION" clearable />
      </n-form-item>
      <n-form-item label="项目起始日期">
        <n-date-picker v-model:formatted-value="queryForm.projectStartDate" clearable />
      </n-form-item>
      <n-form-item label="项目结束日期">
        <n-date-picker v-model:formatted-value="queryForm.projectEndDate" clearable />
      </n-form-item>
      <n-form-item label="创新类型">
        <n-select v-model:value="queryForm.innovationType" :options="options.INNOVATION_TYPE" clearable />
      </n-form-item>
      <n-form-item label="申报类型">
        <n-select v-model:value="queryForm.projectApplyType" :options="projectApplyTypeOptions" clearable />
      </n-form-item>
    </template>
    <template #extendButtons>
      <n-button
        type="info"
        :disabled="loading.createProject"
        @click="handleShowNewBuild"
        v-if="currentTabName === TAB_TYPE.PRE_PRJ"
        >新建项目
      </n-button>
      <!-- 23年项目申报 -->
      <n-button type="warning" v-if="currentTabName === TAB_TYPE.PRE_PRJ" @click="handleShow2023ProjectApply"
        >23年项目申报</n-button
      >
    </template>
    <template #extendRightHeader>
      <n-space>
        <n-button text @click="showNotificationDrawer = true">
          申报通知
          <template #icon>
            <n-icon>
              <notifications-outline />
            </n-icon>
          </template>
        </n-button>
        <n-popover trigger="hover" placement="bottom">
          <template #trigger>
            <n-button text>
              负责人联系方式
              <template #icon>
                <n-icon>
                  <person-outline />
                </n-icon>
              </template>
            </n-button>
          </template>
          <n-card style="width: 300px" title="联系方式" :bordered="false" size="small">
            <n-space vertical>
              <div><strong>工程师：</strong>张志扬</div>
              <div><strong>联系电话：</strong>15608183959</div>
              <div><strong>微信：</strong>同号（尽量微信联系）</div>
            </n-space>
          </n-card>
        </n-popover>
      </n-space>
    </template>
    <template #content>
      <!--      新增/修改-->
      <n-drawer
        ref="projectDetailDrawerRef"
        width="100%"
        v-model:show="projectDetailActive"
        @after-leave="handlePrjDetailAfterLeave"
      >
        <n-drawer-content closable>
          <template #header>
            <h3>{{ projectDetailTitle }}</h3>
          </template>
          <project-detail
            ref="projectDetailRef"
            :project-id="rowId"
            :readonly="projectDetailReadonly"
            :operation-type="projectDetailType"
            :is-project-2023="isProject2023"
            tech-query
            ethics-approval
            application-declaration
          />
          <template #footer v-if="projectDetailType === ProjectOperationType.FILL_BRIEF">
            <n-flex justify="end">
              <n-button @click="projectDetailActive = false">取消</n-button>
              <n-button type="info" :loading="loading.submitBrief" @click="handleSubmitFill">提交</n-button>
            </n-flex>
          </template>
        </n-drawer-content>
      </n-drawer>
      <process-instance-detail-modal
        v-model:show="processVisible"
        :process-instance-id="processInstanceCode"
        :other-props="{}"
      />

      <!--   确认申报。展示项目承诺书等   -->
      <n-drawer v-model:show="declareVisible" width="100%" :on-after-leave="handleDeclareAfterLeave">
        <n-drawer-content closable>
          <template #header>
            <span style="color: red"> 请下滑至页面最底部勾选项目承诺书，并签字确认项目申报。 </span>
          </template>
          <project-detail
            ref="preparationDetailRef"
            :project-id="rowId"
            :operation-type="projectDetailType"
            tech-query
            ethics-approval
            application-declaration
            show-promise-anchor
            readonly
            :is-project-2023="isProject2023"
          />

          <n-form
            id=""
            ref="declareFormRef"
            style="padding: 0 20px 0 20px; width: 80%"
            label-placement="left"
            :show-label="false"
            :model="declareForm"
            :rules="declareFormRules"
          >
          <!-- 技术查新 -->
          <!-- <div v-if="declareType === DeclarationType.PROJECT">
            <n-divider title-placement="center">
              <span id="d18" class="declaration-part-title">技术查新</span>
            </n-divider>
            <n-h4 prefix="bar"><strong>技术查新相关附件</strong></n-h4>
            <n-form-item label="技术查新附件" path="techQueryAttachment">
              <yt-upload
                v-model:file-list="declareForm.techQueryAttachment"
                :upload-fn="uploadRmsFile"
                accept=".pdf,.doc,.docx,.xls,.xlsx,.zip,.rar,.7z,image/*"
                :limit="10"
                :disabled="readonly"
                drag
                @before-upload="disableSaveDraft"
                @success="enableSaveDraft"
                @error="enableSaveDraft"
              />
            </n-form-item>
          </div> -->
          
          <!-- 伦理审批 -->
          <!-- <div v-if="declareType === DeclarationType.PROJECT">
            <n-divider title-placement="center">
              <span id="d18" class="declaration-part-title">伦理审批附件</span>
            </n-divider>
            <n-h4 prefix="bar"><strong>伦理审批相关附件</strong></n-h4>
            <n-form-item label="伦理审批附件" path="ethicsApprovalAttachment">
              <yt-upload
                v-model:file-list="declareForm.ethicsApprovalAttachment"
                :upload-fn="uploadRmsFile"
                accept=".pdf,.doc,.docx,.xls,.xlsx,.zip,.rar,.7z,image/*"
                :limit="10"
                :disabled="readonly"
                drag
                @before-upload="disableSaveDraft"
                @success="enableSaveDraft"
                @error="enableSaveDraft"
              />
            </n-form-item>
          </div> -->

          <!-- 申报书备案 -->
          <!-- <div v-if="declareType === DeclarationType.PROJECT">
            <n-divider title-placement="center">
              <span id="d18" class="declaration-part-title">申报书备案附件</span>
            </n-divider>
            <n-h4 prefix="bar"><strong>伦理审批相关附件</strong></n-h4>
            <n-form-item label="伦理审批附件" path="applicationDeclarationAttachment">
              <yt-upload
                v-model:file-list="declareForm.applicationDeclarationAttachment"
                :upload-fn="uploadRmsFile"
                accept=".pdf,.doc,.docx,.xls,.xlsx,.zip,.rar,.7z,image/*"
                :limit="10"
                :disabled="readonly"
                drag
                @before-upload="disableSaveDraft"
                @success="enableSaveDraft"
                @error="enableSaveDraft"
              />
            </n-form-item>
          </div> -->

          <!-- 储备或立项申报时展示的承诺书 -->
            <n-divider title-placement="center">
              <span id="d17" class="declaration-part-title">意向单位信息</span>
            </n-divider>
            <!-- 意向单位 -->
            <div v-if="declareType === DeclarationType.PROJECT">
              <n-grid :cols="2" :x-gap="24">
                <n-gi :span="2">
                  <n-h4 prefix="bar"><strong>意向单位</strong></n-h4>
                </n-gi>
                <n-gi>
                  <n-form-item label="意向单位" path="intendedUnit">
                    <n-select v-model:value="declareForm.intendedUnitId" :options="options.INTENDED_UNIT" filterable />
                  </n-form-item>
                </n-gi>
                <n-gi>
                  <n-form-item label="单位名称" path="intendedUnitOther" v-if="declareForm.intendedUnitId === 0">
                    <n-input
                      v-model:value="declareForm.intendedUnitOther"
                      :maxlength="50"
                      placeholder="请输入单位名称"
                    />
                  </n-form-item>
                </n-gi>
                <template v-if="declareForm.intendedUnitId || declareForm.intendedUnitId === 0">
                  <n-gi :span="2">
                    <n-h4 prefix="bar"><strong>科研专项</strong></n-h4>
                  </n-gi>
                  <n-gi>
                    <n-form-item label="科研专项" path="intendedUnitSpecialId">
                      <n-select
                        v-model:value="declareForm.intendedUnitSpecialId"
                        :options="options.INTENDED_UNIT_SPECIAL"
                        filterable
                      />
                    </n-form-item>
                  </n-gi>
                  <n-gi>
                    <n-form-item
                      label="专项名称"
                      path="intendedUnitSpecialOther"
                      v-if="declareForm.intendedUnitSpecialId === 0"
                    >
                      <n-input
                        v-model:value="declareForm.intendedUnitSpecialOther"
                        :maxlength="50"
                        placeholder="请输入专项名称"
                      />
                    </n-form-item>
                  </n-gi>
                </template>
              </n-grid>
            </div>
            <n-h4 prefix="bar"><strong>申报人承诺</strong></n-h4>
            <n-form-item label="申报人承诺" path="_projectPromise">
              <n-checkbox v-model:checked="declareForm._projectPromise">
                <strong>我保证申报书内容的真实性、准确性。若填报失实和违反规定，本人将承担全部责任。</strong>
              </n-checkbox>
            </n-form-item>
            <!-- <div v-if="declareType === DeclarationType.PROJECT">
                <n-form-item label="承诺书附件" path="promiseAttachment">
                  <yt-upload
                    v-model:file-list="declareForm.promiseAttachment"
                    :upload-fn="uploadRmsFile"
                    accept=".pdf,.doc,.docx,.xls,.xlsx,.zip,.rar,.7z,image/*"
                    :limit="5"
                    :disabled="readonly"
                    drag
                    @before-upload="disableSaveDraft"
                    @success="enableSaveDraft"
                    @error="enableSaveDraft"
                  />
                </n-form-item>
              </div> -->
            <div v-if="declareType === DeclarationType.PREPARATION">
              <n-h4 prefix="bar"><strong>储备申报签字</strong></n-h4>
              <n-form-item label="储备申报签字" path="preSign">
                <n-image
                  v-show="declareForm._sign.flag"
                  :width="120"
                  :height="60"
                  :src="declareForm._sign.url"
                  :preview-disabled="true"
                />
                <n-tag
                  size="small"
                  type="error"
                  style="cursor: pointer; margin-left: 10px"
                  @click="declareForm._sign.show = true"
                >
                  {{ declareForm._sign.flag ? '重新签名' : '点击签名' }}
                </n-tag>
              </n-form-item>
            </div>
            <div v-if="declareType === DeclarationType.PROJECT">
              <n-h4 prefix="bar"><strong>立项申报签字</strong></n-h4>
              <n-form-item label="立项申报签字" path="prjSign">
                <n-image
                  v-show="declareForm._sign.flag"
                  :width="120"
                  :height="60"
                  :src="declareForm._sign.url"
                  :preview-disabled="true"
                />
                <n-tag
                  size="small"
                  type="error"
                  style="cursor: pointer; margin-left: 10px"
                  @click="declareForm._sign.show = true"
                >
                  {{ declareForm._sign.flag ? '重新签名' : '点击签名' }}
                </n-tag>
              </n-form-item>
            </div>
          </n-form>
          <template #footer>
            <n-flex justify="end">
              <n-button @click="declareVisible = false">取消</n-button>
              <n-button
                type="info"
                :disabled="!declareForm._projectPromise || !declareForm._sign.flag"
                :loading="loading.confirmDeclare"
                @click="confirmDeclaration"
              >
                确认申报
              </n-button>
            </n-flex>
          </template>
        </n-drawer-content>
      </n-drawer>
      <j-modal v-model:show="declareForm._sign.show" width="40%" :show-footer="false" title="签名">
        <j-sign v-show="declareForm._sign.show" :canvas-width="620" @done="declareForm._sign.done" />
      </j-modal>

      <n-modal v-model:show="ethicsVisible" title="伦理审查批件上传" preset="card" style="width: 60%">
        <n-form ref="ethicsFormRef" :model="ethicsForm" :rules="ethicsFormRules" :show-label="false">
          <n-form-item label="伦理审查批件" path="ethicsAttachments">
            <yt-upload
              v-model:file-list="ethicsForm.ethicsAttachments"
              :upload-fn="uploadRmsFile"
              accept=".pdf,.doc,.docx,.xls,.xlsx,.zip,.rar,.7z,image/*"
              :maxSize="5 * 1024"
              auto-upload
              drag
            />
          </n-form-item>
        </n-form>
        <template #action>
          <n-flex justify="end" :size="12">
            <n-button @click="ethicsVisible = false">取消</n-button>
            <n-button :loading="loading.submitEthics" type="primary" @click="handleSubmitEthics">提交</n-button>
          </n-flex>
        </template>
      </n-modal>
    </template>
  </j-crud>
  <n-drawer v-model:show="showNotificationDrawer" width="400" placement="left">
    <n-drawer-content title="申报通知">
      <n-timeline>
        <n-timeline-item type="success" title="2023年度科研项目申报开始" time="2023-05-01">
          <n-text>
            请各科室负责人注意，2023年度科研项目申报工作已经开始，请按照要求准备相关材料并在截止日期前提交。
          </n-text>
        </n-timeline-item>
        <n-timeline-item type="warning" title="申报截止日期提醒" time="2023-05-15">
          <n-text> 距离申报截止日期还有两周时间，请尚未提交的科室抓紧时间完成申报工作。 </n-text>
        </n-timeline-item>
        <n-timeline-item type="info" title="申报材料要求更新" time="2023-05-20">
          <n-text> 根据最新要求，申报材料需要增加项目预期成果的详细说明，请各申报人员注意补充。 </n-text>
        </n-timeline-item>
        <n-timeline-item type="error" title="申报截止" time="2023-06-01">
          <n-text> 今日为申报截止日期，请确保所有材料已提交完毕。 </n-text>
        </n-timeline-item>
      </n-timeline>
    </n-drawer-content>
  </n-drawer>
</template>
<script lang="ts" setup>
  // import { CRUD as JCrud } from '@jcomponents'
  import JCrud from '@/components/common/crud/index.vue'

  import { CRUDColumnInterface, JTab } from '@/types/comps/crud'
  import { Component, computed, h, onMounted, ref, useTemplateRef, nextTick } from 'vue'
  import { queryRmsResearchTopic } from '@/api/rms/topicManager/ResearchTopicWeb'
  import { useRoute } from 'vue-router'
  import {
    NButton,
    NFlex,
    NIcon,
    NTag,
    NDrawer,
    NDrawerContent,
    NTimeline,
    NTimelineItem,
    NText,
    NSpace,
    NPopover,
    NCard,
    NTooltip,
    FormInst,
  } from 'naive-ui'
  import {
    deleteRmsProjectMainInfo,
    pageQueryRmsProjectMainInfo,
    projectDeclare,
    submitEthics,
  } from '@/api/rms/projectApply/ProjectMainInfoWeb'
  import {
    getLabelByValueOfOptions,
    getOptionsOfSelections,
    queryRmsCheckBoxSelection,
  } from '@/api/rms/config/CheckBoxSelectionWeb'
  import JPGlobal from '@jutil'
  import { Option } from '@jtypes'
  import ProcessInstanceDetailModal from '@/views/modules/bpm/processInstance/detail/processDetailModal.vue'
  import ProjectDetail from '@/views/modules/rms/projectApplication/components/projectDetail.vue'
  import {
    DeclarationValidateKey,
    ProjectDetailInst,
    ProjectOperationType,
  } from '@/types/modules/rms/project-declare/projectDetail.ts'
  import { getRangeShortcuts } from '@/utils'
  import {
    BaseProjectSearchParam,
    FormValidateRule,
    RecursiveRule,
    RmsBaseAuditStatus,
    RmsOptions,
  } from '@/types/modules/rms'
  import { queryRmsIntendedUnit } from '@/api/rms/config/IntendedUnitWeb.ts'
  import { RmsProjectVO } from '@/types/modules/rms/project-management'
  import { Delete, Edit, Promotion } from '@element-plus/icons-vue'
  import axios from 'axios'
  import { RmsIntendedUnit, RmsIntendedUnitSpecial, RmsResearchTopic } from '@/types/modules/rms/entity/inex.ts'
  import { queryRmsIntendedUnitSpecial } from '@/api/rms/config/IntendedUnitSpecialWeb.ts'
  import { ConstructOutline, DocumentTextOutline, NotificationsOutline, PersonOutline } from '@vicons/ionicons5'
  import { generateRmsBaseAuditStatusTag } from '@/utils/rms'
  import { uploadRmsFile } from '@/api/rms/common/FileWeb.ts'
  import YtUpload from '@/views/modules/rms/components/upload/index.vue'
  import { ProjectDeclare } from '@/types/modules/rms/project-declare'
  import { Naive } from '@/types/naive'
  import UploadProjectFileToApply from './components/uploadProjectFileToApply.vue'
  import { AnchorItem, Attachment, RecursiveRules} from '@/types/modules/rms'

  let crudRef = ref<InstanceType<typeof JCrud>>(null)
  const route = useRoute()

  /**
   * 加载状态
   *
   */
  const loading = ref<{
    // 新建项目
    createProject: boolean
    // 修改项目
    editProject: boolean[]
    // 筹备申报
    preDeclare: boolean[]
    // 立项申报
    prjDeclare: boolean[]
    // 删除项目
    deleteProject: boolean[]
    // 确认创建项目
    confirmCreate: boolean
    // 确认申报项目
    confirmDeclare: boolean
    // 伦理审查批件提交
    submitEthics: boolean
    // 提交任务书
    submitBrief: boolean
  }>({
    createProject: false,
    editProject: [],
    preDeclare: [],
    prjDeclare: [],
    deleteProject: [],
    confirmCreate: false,
    confirmDeclare: false,
    submitEthics: false,
    submitBrief: false,
  })

  /**
   * 处理查询完成
   * @param val
   */
  const handleQueryComplete = (val: RmsProjectVO[]) => {
    // 根据val的长度初始化 loading.value
    loading.value.editProject = new Array(val.length).fill(false)
    loading.value.preDeclare = new Array(val.length).fill(false)
    loading.value.prjDeclare = new Array(val.length).fill(false)
    loading.value.deleteProject = new Array(val.length).fill(false)
  }

  const handleSubmitFill = async () => {
    loading.value.submitBrief = true
    const result = await projectDetailRef.value?.handleSubmitBrief()
    loading.value.submitBrief = false
    if (result) {
      projectDetailActive.value = false
      crudRef.value?.queryData()
    }
  }

  // 项目详情表单
  const projectDetailRef = ref<ProjectDetailInst | null>(null)

  // 项目详情表单显示
  const projectDetailActive = ref(false)

  // 项目详情表单类型
  const projectDetailType = ref<ProjectOperationType>(ProjectOperationType.ADD)

  // 项目详情表单标题
  const projectDetailTitle = computed(() => {
    switch (projectDetailType.value) {
      case ProjectOperationType.ADD:
        return '新建项目'
      case ProjectOperationType.EDIT:
        return '修改项目'
      case ProjectOperationType.FILL_BRIEF:
        return '任务书填报'
      case ProjectOperationType.DETAILS:
        return '项目详情'
      case ProjectOperationType.APPLY_2023:
        return '23年项目申报'
      default:
        return ''
    }
  })

  // 当前行项目ID
  const rowId = ref<number | null>(null)

  // 当前行索引
  const currentIndex = ref(-1)

  /**
   * 处理项目详情表单关闭后
   */
  const handlePrjDetailAfterLeave = () => {
    loading.value.createProject = false
    loading.value.editProject[currentIndex.value] = false
    projectDetailReadonly.value = false
    rowId.value = null
    isProject2023.value = false
    crudRef.value?.queryData()
  }

  /**
   * 处理项目申报表单关闭后
   */
  const handleDeclareAfterLeave = () => {
    loading.value.preDeclare[currentIndex.value] = false
    loading.value.prjDeclare[currentIndex.value] = false
    resetDeclarationPromise()
    isProject2023.value = false
    crudRef.value?.queryData()
  }

  /**
   * 显示新建项目
   */
  const handleShowNewBuild = () => {
    loading.value.createProject = true
    projectDetailActive.value = true
    projectDetailType.value = ProjectOperationType.ADD
    rowId.value = null
    isProject2023.value = false

    // 在下一个事件循环中设置 projectApplyType
    nextTick(() => {
      if (projectDetailRef.value) {
        projectDetailRef.value.saveForm.projectApplyType = '0'
      }
    })
  }

  /**
   * 重置项目申报承诺信息
   */
  const resetDeclarationPromise = () => {
    declareForm.value._projectPromise = false
    declareForm.value._sign.reset()
  }

  interface Sign {
    // 展示签字框
    show: boolean
    // 签字标记
    flag: boolean
    // 签字url
    url: string | null
    // 签字完成回调
    readonly done: (signPath: string) => void
    // 重置签字数据
    readonly reset: () => void
  }

  /**
   * 项目申报表单类型
   */
  interface DeclarationForm {
    // 项目ID
    id: number | null
    // 流程定义标识
    processDefinitionKey: string | null
    // 立项申报意向单位ID
    intendedUnitId?: number | null
    intendedUnitOther?: string | null
    intendedUnitSpecialId?: number | null
    intendedUnitSpecialOther?: string | null
    // 项目承诺(仅用于页面显示)
    _projectPromise: boolean
    // 签名(仅用于页面显示)
    _sign: Sign
    // 储备申报签名
    preSign?: string | null
    // 立项申报签名
    prjSign?: string | null
    // 技术查新附件
    techQueryAttachment?: Attachment[]
    // 伦理审批附件
    ethicsApprovalAttachment?: Attachment[]
    // 申报书备案附件
    applicationDeclarationAttachment?: Attachment[]
    // 承诺书附件
    promiseAttachment?: Attachment[]
  }

  // 项目申报表单校验规则类型
  type DeclarationFormRules = {
    [key in keyof DeclarationForm]?: FormValidateRule
  }

  // 项目申报表单
  const declareFormRef = ref<FormInst | null>(null)

  // 项目申报表单数据
  const declareForm = ref<DeclarationForm>({
    id: null,
    processDefinitionKey: null,
    intendedUnitId: null,
    _projectPromise: false,
    _sign: {
      show: false,
      flag: false,
      url: null,
      done: (signPath: string) => {
        declareForm.value._sign.show = false
        declareForm.value._sign.flag = true
        declareForm.value._sign.url = JPGlobal.getRealOCUrl(signPath)
        if (declareType.value === DeclarationType.PREPARATION) {
          declareForm.value.preSign = signPath
        } else {
          declareForm.value.prjSign = signPath
        }
      },
      reset: () => {
        declareForm.value._sign.show = false
        declareForm.value._sign.flag = false
        declareForm.value._sign.url = null
      },
    },
    preSign: null,
    prjSign: null,
    techQueryAttachment: [],
    ethicsApprovalAttachment: [],
    applicationDeclarationAttachment: [],
    promiseAttachment: [],
  })

  // 项目申报表单校验规则
  const declareFormRules: DeclarationFormRules = {
    intendedUnitId: [{ required: true, message: '请选择意向单位', trigger: ['blur', 'change'], type: 'number' }],
    intendedUnitOther: [{ required: true, message: '请输入其他意向单位', trigger: ['blur', 'input'] }],
    intendedUnitSpecialId: [{ required: true, message: '请选择科研专项', trigger: ['blur', 'change'], type: 'number' }],
    intendedUnitSpecialOther: [{ required: true, message: '请输入其他科研专项', trigger: ['blur', 'input'] }],
    _projectPromise: [{ required: true, message: '请同意项目申报承诺', trigger: ['blur', 'change'], type: 'boolean' }],
    preSign: [{ required: true, message: '请签名', trigger: ['blur', 'change'] }],
    prjSign: [{ required: true, message: '请签名', trigger: ['blur', 'change'] }],
  }

  // 储备详情表单
  // const preparationDetailRef = ref<ProjectDetailInst | null>(null)
  const preparationDetailRef = useTemplateRef<ProjectDetailInst | null>('preparationDetailRef')

  /**
   * 确认申报
   */
  const confirmDeclaration = async () => {
    loading.value.confirmDeclare = true

    // 先保存草稿
    try {
      const saveDraftResult = await preparationDetailRef.value?.saveDraft()
      if (!saveDraftResult) {
        loading.value.confirmDeclare = false
        window.$message.error('保存草稿失败，请检查填写内容')
        return
      }
    } catch (e) {
      loading.value.confirmDeclare = false
      window.$message.error('保存草稿时出现错误')
      return
    }

    // 项目表单数据校验
    let valid = await preparationDetailRef.value?.validateForm([
      DeclarationValidateKey.DRAFT,
      DeclarationValidateKey.PREPARATION,
    ])
    if (!valid) {
      loading.value.confirmDeclare = false
      return
    }
    // 申报表单数据校验
    declareFormRef.value?.validate(errors => {
      valid = !errors
    })
    if (!valid) {
      loading.value.confirmDeclare = false
      return
    }
    // 合并附件数据
    const projectDetailData = preparationDetailRef.value?.saveForm
    if (projectDetailData) {
      declareForm.value.techQueryAttachment = projectDetailData.techQueryAttachment
      declareForm.value.ethicsApprovalAttachment = projectDetailData.ethicsApprovalAttachment
      declareForm.value.applicationDeclarationAttachment = projectDetailData.applicationDeclarationAttachment
      declareForm.value.promiseAttachment = projectDetailData.promiseAttachment
    }
    // 校验通过
    declareForm.value.id = rowId.value
    if (declareType.value === DeclarationType.PREPARATION) {
      declareForm.value.processDefinitionKey = 'RMS_PRJ_PRE_DECLARE'
    } else if (declareType.value === DeclarationType.PROJECT) {
      declareForm.value.processDefinitionKey = 'RMS_PRJ_PRJ_DECLARE'
    }
    //更改项目状态
    try {
      const { code, message } = await projectDeclare(declareForm.value)
      loading.value.confirmDeclare = false
      if (code === 200) {
        //弹出消息
        window.$message.success('项目申报成功')
        //关闭申报窗口
        declareVisible.value = false
        // 重置申报承诺信息
        resetDeclarationPromise()
        // 刷新列表
        crudRef.value.queryData()
      } else {
        window.$message.error(message)
      }
    } catch (e) {
      loading.value.confirmDeclare = false
    }
  }

  // 申报抽屉显示
  const declareVisible = ref(false)

  // 申报类型
  enum DeclarationType {
    // 储备申报
    PREPARATION = '1',
    // 立项申报
    PROJECT = '2',
  }

  // 当前申报类型
  const declareType = ref<string>(DeclarationType.PREPARATION)

  /**
   * 显示申报窗口
   * @param type 申报类型
   * @param id 项目ID
   * @param is2023Project 是否是23年项目
   */
  const showDeclareDrawer = (type: DeclarationType, id: number, is2023Project: boolean = false) => {
    if (type === DeclarationType.PREPARATION) {
      projectDetailType.value = ProjectOperationType.PREPARATION_DECLARATION
    } else if (type === DeclarationType.PROJECT) {
      projectDetailType.value = ProjectOperationType.PROJECT_DECLARATION
    }
    declareVisible.value = true
    declareType.value = type
    rowId.value = id
    isProject2023.value = is2023Project
  }

  // 流程实例详情抽屉显示
  const processVisible = ref(false)

  // 流程实例编号
  const processInstanceCode = ref<string>(null)

  /**
   * 显示流程详情
   * @param code 流程实例编码
   */
  const showProcessDetail = (code: string) => {
    processInstanceCode.value = code
    processVisible.value = true
  }

  interface QueryForm extends BaseProjectSearchParam {
    /**
     * 筛选条件：申报状态
     * 1 筹备项目：未完成筹备流程的项目
     * 2 筹备通过项目以及未完成立项流程的项目
     * 3 筹备未通过项目
     * 4 立项通过项目
     * 5 立项未通过项目
     */
    declareStatus?: string | null
    /**
     * 项目申报类型
     * 0 常规项目申报
     * 1 23年项目申报
     */
    projectApplyType?: string | null
  }

  // 查询表单
  const queryForm = ref<QueryForm>({
    projectName: null,
    projectStartDate: null,
    projectEndDate: null,
    projectLevel: null,
    topicCategory: null,
    researchType: null,
    externalCooperation: null,
    innovationType: null,
    startTime: null,
    endTime: null,
    declareStatus: null,
    projectApplyType: null,
  })

  /**
   * tab 类型
   */
  enum TAB_TYPE {
    // 储备项目
    PRE_PRJ = '0',
    // 储备通过
    PRE_PASSED = '1',
    // 储备驳回
    PRE_FAILED = '2',
    // 立项通过
    PRJ_PASSED = '3',
    // 立项驳回
    PRJ_FAILED = '4',
  }

  enum AuditLevelName {
    PRE_DECLARANT = '申报人储备',
    PRE_SES_SELECT = '科教科预审',
    PRE_REVIEWERS_AUDIT = '专家评审',
    PRE_SES_AUDIT = '科教科评审',
    PRJ_DECLARANT = '申报人立项',
    PRJ_SES_SELECT = '科教科预审',
    PRJ_REVIEWERS_AUDIT = '专家评审',
    PRJ_SES_AUDIT = '科教科评审',
    PRJ_INTENDED_UNIT_AUDIT = '意向单位评审',
  }

  interface AuditLevel {
    level: string
    name: string
  }

  enum AuditState {
    PASSED = '1',
    REJECTED = '2',
  }

  interface AuditStatus {
    applyStatus: AuditLevel
    chkState?: string
  }

  const auditStatusOptions: AuditStatus[] = [
    {
      applyStatus: {
        level: '0',
        name: AuditLevelName.PRE_DECLARANT,
      },
      chkState: AuditState.PASSED,
    },
    {
      applyStatus: {
        level: '0',
        name: AuditLevelName.PRE_DECLARANT,
      },
      chkState: AuditState.REJECTED,
    },
    {
      applyStatus: {
        level: '1',
        name: AuditLevelName.PRE_SES_SELECT,
      },
      chkState: AuditState.PASSED,
    },
    {
      applyStatus: {
        level: '1',
        name: AuditLevelName.PRE_SES_SELECT,
      },
      chkState: AuditState.REJECTED,
    },
    {
      applyStatus: {
        level: '2',
        name: AuditLevelName.PRE_REVIEWERS_AUDIT,
      },
      chkState: AuditState.PASSED,
    },
    {
      applyStatus: {
        level: '2',
        name: AuditLevelName.PRE_REVIEWERS_AUDIT,
      },
      chkState: AuditState.REJECTED,
    },
    {
      applyStatus: {
        level: '3',
        name: AuditLevelName.PRE_SES_AUDIT,
      },
      chkState: AuditState.PASSED,
    },
    {
      applyStatus: {
        level: '3',
        name: AuditLevelName.PRE_SES_AUDIT,
      },
      chkState: AuditState.REJECTED,
    },
    {
      applyStatus: {
        level: '4',
        name: AuditLevelName.PRJ_DECLARANT,
      },
      chkState: AuditState.PASSED,
    },
    {
      applyStatus: {
        level: '4',
        name: AuditLevelName.PRJ_DECLARANT,
      },
      chkState: AuditState.REJECTED,
    },
    {
      applyStatus: {
        level: '5',
        name: AuditLevelName.PRJ_SES_SELECT,
      },
      chkState: AuditState.PASSED,
    },
    {
      applyStatus: {
        level: '5',
        name: AuditLevelName.PRJ_SES_SELECT,
      },
      chkState: AuditState.REJECTED,
    },
    {
      applyStatus: {
        level: '6',
        name: AuditLevelName.PRJ_REVIEWERS_AUDIT,
      },
      chkState: AuditState.PASSED,
    },
    {
      applyStatus: {
        level: '6',
        name: AuditLevelName.PRJ_REVIEWERS_AUDIT,
      },
      chkState: AuditState.REJECTED,
    },
    {
      applyStatus: {
        level: '7',
        name: AuditLevelName.PRJ_SES_AUDIT,
      },
      chkState: AuditState.PASSED,
    },
    {
      applyStatus: {
        level: '7',
        name: AuditLevelName.PRJ_SES_AUDIT,
      },
      chkState: AuditState.REJECTED,
    },
    {
      applyStatus: {
        level: '8',
        name: AuditLevelName.PRJ_INTENDED_UNIT_AUDIT,
      },
      chkState: AuditState.PASSED,
    },
    {
      applyStatus: {
        level: '8',
        name: AuditLevelName.PRJ_INTENDED_UNIT_AUDIT,
      },
      chkState: AuditState.REJECTED,
    },
  ]

  const getAuditStatusTag = (applyStatus: string, chkState: string, callback?: () => void) => {
    for (let auditStatusOption of auditStatusOptions) {
      if (auditStatusOption.applyStatus.level === applyStatus && auditStatusOption.chkState === chkState) {
        if (chkState === AuditState.PASSED) {
          return h(
            NTag,
            {
              type: 'success',
              size: 'small',
              style: {
                cursor: callback ? 'pointer' : 'default',
              },
              onClick: callback,
            },
            {
              default: () => auditStatusOption.applyStatus.name + '通过',
            }
          )
        } else if (chkState === AuditState.REJECTED) {
          return h(
            NTag,
            {
              type: 'error',
              size: 'small',
              style: {
                cursor: callback ? 'pointer' : 'default',
              },
              onClick: callback,
            },
            {
              default: () => auditStatusOption.applyStatus.name + '拒绝',
            }
          )
        }
      }
    }
  }

  const ethicsVisible = ref(false)

  const ethicsFormRef = ref<FormInst | null>(null)

  const ethicsForm = ref<ProjectDeclare.EthicsForm>({
    id: null,
    ethicsAttachments: [],
  })

  const ethicsFormRules: RecursiveRule<ProjectDeclare.EthicsForm> = {
    ethicsAttachments: [{ required: true, message: '请上传伦理审查批件', trigger: ['blur', 'change'], type: 'array' }],
  }


  //TODO: 立项通过后的伦理附件上传
  const handleSubmitEthics = () => {
    console.log(ethicsForm.value)
    loading.value.submitEthics = true
    ethicsFormRef.value?.validate(async errors => {
      if (!errors) {
        try {
          await submitEthics(ethicsForm.value)
          window.$message.success('操作成功')
          ethicsVisible.value = false
          crudRef.value?.queryData()
        } catch (e) {
          console.log(e)
        } finally {
          loading.value.submitEthics = false
        }
      } else {
        loading.value.submitEthics = false
      }
    })
  }

  /**
   * 获取 tab columns
   * @param tabName tab名
   */
  const getColumns = (tabName: string) => {
    let base: CRUDColumnInterface[] = [
      {
        title: '#',
        key: 'index',
        width: 50,
        fixed: 'left',
        align: 'center',
      },
      {
        title: '项目名称',
        key: 'projectName',
        minWidth: 200,
      },
      {
        title: '项目负责人',
        key: 'projectLeader',
        minWidth: 120,
      },
      {
        title: '联系电话',
        key: 'telephone',
        minWidth: 120,
        // 手机号脱敏
        render: (row: any) => {
          return row.telephone ? row.telephone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') : null
        },
      },
      {
        title: '申报级别',
        key: 'projectLevel',
        minWidth: 100,
        render: (row: any) => {
          return getLabelByValueOfOptions(options.value.PROJECT_LEVEL, row.projectLevel)
        },
      },
      {
        title: '课题类别',
        key: 'topicCategory',
        minWidth: 100,
        render: (row: any) => {
          return getLabelByValueOfOptions(options.value.TOPIC_CATEGORY, row.topicCategory)
        },
      },
      {
        title: '研究类型',
        key: 'researchType',
        minWidth: 100,
        render: (row: any) => {
          return getLabelByValueOfOptions(options.value.RESEARCH_TYPE, row.researchType)
        },
      },
      {
        title: '与外单位合作',
        key: 'externalCooperation',
        minWidth: 120,
        render: (row: any) => {
          return getLabelByValueOfOptions(options.value.EXTERNAL_COOPERATION, row.externalCooperation)
        },
      },
      {
        title: '项目起始日期',
        key: 'projectStartDate',
        minWidth: 120,
        sorter: 'default',
      },
      {
        title: '项目结束日期',
        key: 'projectEndDate',
        minWidth: 120,
        sorter: 'default',
      },
      {
        title: '提交时间',
        key: 'submitDate',
        width: 150,
        align: 'center',
        sorter: 'default',
      },
    ]
    if (tabName === TAB_TYPE.PRE_PRJ || tabName === TAB_TYPE.PRE_FAILED) {
      base.push({
        title: '储备审核状态',
        key: 'preAuditStatus',
        width: 140,
        fixed: 'right',
        align: 'center',
        render: (row: any) => {
          //applyStatus 0 申报人储备
          if (row.applyStatus === '0') {
            if (row.chkState === '1') { //chkState 1 申报了还没审批完成
              return h(
                NTag,
                {
                  type: 'warning',
                  size: 'small',
                  style: {
                    cursor: 'pointer',
                  },
                  onClick: () => showProcessDetail(row.preProcessInstanceCode),
                },
                () => '待审批'
              )
            }
            return h(NTag, { size: 'small' }, () => '筹备中待申报')
          }
          return getAuditStatusTag(row.applyStatus, row.chkState, () => {
            console.log('show pre process detail')
            showProcessDetail(row.preProcessInstanceCode)
          })
        },
      })
    }
    if (tabName === TAB_TYPE.PRE_PASSED || tabName === TAB_TYPE.PRJ_PASSED || tabName === TAB_TYPE.PRJ_FAILED) {
      base.push({
        title: '立项审核状态',
        key: 'prjAuditStatus',
        width: 140,
        fixed: 'right',
        align: 'center',
        render: (row: any) => {
          if (row.applyStatus === '3') {
            return h(NTag, { size: 'small' }, () => '未申报')
          }
          if (row.applyStatus === '4' && row.chkState === '1') {
            return h(
              NTag,
              {
                type: 'warning',
                size: 'small',
                style: {
                  cursor: 'pointer',
                },
                onClick: () => showProcessDetail(row.prjProcessInstanceCode),
              },
              () => '待审核'
            )
          }
          return getAuditStatusTag(row.applyStatus, row.chkState, () => showProcessDetail(row.prjProcessInstanceCode))
        },
      })
    }
    if (tabName === TAB_TYPE.PRJ_PASSED) {
      base.push(
        {
          title: '立项确认状态',
          key: 'prjApproved',
          width: 120,
          fixed: 'right',
          align: 'center',
          render: (row: RmsProjectVO) => {
            if (row.prjApproved) {
              return h(
                NTooltip,
                {
                  trigger: 'hover',
                },
                {
                  default: () => `确定时间: ${row.prjApprovedTime}`,
                  trigger: () =>
                    h(NTag, { size: 'small', type: 'success', style: 'cursor: pointer; ' }, () => '已确认'),
                }
              )
            } else {
              return h(NTag, { size: 'small', type: 'warning' }, () => '未确认')
            }
          },
        },
        {
          title: '伦理审批状态',
          key: 'ethicsAuditStatus',
          width: 120,
          fixed: 'right',
          align: 'center',
          render: (row: RmsProjectVO) => {
            const size: Naive.ButtonSize = 'small'
            if (!row.prjApproved) {
              return h(NTag, { size }, () => '待确认')
            }
            return generateRmsBaseAuditStatusTag(row.ethicsAuditStatus, {
              size,
              onClick:
                row.ethicsAuditStatus === RmsBaseAuditStatus.UNSUBMITTED
                  ? undefined
                  : () => showProcessDetail(row.ethicsProcessInstanceCode),
            })
          },
        },
        {
          title: '任务书状态',
          key: 'briefAuditStatus',
          width: 120,
          fixed: 'right',
          align: 'center',
          render: (row: RmsProjectVO) => {
            const size: Naive.ButtonSize = 'small'
            if (!row.prjApproved) {
              return h(NTag, { size }, () => '待确认')
            }
            if (row.ethicsAuditStatus === RmsBaseAuditStatus.UNSUBMITTED) {
              return h(NTag, { size }, () => '待伦理审批')
            }
            if (
              row.briefAuditStatus === RmsBaseAuditStatus.UNSUBMITTED &&
              Date.now() - new Date(row.prjApprovedTime).getTime() > 7 * 24 * 60 * 60 * 1000
            ) {
              return h(NTag, { size, type: 'warning' }, () => '已超时')
            }
            return generateRmsBaseAuditStatusTag(row.briefAuditStatus, {
              size,
              onClick:
                row.briefAuditStatus === RmsBaseAuditStatus.UNSUBMITTED
                  ? undefined
                  : () => showProcessDetail(row.briefProcessInstanceCode),
            })
          },
        }
      )
    }
    base.push({
      title: '操作',
      key: 'action',
      width: 240,
      fixed: 'right',
      align: 'center',
      render: (row: RmsProjectVO, index: number) => {
        // h(
        //   NFlex,
        //   {
        //     vertical: true,
        //   },
        //   () => {
        switch (currentTabName.value) {
          case TAB_TYPE.PRE_PRJ:
            if (!preOptDisabled({ applyStatus: row.applyStatus, chkState: row.chkState })) {
              return [
                genActionBtn(
                  'info',
                  '修改',
                  () => {
                    currentIndex.value = index
                    loading.value.editProject[index] = true
                    projectDetailActive.value = true
                    projectDetailType.value = ProjectOperationType.EDIT
                    rowId.value = row.id
                    isProject2023.value = row.projectApplyType === '1'
                  },
                  Edit,
                  loading.value.editProject[index]
                ),
                genActionBtn(
                  'primary',
                  '储备申报',
                  () => {
                    currentIndex.value = index
                    loading.value.preDeclare[index] = true
                    showDeclareDrawer(DeclarationType.PREPARATION, row.id, row.projectApplyType === '1')
                  },
                  Promotion,
                  loading.value.preDeclare[index]
                ),
                genActionBtn(
                  'error',
                  '删除',
                  () => {
                    currentIndex.value = index
                    loading.value.deleteProject[index] = true
                    window.$dialog.warning({
                      title: '警告',
                      content: '确定删除当前筹划项目？',
                      positiveText: '确定',
                      negativeText: '取消',
                      onPositiveClick: async () => {
                        const { code, message } = await deleteRmsProjectMainInfo(row)
                        if (code === 200) {
                          window.$message.success('删除成功')
                          // 刷新列表数据
                          crudRef.value.queryData()
                        } else {
                          window.$message.error(message)
                        }
                        loading.value.deleteProject[index] = false
                      },
                      onNegativeClick: () => {
                        loading.value.deleteProject[index] = false
                      },
                      onAfterLeave: () => {
                        loading.value.deleteProject[index] = false
                      },
                    })
                  },
                  Delete,
                  loading.value.deleteProject[index]
                ),
              ]
            }
            break
          case TAB_TYPE.PRE_PASSED:
            if (!prjOptDisabled({ applyStatus: row.applyStatus, chkState: row.chkState })) {
              return [
                genActionBtn(
                  'primary',
                  '立项申报',
                  () => {
                    currentIndex.value = index
                    loading.value.prjDeclare[index] = true
                    showDeclareDrawer(DeclarationType.PROJECT, row.id, row.projectApplyType === '1')
                  },
                  Promotion,
                  loading.value.prjDeclare[index]
                ),
              ]
            }
            break
          case TAB_TYPE.PRE_FAILED:
            break
          case TAB_TYPE.PRJ_PASSED:
            if (
              (row.prjApproved && row.ethicsAuditStatus === RmsBaseAuditStatus.UNSUBMITTED) ||
              row.ethicsAuditStatus === RmsBaseAuditStatus.REJECTED ||
              row.ethicsAuditStatus === RmsBaseAuditStatus.CANCELLED
            ) {
              return genActionBtn(
                'primary',
                '伦理审查批件',
                () => {
                  ethicsForm.value = {
                    id: row.id,
                    ethicsAttachments: row.ethicsAttachments || [],
                  }
                  ethicsVisible.value = true
                  isProject2023.value = row.projectApplyType === '1'
                },
                ConstructOutline
              )
            } else if (
              (row.prjApproved &&
                Date.now() - new Date(row.prjApprovedTime).getTime() < 7 * 24 * 60 * 60 * 1000 &&
                row.briefAuditStatus === RmsBaseAuditStatus.UNSUBMITTED) ||
              row.briefAuditStatus === RmsBaseAuditStatus.REJECTED ||
              row.briefAuditStatus === RmsBaseAuditStatus.CANCELLED
            ) {
              return genActionBtn(
                'primary',
                '任务书填报',
                () => {
                  currentIndex.value = index
                  // loading.value.fillDeclaration[index] = true
                  projectDetailActive.value = true
                  projectDetailReadonly.value = true
                  projectDetailType.value = ProjectOperationType.FILL_BRIEF
                  rowId.value = row.id
                  isProject2023.value = row.projectApplyType === '1'
                },
                DocumentTextOutline
              )
            }
            break
          case TAB_TYPE.PRJ_FAILED:
            break
        }
        //   }
        // ),
      },
    })
    return base
  }

  /**
   * 生成一个操作按钮
   * @param type 按钮类型
   * @param label 按钮标签
   * @param onClick 按钮点击事件
   * @param icon 按钮图标
   * @param loading 按钮加载状态
   */
  const genActionBtn = (
    type: Naive.ButtonType,
    label: string,
    onClick: () => void,
    icon: Component,
    loading?: boolean
  ) => {
    return h(
      NButton,
      {
        type,
        text: true,
        loading,
        onClick,
        renderIcon: () =>
          h(NIcon, {
            component: icon,
          }),
      },
      () => label
    )
  }

  // 当前 tab
  const currentTabName = ref<string>(TAB_TYPE.PRE_PRJ)

  // tab 切换
  const tabChange = (tab: JTab) => {
    currentTabName.value = tab.name
    switch (tab.name) {
      case TAB_TYPE.PRE_PRJ:
        queryForm.value.declareStatus = '1'
        break
      case TAB_TYPE.PRE_PASSED:
        queryForm.value.declareStatus = '2'
        break
      case TAB_TYPE.PRE_FAILED:
        queryForm.value.declareStatus = '3'
        break
      case TAB_TYPE.PRJ_PASSED:
        queryForm.value.declareStatus = '4'
        break
      case TAB_TYPE.PRJ_FAILED:
        queryForm.value.declareStatus = '5'
        break
    }
  }

  const tabs = ref<JTab[]>([
    {
      name: TAB_TYPE.PRE_PRJ,
      tab: '储备项目',
      useBadge: true,
      columns: getColumns(TAB_TYPE.PRE_PRJ),
      tabChange: tabChange,
    },
    {
      name: TAB_TYPE.PRE_PASSED,
      tab: '储备通过',
      useBadge: true,
      columns: getColumns(TAB_TYPE.PRE_PASSED),
      tabChange: tabChange,
    },
    {
      name: TAB_TYPE.PRE_FAILED,
      tab: '储备未通过',
      useBadge: true,
      columns: getColumns(TAB_TYPE.PRE_FAILED),
      tabChange: tabChange,
    },
    {
      name: TAB_TYPE.PRJ_PASSED,
      tab: '立项通过',
      useBadge: true,
      columns: getColumns(TAB_TYPE.PRJ_PASSED),
      tabChange: tabChange,
    },
    {
      name: TAB_TYPE.PRJ_FAILED,
      tab: '立项未通过',
      useBadge: true,
      columns: getColumns(TAB_TYPE.PRJ_FAILED),
      tabChange: tabChange,
    },
  ])

  // 项目详情是否只读
  const projectDetailReadonly = ref<boolean>(false)

  // 是否只读模式
  const readonly = ref<boolean>(false)

  /**
   * 禁用保存按钮
   */
  const disableSaveDraft = () => {
    // 禁用保存草稿功能的逻辑
  }

  /**
   * 启用保存按钮
   */
  const enableSaveDraft = () => {
    // 启用保存草稿功能的逻辑
  }

  /**
   * 储备项目tab页行操作按钮是否被禁用
   * @param row
   */
  const preOptDisabled = (row: { applyStatus: string; chkState: string }) => {
    return (row.applyStatus !== '0' && row.chkState !== '2') || (row.applyStatus === '0' && row.chkState === '1')
  }

  /**
   * 立项项目tab页行操作按钮是否被禁用
   * @param row
   */
  const prjOptDisabled = (row: { applyStatus: string; chkState: string }) => {
    return (row.applyStatus !== '3' && row.chkState !== '2') || (row.applyStatus === '4' && row.chkState === '1')
  }

  const createTimeRange = ref<[string, string]>()
  const createDateChange = (value: [string, string] | null) => {
    if (value != null) {
      queryForm.value.startTime = value[0]
      queryForm.value.endTime = value[1]
    } else {
      queryForm.value.startTime = null
      queryForm.value.endTime = null
    }
  }

  /**
   * 快捷时间范围
   */
  const rangeShortcut = {
    // 创建时间
    createTimeRange: getRangeShortcuts({
      displayOrder: ['past_year', 'past_half_year', 'past_month', 'past_week', 'this_week', 'this_month', 'this_year'],
      firstDayOfWeek: 'Monday',
    }),
  }

  const options = ref<RmsOptions>({
    PROJECT_LEVEL: [],
    TOPIC_CATEGORY: [],
    RESEARCH_TYPE: [],
    EXTERNAL_COOPERATION: [],
    INTENDED_UNIT: [],
    INTENDED_UNIT_SPECIAL: [],
    INNOVATION_TYPE: [],
  })

  /**
   * 初始化下拉框选项数据
   */
  const initOptions = async () => {
    try {
      const [{ data: topicCategories }, { data: intendedUnits }, { data: selections }] = await axios.all([
        queryRmsResearchTopic({}),
        queryRmsIntendedUnit(),
        queryRmsCheckBoxSelection(),
      ])
      options.value.TOPIC_CATEGORY = topicCategories?.map((item: RmsResearchTopic) => {
        return <Option>{
          id: item.id.toString(),
          label: item.name,
          value: item.id.toString(),
        }
      })
      options.value.INTENDED_UNIT = intendedUnits?.map((item: RmsIntendedUnit) => {
        return <Option>{
          id: item.id,
          label: item.unitName,
          value: item.id,
        }
      })
      options.value.INTENDED_UNIT.push({
        id: 0,
        label: '其他',
        value: 0,
      })
      options.value.PROJECT_LEVEL = getOptionsOfSelections(selections, 'PROJECT_LEVEL')
      options.value.RESEARCH_TYPE = getOptionsOfSelections(selections, 'RESEARCH_TYPE')
      options.value.EXTERNAL_COOPERATION = getOptionsOfSelections(selections, 'EXTERNAL_COOPERATION')
      options.value.INNOVATION_TYPE = getOptionsOfSelections(selections, 'INNOVATION_TYPE')
    } catch (e) {
      window.$message.error(e?.message ?? '获取字典数据失败')
    }
  }

  watchEffect(async () => {
    const { data } = await queryRmsIntendedUnitSpecial({ unitId: declareForm.value.intendedUnitId })
    options.value.INTENDED_UNIT_SPECIAL = data?.map((item: RmsIntendedUnitSpecial) => {
      return <Option>{
        id: item.id,
        label: item.name,
        value: item.id,
      }
    })
    options.value.INTENDED_UNIT_SPECIAL.push({
      id: 0,
      label: '其他',
      value: 0,
    })
  })

  // 页面初始化
  onMounted(() => {
    initOptions()
  })

  const handleShow2023ProjectApply = () => {
    // 打开23年项目申报抽屉
    projectDetailActive.value = true
    projectDetailType.value = ProjectOperationType.APPLY_2023
    projectDetailReadonly.value = false
    rowId.value = null
    isProject2023.value = true

    // 在下一个事件循环中设置 projectApplyType
    nextTick(() => {
      if (projectDetailRef.value) {
        projectDetailRef.value.saveForm.projectApplyType = '1'
      }
    })
  }

  // 在setup中添加showNotificationDrawer变量
  const showNotificationDrawer = ref(false)

  // 项目申报类型选项
  const projectApplyTypeOptions = ref<Option[]>([
    { label: '常规项目申报', value: '0' },
    { label: '23年项目申报', value: '1' },
  ])

  // 是否是23年项目申报
  const isProject2023 = ref<boolean>(false)
</script>

<script lang="ts">
  export default {
    name: 'index',
  }
</script>
<style scoped>
  .declaration-part-title {
    font-size: 20px;
    font-weight: bold;
  }
</style>
