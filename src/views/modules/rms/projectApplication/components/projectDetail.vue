<template>
  <div id="d0">
    <n-grid x-gap="20" :cols="5">
      <n-grid-item :span="4">
        <n-form
          ref="saveFormRef"
          size="large"
          :model="saveForm"
          :rules="saveFormRules"
          :show-label="false"
          :disabled="readonly"
        >
          <div>
            <!--            封面-->
            <div class="cover" id="d01">
              <n-descriptions
                bordered
                size="large"
                label-align="center"
                label-placement="left"
                label-class="descriptions-label"
                :column="2"
              >
                <template #header>
                  <h2 style="text-align: center">中江县人民医院</h2>
                  <h1 style="text-align: center">{{ showYear }}科研项目申报书</h1>
                </template>
                <n-descriptions-item label="项目名称" :span="2">
                  <n-form-item path="projectName">
                    <input
                      placeholder="请输入项目名称"
                      :disabled="operationType === ProjectOperationType.DETAILS"
                      class="nInput"
                      v-model="saveForm.projectName"
                    />
                  </n-form-item>
                </n-descriptions-item>
                <n-descriptions-item label="项目负责人" :span="2">
                  <n-form-item path="projectLeaderUsername">
                    <!--                    <input-->
                    <!--                      placeholder="请输入项目负责人"-->
                    <!--                      :disabled="readonly"-->
                    <!--                      class="nInput"-->
                    <!--                      v-model="saveForm.projectLeader"-->
                    <!--                      @change="handleUpdateProjectLeader"-->
                    <!--                    />-->
                    <j-bus-emp-search
                      :value="saveForm.projectLeaderUsername"
                      :disabled="operationType === ProjectOperationType.DETAILS"
                      @update:option="handleUpdateProjectLeader"
                    />
                  </n-form-item>
                </n-descriptions-item>
                <n-descriptions-item label="联系电话" :span="2">
                  <n-form-item path="telephone">
                    <input
                      placeholder="请输入联系电话"
                      :disabled="operationType === ProjectOperationType.DETAILS"
                      class="nInput"
                      v-model="saveForm.telephone"
                    />
                  </n-form-item>
                </n-descriptions-item>
                <n-descriptions-item label="申报级别" :span="2">
                  <n-form-item path="projectLevel">
                    <n-radio-group v-model:value="saveForm.projectLevel" :disabled="operationType === ProjectOperationType.DETAILS">
                      <n-flex justify="space-between">
                        <n-radio
                          v-for="item in options.PROJECT_LEVEL"
                          class="enlarged"
                          size="large"
                          :value="item.value"
                          :label="item.label"
                        />
                      </n-flex>
                    </n-radio-group>
                  </n-form-item>
                </n-descriptions-item>
                <n-descriptions-item label="课题类别" :span="2">
                  <n-radio-group v-model:value="saveForm.topicCategory" :disabled="operationType === ProjectOperationType.DETAILS">
                    <n-flex justify="space-between">
                      <n-radio
                        v-for="item in options.TOPIC_CATEGORY"
                        class="enlarged"
                        size="large"
                        :value="item.value"
                        :label="item.label"
                      />
                    </n-flex>
                  </n-radio-group>
                </n-descriptions-item>
                <n-descriptions-item label="研究类型" :span="2">
                  <n-radio-group v-model:value="saveForm.researchType" :disabled="operationType === ProjectOperationType.DETAILS">
                    <n-flex justify="space-between">
                      <n-radio
                        v-for="item in options.RESEARCH_TYPE"
                        class="enlarged"
                        size="large"
                        :value="item.value"
                        :label="item.label"
                      />
                    </n-flex>
                  </n-radio-group>
                </n-descriptions-item>
                <n-descriptions-item label="与外单位合作" :span="2">
                  <n-radio-group v-model:value="saveForm.externalCooperation" :disabled="operationType === ProjectOperationType.DETAILS">
                    <n-flex justify="space-between">
                      <n-radio
                        v-for="item in options.EXTERNAL_COOPERATION"
                        class="enlarged"
                        size="large"
                        :value="item.value"
                        :label="item.label"
                      />
                    </n-flex>
                  </n-radio-group>
                </n-descriptions-item>
                <n-descriptions-item label="起止时间">
                  <n-form-item path="projectDateRange">
                    <n-date-picker
                      class="enlarged widened"
                      type="daterange"
                      :disabled="operationType === ProjectOperationType.DETAILS"
                      :shortcuts="rangeShortcuts.projectDateRange"
                      :formatted-value="saveForm.projectDateRange"
                      @update-formatted-value="updateProjectDateRange"
                    />
                  </n-form-item>
                </n-descriptions-item>
              </n-descriptions>
              <h2 style="text-align: center; margin-top: 10%">中江县人民医院科教科制</h2>
              <h3 style="text-align: center">{{ showYearMonth }}</h3>
              <h2 style="text-align: center; margin-top: 15%; margin-bottom: 5%">填 报 说 明</h2>
              <ol style="padding: 20px; font-size: 14px">
                <li>
                  <p>
                    项目负责人填写项目申报书，应实事求是，表述明确。外来语要同时用原文和中文表达，第一次出现的缩略词，须注明全称。
                  </p>
                </li>
                <li>
                  <p>负责人必须填写&ldquo;申报级别&rdquo;及&ldquo;课题类别&rdquo;栏。</p>
                </li>
                <li>
                  <p>编写要求：</p>
                  <p>（1）项目申报目标定位准确，指标明确、可考核；</p>
                  <p>（2）项目任务明确，要充分考虑经济、技术等方面的可行性；</p>
                </li>
                <li>
                  <p>课题类型：自然科学类：用于临床科研研究；社会科学类：用于各种社会现象的研究。</p>
                </li>
              </ol>
            </div>
            <div style="display: flex; flex-direction: row" ref="containerRef">
              <div style="width: 100%; padding: 20px">
                <!--                项目信息表    -->
                <div class="declaration-part" id="d02">
                  <n-divider title-placement="center">
                    <span class="declaration-part-title">项目信息</span>
                  </n-divider>
                  <n-descriptions
                    bordered
                    label-align="center"
                    label-placement="left"
                    label-class="descriptions-label"
                    :column="8"
                  >
                    <n-descriptions-item label="创新类型" :span="8">
                      <n-form-item path="innovationType">
                        <n-radio-group v-model:value="saveForm.projectInfo.innovationType" :disabled="operationType === ProjectOperationType.DETAILS">
                          <n-flex justify="space-between">
                            <n-radio
                              v-for="item in options.INNOVATION_TYPE"
                              class="enlarged"
                              size="large"
                              :value="item.value"
                              :label="item.label"
                            />
                          </n-flex>
                        </n-radio-group>
                      </n-form-item>
                    </n-descriptions-item>
                    <n-descriptions-item label="产学研联合" :span="4">
                      <n-form-item path="industryAcademiaCollab">
                        <n-radio-group v-model:value="saveForm.projectInfo.industryAcademiaCollab" :disabled="operationType === ProjectOperationType.DETAILS">
                          <n-flex justify="space-between">
                            <n-radio
                              v-for="item in options.INDUSTRY_ACADEMIA_COLLAB"
                              class="enlarged"
                              size="large"
                              :value="item.value"
                              :label="item.label"
                            />
                          </n-flex>
                        </n-radio-group>
                      </n-form-item>
                    </n-descriptions-item>
                    <n-descriptions-item label="知识产权状况" :span="4">
                      <n-form-item path="ipStatus">
                        <n-radio-group v-model:value="saveForm.projectInfo.ipStatus" :disabled="operationType === ProjectOperationType.DETAILS">
                          <n-flex justify="space-between">
                            <n-radio
                              v-for="item in options.IP_STATUS"
                              class="enlarged"
                              size="large"
                              :value="item.value"
                              :label="item.label"
                            />
                          </n-flex>
                        </n-radio-group>
                      </n-form-item>
                    </n-descriptions-item>
                    <n-descriptions-item label="成果水平" :span="8">
                      <n-form-item path="resultLevel">
                        <n-radio-group v-model:value="saveForm.projectInfo.resultLevel" :disabled="operationType === ProjectOperationType.DETAILS">
                          <n-flex justify="space-between">
                            <n-radio
                              v-for="item in options.RESULTLEVEL"
                              class="enlarged"
                              size="large"
                              :value="item.value"
                              :label="item.label"
                            />
                          </n-flex>
                        </n-radio-group>
                      </n-form-item>
                    </n-descriptions-item>
                    <n-descriptions-item label="成果形式" :span="8">
                      <n-form-item path="resultFormat">
                        <n-checkbox-group v-model:value="saveForm.projectInfo.resultFormat" :disabled="operationType === ProjectOperationType.DETAILS">
                          <n-flex justify="start">
                            <n-checkbox
                              v-for="item in options.RESULT_FORMAT"
                              class="enlarged"
                              size="large"
                              :value="item.value"
                              :label="item.label"
                            />
                          </n-flex>
                        </n-checkbox-group>
                      </n-form-item>
                    </n-descriptions-item>
                    <n-descriptions-item label="知识产权" :span="8">
                      <span>
                        发明专利
                        <input
                          type="number"
                          min="0"
                          :disabled="operationType === ProjectOperationType.DETAILS"
                          v-model="saveForm.projectInfo.intellectualProperty.inventPatentCnt"
                          class="nInputInText"
                        />
                        项，实用新型专利
                        <input
                          type="number"
                          min="0"
                          :disabled="operationType === ProjectOperationType.DETAILS"
                          v-model="saveForm.projectInfo.intellectualProperty.utilPatentCnt"
                          class="nInputInText"
                        />
                        项，其他
                        <input
                          type="number"
                          min="0"
                          :disabled="operationType === ProjectOperationType.DETAILS"
                          v-model="saveForm.projectInfo.intellectualProperty.otherPatentNum"
                          class="nInputInText"
                        />
                        项。
                      </span>
                    </n-descriptions-item>
                    <n-descriptions-item label="技术标准制定" :span="8">
                      <n-form-item path="techStdDev">
                        <n-radio-group v-model:value="saveForm.projectInfo.techStdDev" :disabled="operationType === ProjectOperationType.DETAILS">
                          <n-flex justify="space-between">
                            <n-radio
                              v-for="item in options.TECH_STD_DEV"
                              class="enlarged"
                              size="large"
                              :value="item.value"
                              :label="item.label"
                            />
                          </n-flex>
                        </n-radio-group>
                      </n-form-item>
                    </n-descriptions-item>
                    <n-descriptions-item label="经费预算" :span="8" id="d0201">
                      <n-form-item path="budget">
                        <!--                          <span>-->
                        <!--                            <input-->
                        <!--                              type="number"-->
                        <!--                              min="0"-->
                        <!--                              :disabled="readonly"-->
                        <!--                              v-model="saveForm.projectInfo.budget"-->
                        <!--                              class="nInputInText"-->
                        <!--                            />-->
                        <!--                            万元-->
                        <!--                          </span>-->
                        <n-input-number
                          class="enlarged"
                          size="large"
                          :value="saveForm.projectInfo.budget"
                          :min="0"
                          clearable
                          :precision="2"
                          :show-button="false"
                          :disabled="operationType === ProjectOperationType.DETAILS"
                          @update:value="updateProjectBudget"
                        >
                          <template #prefix>￥</template>
                          <template #suffix>万元</template>
                        </n-input-number>
                      </n-form-item>
                    </n-descriptions-item>
                    <n-descriptions-item
                      label="项目概述(不超过500字)"
                      :span="8"
                      id="d0202"
                      v-if="operationType !== ProjectOperationType.APPLY_2023 && !isProject2023"
                    >
                      <template #default>
                        <n-form-item path="projectDescr">
                          <wang-editor
                            v-model="saveForm.projectInfo.projectDescr"
                            placeholder="请输入项目概述(不超过500字)"
                            :readonly="readonly"
                            :max-length="500"
                          />
                        </n-form-item>
                      </template>
                    </n-descriptions-item>
                  </n-descriptions>
                </div>

                <!--                一、项目研究目标-->
                <div
                  class="declaration-part"
                  id="d04"
                  v-if="operationType !== ProjectOperationType.APPLY_2023 && !isProject2023"
                >
                  <n-divider title-placement="center">
                    <span class="declaration-part-title"
                      >一、项目现有技术指标(产品参数)、经济指标；项目研究主要目标、研究内容、技术关键、技术路线和应用方案</span
                    >
                    <n-tooltip trigger="hover">
                      <template #trigger>
                        <n-icon style="cursor: pointer">
                          <QuestionFilled />
                        </n-icon>
                      </template>
                      不超过3000字
                    </n-tooltip>
                  </n-divider>
                  <n-descriptions bordered label-class="descriptions-label" label-style="display: none;" :column="8">
                    <n-descriptions-item :span="8">
                      <n-form-item path="richText.technicalProposal">
                        <wang-editor
                          v-model="saveForm.richText.technicalProposal"
                          placeholder="请输入项目现有技术指标（产品参数）、经济指标；项目研究主要目标、研究内容、技术关键、技术路线和应用方案(不超过3000字)"
                          :readonly="readonly"
                          :max-length="3000"
                        />
                      </n-form-item>
                    </n-descriptions-item>
                  </n-descriptions>
                </div>

                <!--                二、立项的必要性-->
                <div
                  class="declaration-part"
                  id="d05"
                  v-if="operationType !== ProjectOperationType.APPLY_2023 && !isProject2023"
                >
                  <n-divider title-placement="center">
                    <span class="declaration-part-title"
                      >二、立项的必要性及国内外研究现状、发展趋势和知识产权状况分析</span
                    >
                    <n-tooltip trigger="hover">
                      <template #trigger>
                        <n-icon style="cursor: pointer">
                          <QuestionFilled />
                        </n-icon>
                      </template>
                      不超过2000字
                    </n-tooltip>
                  </n-divider>
                  <n-descriptions bordered label-class="descriptions-label" label-style="display: none;" :column="8">
                    <n-descriptions-item :span="8">
                      <n-form-item path="richText.researchBackground">
                        <wang-editor
                          v-model="saveForm.richText.researchBackground"
                          placeholder="请输入立项的必要性及国内外研究现状、发展趋势和知识产权状况分析(不超过2000字)"
                          :readonly="readonly"
                          :max-length="2000"
                        />
                      </n-form-item>
                    </n-descriptions-item>
                  </n-descriptions>
                </div>

                <!--                三、项目绩效目标-->
                <div class="declaration-part" id="d06">
                  <n-divider title-placement="center">
                    <span class="declaration-part-title">三、项目绩效目标</span>
                  </n-divider>
                  <div
                    v-for="(indicator, pidx) in performanceGoalsTree"
                    :id="`d06${(pidx + 1).toString().padStart(2, '0')}`"
                    class="descriptions-part-subtitle"
                  >
                    <n-h4 prefix="bar"
                      ><strong
                        >{{ pidx + 1 }}.
                        {{
                          indicator.performanceIndicator.label +
                          (indicator.performanceIndicator.remark ? `（${indicator.performanceIndicator.remark}）` : '')
                        }}</strong
                      ></n-h4
                    >
                    <n-descriptions
                      v-if="indicator.children"
                      bordered
                      label-align="center"
                      label-placement="left"
                      label-class="descriptions-label"
                      style="margin-bottom: 18px"
                      :column="indicator.performanceIndicator.cols"
                    >
                      <n-descriptions-item
                        v-for="item in indicator.children"
                        :label="
                          item.performanceIndicator.label +
                          (item.performanceIndicator.remark ? `（${item.performanceIndicator.remark}）` : '')
                        "
                      >
                        <!--                        {{item.dataType}} - {{item.children}}-->
                        <template v-if="!item.performanceIndicator.dataType && item.children">
                          <template v-for="(i, idx) in item.children">
                            <template v-if="i.performanceIndicator.showPrefix">
                              {{ i.performanceIndicator.label }}
                            </template>
                            <rms-input-number
                              v-if="i.performanceIndicator.dataType === PerformanceIndicatorDataTypeEnum.NUMBER"
                              required
                              v-model:value="saveForm.rmsProjectPerformanceGoalData[i.index].indicatorValue as number"
                              :disabled="operationType === ProjectOperationType.DETAILS"
                              :min="operationType === ProjectOperationType.FILL_BRIEF ? (originalData?.rmsProjectPerformanceGoals ? originalData?.rmsProjectPerformanceGoals[i.performanceIndicator.fieldName] as number : 0) :0"
                            />
                            <input
                              v-if="i.performanceIndicator.dataType === PerformanceIndicatorDataTypeEnum.TEXT"
                              :disabled="operationType === ProjectOperationType.DETAILS"
                              v-model="saveForm.rmsProjectPerformanceGoalData[i.index].indicatorValue"
                              class="nInput"
                              style="width: 70%; height: 20px"
                            />
                            {{ i.performanceIndicator.unit }}
                            <template v-if="idx !== item.children.length - 1">，</template>
                          </template>
                        </template>
                        <n-form-item
                          v-else
                          ignore-path-change
                          :path="`rmsProjectPerformanceGoalData[${item.index}].indicatorValue`"
                          :rule="saveFormRules.rmsProjectPerformanceGoals[item.performanceIndicator.fieldName]"
                        >
                          <template
                            v-if="item.performanceIndicator.dataType === PerformanceIndicatorDataTypeEnum.NUMBER"
                          >
                            <template v-if="item.performanceIndicator.showPrefix">
                              {{ item.performanceIndicator.label }}
                            </template>
                            <rms-input-number
                              v-if="item.performanceIndicator.dataType === PerformanceIndicatorDataTypeEnum.NUMBER"
                              required
                              v-model:value="saveForm.rmsProjectPerformanceGoalData[item.index].indicatorValue as number"
                              :disabled="operationType === ProjectOperationType.DETAILS"
                              :min="operationType === ProjectOperationType.FILL_BRIEF ? (originalData?.rmsProjectPerformanceGoals ? originalData?.rmsProjectPerformanceGoalData[item.index].indicatorValue as number : 0) :0"
                            />
                            {{ item.performanceIndicator.unit }}
                          </template>
                          <n-checkbox-group
                            v-else-if="item.performanceIndicator.dataType === PerformanceIndicatorDataTypeEnum.ARRAY"
                            v-model:value="saveForm.rmsProjectPerformanceGoalData[item.index].indicatorValue"
                            :disabled="operationType === ProjectOperationType.DETAILS"
                          >
                            <n-flex justify="start">
                              <n-checkbox
                                v-for="option in options[item.performanceIndicator.dictType]"
                                class="enlarged"
                                size="large"
                                :disabled="
                                  (readonly && operationType !== ProjectOperationType.FILL_BRIEF) ||
                                  (operationType === ProjectOperationType.FILL_BRIEF &&
                                    originalData?.rmsProjectPerformanceGoalData[item.index].indicatorValue?.includes(
                                      option.value
                                    ))
                                "
                                :key="option.value"
                                :value="option.value"
                                :label="option.label"
                              />
                            </n-flex>
                          </n-checkbox-group>
                          <input
                            v-else-if="item.performanceIndicator.dataType === PerformanceIndicatorDataTypeEnum.TEXT"
                            :disabled="operationType === ProjectOperationType.DETAILS"
                            v-model="saveForm.rmsProjectPerformanceGoalData[item.index].indicatorValue"
                            class="nInput"
                          />
                          <n-input
                            v-else-if="item.performanceIndicator.dataType === PerformanceIndicatorDataTypeEnum.TEXTAREA"
                            class="enlarged"
                            size="large"
                            type="textarea"
                            show-count
                            clearable
                            :maxlength="item.performanceIndicator.maxLength"
                            :autosize="{ minRows: 3 }"
                            :disabled="operationType === ProjectOperationType.DETAILS"
                            v-model:value="saveForm.rmsProjectPerformanceGoalData[item.index].indicatorValue as string"
                          />
                        </n-form-item>
                      </n-descriptions-item>
                      <!--                      <n-descriptions-item content-style="background-color: #FAFAFC; width: 150px; font-size: 18px; display: flex; align-items: center; justify-content: center;">成果形式</n-descriptions-item>-->
                    </n-descriptions>
                  </div>

                  <!--                  <div id="d0601" class="declaration-part-subtitle">-->
                  <!--                    <n-h4 prefix="bar"><strong>1.技术创新目标</strong></n-h4>-->
                  <!--                    <n-descriptions-->
                  <!--                      bordered-->
                  <!--                      label-align="center"-->
                  <!--                      label-placement="left"-->
                  <!--                      label-class="descriptions-label"-->
                  <!--                      :column="8"-->
                  <!--                    >-->
                  <!--                      <n-descriptions-item :span="8" label="成果形式">-->
                  <!--                        <n-form-item path="rmsProjectPerformanceGoals.resultForm">-->
                  <!--                          <n-checkbox-group v-model:value="saveForm.rmsProjectPerformanceGoals.resultForm">-->
                  <!--                            <n-flex justify="start">-->
                  <!--                              <n-checkbox-->
                  <!--                                v-for="item in options.RESULT_FORM"-->
                  <!--                                class="enlarged"-->
                  <!--                                size="large"-->
                  <!--                                :value="item.value"-->
                  <!--                                :label="item.label"-->
                  <!--                              />-->
                  <!--                            </n-flex>-->
                  <!--                          </n-checkbox-group>-->
                  <!--                        </n-form-item>-->
                  <!--                      </n-descriptions-item>-->
                  <!--                      <n-descriptions-item :span="8" label="知识产权">-->
                  <!--                        发明专利授权-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.inventPatentCt"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        项，发明专利受理-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.inventApplicationCt"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        项，实用新型专利授权-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.utilityPatentCt"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        项，实用新型专利受理-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.utilityApplicationCt"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        项-->
                  <!--                      </n-descriptions-item>-->
                  <!--                      <n-descriptions-item :span="8" label="技术标准制定">-->
                  <!--                        国际标准-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.interStndaCt"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        项，国家、行业标准-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.ctyindastyStndaCt"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        项，地方、企业标准-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.companyStndaCt"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        项-->
                  <!--                      </n-descriptions-item>-->
                  <!--                      <n-descriptions-item :span="8" label="认证、认可">-->
                  <!--                        新药证书-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.newDrugCertifyCt"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        项，新品种审定证书-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.newVarietyCertifyCt"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        项，计算机软件著作权登记证书-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.softwareCprightCt"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        项，新药临床批件-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.ndClinicalPermitCt"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        件，三类医疗器械注册受理证明-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.tclsMedcDeviceRegistCt"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        件，三类医疗器械临床试验许可-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.tclsMedcDeviceRegistLic"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        件。-->
                  <!--                      </n-descriptions-item>-->
                  <!--                      <n-descriptions-item :span="8" label="论文专著">-->
                  <!--                        公开发表-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.publicationCt"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        篇，引用-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.citationCt"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        次，出版专著-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.bookCt"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        部-->
                  <!--                      </n-descriptions-item>-->
                  <!--                      <n-descriptions-item :span="8" label="其他">-->
                  <!--                        <n-form-item path="rmsProjectPerformanceGoals.otherTchInnovateTgs">-->
                  <!--                          <n-input-->
                  <!--                            class="enlarged"-->
                  <!--                            size="large-->
                  <!--                            type="textarea"-->
                  <!--                            maxlength="200"-->
                  <!--                            show-count-->
                  <!--                            clearable-->
                  <!--                            :autosize="{ minRows: 3 }"-->
                  <!--                            :disabled="readonly"-->
                  <!--                            v-model:value="saveForm.rmsProjectPerformanceGoals.otherTchInnovateTgs"-->
                  <!--                          />-->
                  <!--                          &lt;!&ndash;                          <input&ndash;&gt;-->
                  <!--                          &lt;!&ndash;                            type="text"&ndash;&gt;-->
                  <!--                          &lt;!&ndash;                            :disabled="readonly"&ndash;&gt;-->
                  <!--                          &lt;!&ndash;                            v-model="saveForm.rmsProjectPerformanceGoals.otherTchInnovateTgs"&ndash;&gt;-->
                  <!--                          &lt;!&ndash;                            class="nInput"&ndash;&gt;-->
                  <!--                          &lt;!&ndash;                          />&ndash;&gt;-->
                  <!--                        </n-form-item>-->
                  <!--                      </n-descriptions-item>-->
                  <!--                    </n-descriptions>-->
                  <!--                  </div>-->

                  <!--                  <div id="d0602" class="declaration-part-subtitle">-->
                  <!--                    <n-h4 prefix="bar"><strong>2.示范应用目标</strong></n-h4>-->
                  <!--                    <n-descriptions-->
                  <!--                      bordered-->
                  <!--                      label-align="center"-->
                  <!--                      label-placement="left"-->
                  <!--                      label-class="descriptions-label"-->
                  <!--                      :column="8"-->
                  <!--                    >-->
                  <!--                      <n-descriptions-item :span="8" label="推广应用目标">-->
                  <!--                        <n-form-item path="rmsProjectPerformanceGoals.promotionTgs">-->
                  <!--                          <n-input-->
                  <!--                            class="enlarged"-->
                  <!--                            size="large-->
                  <!--                            type="textarea"-->
                  <!--                            maxlength="200"-->
                  <!--                            show-count-->
                  <!--                            clearable-->
                  <!--                            :autosize="{ minRows: 3 }"-->
                  <!--                            :disabled="readonly"-->
                  <!--                            v-model:value="saveForm.rmsProjectPerformanceGoals.promotionTgs"-->
                  <!--                          />-->
                  <!--                          &lt;!&ndash;                          <input&ndash;&gt;-->
                  <!--                          &lt;!&ndash;                            type="text"&ndash;&gt;-->
                  <!--                          &lt;!&ndash;                            :disabled="readonly"&ndash;&gt;-->
                  <!--                          &lt;!&ndash;                            v-model="saveForm.rmsProjectPerformanceGoals.promotionTgs"&ndash;&gt;-->
                  <!--                          &lt;!&ndash;                            class="nInput"&ndash;&gt;-->
                  <!--                          &lt;!&ndash;                          />&ndash;&gt;-->
                  <!--                        </n-form-item>-->
                  <!--                      </n-descriptions-item>-->
                  <!--                      <n-descriptions-item :span="8" label="培训（科技培训项目必填）">-->
                  <!--                        办-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.trainingSessionCt"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        期培训班，培训科技管理人员-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.techManagerCount"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        人、培训医疗技术、推广人员-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.medcTchPromoterCt"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        人。-->
                  <!--                      </n-descriptions-item>-->
                  <!--                    </n-descriptions>-->
                  <!--                  </div>-->

                  <!--                  <div id="d0603" class="declaration-part-subtitle">-->
                  <!--                    <n-h4 prefix="bar"><strong>3.技术和人才合作目标</strong></n-h4>-->
                  <!--                    <n-descriptions-->
                  <!--                      bordered-->
                  <!--                      label-align="center"-->
                  <!--                      label-placement="left"-->
                  <!--                      label-class="descriptions-label"-->
                  <!--                      :column="8"-->
                  <!--                    >-->
                  <!--                      <n-descriptions-item :span="4" label="引进关键技术">-->
                  <!--                        <n-form-item path="rmsProjectPerformanceGoals.keyTchCt">-->
                  <!--                          <input-->
                  <!--                            type="number"-->
                  <!--                            min="0"-->
                  <!--                            :disabled="readonly"-->
                  <!--                            v-model="saveForm.rmsProjectPerformanceGoals.keyTchCt"-->
                  <!--                            class="nInputInText"-->
                  <!--                          />-->
                  <!--                          项-->
                  <!--                        </n-form-item>-->
                  <!--                      </n-descriptions-item>-->
                  <!--                      <n-descriptions-item :span="4" label="技术名称">-->
                  <!--                        <n-form-item path="rmsProjectPerformanceGoals.tchName">-->
                  <!--                          <input-->
                  <!--                            :disabled="readonly"-->
                  <!--                            v-model="saveForm.rmsProjectPerformanceGoals.tchName"-->
                  <!--                            class="nInput"-->
                  <!--                          />-->
                  <!--                        </n-form-item>-->
                  <!--                      </n-descriptions-item>-->
                  <!--                      <n-descriptions-item :span="4" label="引进关键设备">-->
                  <!--                        <n-form-item path="rmsProjectPerformanceGoals.keyEquipmentCt">-->
                  <!--                          <input-->
                  <!--                            type="number"-->
                  <!--                            min="0"-->
                  <!--                            :disabled="readonly"-->
                  <!--                            v-model="saveForm.rmsProjectPerformanceGoals.keyEquipmentCt"-->
                  <!--                            class="nInputInText"-->
                  <!--                          />-->
                  <!--                          台-->
                  <!--                        </n-form-item>-->
                  <!--                      </n-descriptions-item>-->
                  <!--                      <n-descriptions-item :span="4" label="设备名称">-->
                  <!--                        <n-form-item path="rmsProjectPerformanceGoals.equipmentName">-->
                  <!--                          <input-->
                  <!--                            :disabled="readonly"-->
                  <!--                            v-model="saveForm.rmsProjectPerformanceGoals.equipmentName"-->
                  <!--                            class="nInput"-->
                  <!--                          />-->
                  <!--                        </n-form-item>-->
                  <!--                      </n-descriptions-item>-->
                  <!--                      <n-descriptions-item :span="4" label="引进特有资源">-->
                  <!--                        <n-form-item path="rmsProjectPerformanceGoals.uniqueResourceCt">-->
                  <!--                          <input-->
                  <!--                            type="number"-->
                  <!--                            min="0"-->
                  <!--                            :disabled="readonly"-->
                  <!--                            v-model="saveForm.rmsProjectPerformanceGoals.uniqueResourceCt"-->
                  <!--                            class="nInputInText"-->
                  <!--                          />-->
                  <!--                          种-->
                  <!--                        </n-form-item>-->
                  <!--                      </n-descriptions-item>-->
                  <!--                      <n-descriptions-item :span="4" label="资源名称">-->
                  <!--                        <n-form-item path="rmsProjectPerformanceGoals.resourceName">-->
                  <!--                          <input-->
                  <!--                            :disabled="readonly"-->
                  <!--                            v-model="saveForm.rmsProjectPerformanceGoals.resourceName"-->
                  <!--                            class="nInput"-->
                  <!--                          />-->
                  <!--                        </n-form-item>-->
                  <!--                      </n-descriptions-item>-->
                  <!--                    </n-descriptions>-->
                  <!--                  </div>-->

                  <!--                  <div id="d0604" class="declaration-part-subtitle">-->
                  <!--                    <n-h4 prefix="bar"><strong>4.人才培养目标（所有有高校参与的项目，必须填写）</strong></n-h4>-->
                  <!--                    <n-descriptions-->
                  <!--                      bordered-->
                  <!--                      label-align="center"-->
                  <!--                      label-placement="left"-->
                  <!--                      label-class="descriptions-label"-->
                  <!--                      :column="8"-->
                  <!--                    >-->
                  <!--                      <n-descriptions-item :span="8" label="高端人才">-->
                  <!--                        院士-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.academicianCt"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        人，享受国务院政府特殊津贴专家-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.speclAllowanceEptCt"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        人，国家杰出青年科学基金-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.ctryOutstandiTfdCt"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        人，全国杰出专业技术人才-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.ctryOutstandiTltCt"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        人，长江学者-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.yangtzeScholarCt"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        人，新世纪优秀人才-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.newCenturyTltCt"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        人，省有突出贡献的优秀专家-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.provinOutstandEptCt"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        人，省学术和技术带头人-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.provinAcdmicLeaderCt"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        人，省学术和技术带头人后备人选-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.pvlAcademicLdrCandid"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        人，千人计划-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.thousandTltPlanCt"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        人，万人计划-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.tenThousandPlanCt"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        人-->
                  <!--                      </n-descriptions-item>-->
                  <!--                      <n-descriptions-item :span="8" label="职称晋升">-->
                  <!--                        高级-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.seniorTitle"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        人，中级-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.intermediateTitle"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        人-->
                  <!--                      </n-descriptions-item>-->
                  <!--                      <n-descriptions-item :span="8" label="学位人才">-->
                  <!--                        博士后进站-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.postdocEntryCt"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        人，在读博士研究生-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.phdStudentCt"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        人，在读硕士研究生-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.mastersStudentCt"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        人，博士后出站-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.postdocExitCt"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        人，毕业博士研究生-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.profPhdStudentCt"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        人，毕业硕士研究生-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.mastersGradCt"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        人，毕业学士-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.bachelorsGradCt"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        人-->
                  <!--                      </n-descriptions-item>-->
                  <!--                      <n-descriptions-item :span="8" label="吸纳大学生就业">-->
                  <!--                        博士后-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.postdocRecruitsCt"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        人，博士研究生-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.phdEmployedCt"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        人，硕士研究生-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.mastersEmployedCt"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        人，本科生-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.bachelorsEmployedCt"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        人，专科生-->
                  <!--                        <input-->
                  <!--                          type="number"-->
                  <!--                          min="0"-->
                  <!--                          :disabled="readonly"-->
                  <!--                          v-model="saveForm.rmsProjectPerformanceGoals.assocDegEmployedCt"-->
                  <!--                          class="nInputInText"-->
                  <!--                        />-->
                  <!--                        人-->
                  <!--                      </n-descriptions-item>-->
                  <!--                    </n-descriptions>-->
                  <!--                  </div>-->

                  <!--                  <div id="d0605" class="declaration-part-subtitle">-->
                  <!--                    <n-h4 prefix="bar"><strong>5.社会效益目标（必填）</strong></n-h4>-->
                  <!--                    <n-descriptions-->
                  <!--                      bordered-->
                  <!--                      label-align="center"-->
                  <!--                      label-placement="left"-->
                  <!--                      label-class="descriptions-label"-->
                  <!--                      :column="8"-->
                  <!--                    >-->
                  <!--                      <n-descriptions-item label="技术及产品应用形成的公益性贡献和价值" :span="8">-->
                  <!--                        <n-form-item path="rmsProjectPerformanceGoals.socialImpactGoals">-->
                  <!--                          <n-input-->
                  <!--                            class="enlarged"-->
                  <!--                            size="large"-->
                  <!--                            type="textarea"-->
                  <!--                            maxlength="200"-->
                  <!--                            show-count-->
                  <!--                            clearable-->
                  <!--                            :autosize="{ minRows: 3 }"-->
                  <!--                            :disabled="readonly"-->
                  <!--                            v-model:value="saveForm.rmsProjectPerformanceGoals.socialImpactGoals"-->
                  <!--                          />-->
                  <!--                          &lt;!&ndash;                          <input&ndash;&gt;-->
                  <!--                          &lt;!&ndash;                            :disabled="readonly"&ndash;&gt;-->
                  <!--                          &lt;!&ndash;                            v-model="saveForm.rmsProjectPerformanceGoals.socialImpactGoals"&ndash;&gt;-->
                  <!--                          &lt;!&ndash;                            type="text"&ndash;&gt;-->
                  <!--                          &lt;!&ndash;                            class="nInput"&ndash;&gt;-->
                  <!--                          &lt;!&ndash;                          />&ndash;&gt;-->
                  <!--                        </n-form-item>-->
                  <!--                      </n-descriptions-item>-->
                  <!--                    </n-descriptions>-->
                  <!--                  </div>-->

                  <!--                  <div id="d0606" class="declaration-part-subtitle">-->
                  <!--                    <n-h4 prefix="bar"><strong>6.科技报告</strong></n-h4>-->
                  <!--                    <n-descriptions-->
                  <!--                      bordered-->
                  <!--                      label-align="center"-->
                  <!--                      label-placement="left"-->
                  <!--                      label-class="descriptions-label"-->
                  <!--                      :column="8"-->
                  <!--                    >-->
                  <!--                      <n-descriptions-item :span="8" label="中期报告">-->
                  <!--                        <n-form-item path="rmsProjectPerformanceGoals.interimReportsCt">-->
                  <!--                          <input-->
                  <!--                            type="number"-->
                  <!--                            min="0"-->
                  <!--                            :disabled="readonly"-->
                  <!--                            v-model="saveForm.rmsProjectPerformanceGoals.interimReportsCt"-->
                  <!--                            class="nInputInText"-->
                  <!--                          />-->
                  <!--                          篇-->
                  <!--                        </n-form-item>-->
                  <!--                      </n-descriptions-item>-->
                  <!--                      <n-descriptions-item :span="8" label="年度报告">-->
                  <!--                        <n-form-item path="rmsProjectPerformanceGoals.annualReportsCt">-->
                  <!--                          <input-->
                  <!--                            type="number"-->
                  <!--                            min="0"-->
                  <!--                            :disabled="readonly"-->
                  <!--                            v-model="saveForm.rmsProjectPerformanceGoals.annualReportsCt"-->
                  <!--                            class="nInputInText"-->
                  <!--                          />-->
                  <!--                          篇-->
                  <!--                        </n-form-item>-->
                  <!--                      </n-descriptions-item>-->
                  <!--                      <n-descriptions-item :span="8" label="最终报告">-->
                  <!--                        <n-form-item path="rmsProjectPerformanceGoals.finalReportCt">-->
                  <!--                          <input-->
                  <!--                            type="number"-->
                  <!--                            min="0"-->
                  <!--                            :disabled="readonly"-->
                  <!--                            v-model="saveForm.rmsProjectPerformanceGoals.finalReportCt"-->
                  <!--                            class="nInputInText"-->
                  <!--                          />-->
                  <!--                          篇-->
                  <!--                        </n-form-item>-->
                  <!--                      </n-descriptions-item>-->
                  <!--                    </n-descriptions>-->
                  <!--                  </div>-->
                </div>

                <!--                四、项目的创新性-->
                <div
                  class="declaration-part"
                  id="d07"
                  v-if="operationType !== ProjectOperationType.APPLY_2023 && !isProject2023"
                >
                  <n-divider title-placement="center">
                    <span class="declaration-part-title">四、项目的创新性</span>
                    <n-tooltip trigger="hover">
                      <template #trigger>
                        <n-icon style="cursor: pointer">
                          <QuestionFilled />
                        </n-icon>
                      </template>
                      理论创新、应用创新、技术创新(不超过800字)
                    </n-tooltip>
                  </n-divider>
                  <n-descriptions bordered label-class="descriptions-label" label-style="display: none;" :column="8">
                    <n-descriptions-item :span="8">
                      <n-form-item path="richText.projectInnovation">
                        <wang-editor
                          v-model="saveForm.richText.projectInnovation"
                          placeholder="请输入项目的创新性(理论创新、应用创新、技术创新(不超过800字))"
                          :readonly="readonly"
                          :max-length="800"
                        />
                      </n-form-item>
                    </n-descriptions-item>
                  </n-descriptions>
                </div>

                <!--                五、项目应用前景-->
                <div
                  class="declaration-part"
                  id="d08"
                  v-if="operationType !== ProjectOperationType.APPLY_2023 && !isProject2023"
                >
                  <n-divider title-placement="center">
                    <span class="declaration-part-title">五、项目应用前景和预期经济、社会效益</span>
                    <n-tooltip trigger="hover">
                      <template #trigger>
                        <n-icon style="cursor: pointer">
                          <QuestionFilled />
                        </n-icon>
                      </template>
                      不超过1000字
                    </n-tooltip>
                  </n-divider>
                  <n-descriptions bordered label-class="descriptions-label" label-style="display: none;" :column="8">
                    <n-descriptions-item :span="8">
                      <n-form-item path="richText.projectBenefits">
                        <wang-editor
                          v-model="saveForm.richText.projectBenefits"
                          placeholder="请输入项目应用前景和预期经济、社会效益(不超过1000字)"
                          :readonly="readonly"
                          :max-length="1000"
                        />
                      </n-form-item>
                    </n-descriptions-item>
                  </n-descriptions>
                </div>

                <!--                六、已有研究基础-->
                <div
                  class="declaration-part"
                  id="d09"
                  v-if="operationType !== ProjectOperationType.APPLY_2023 && !isProject2023"
                >
                  <n-divider title-placement="center">
                    <span class="declaration-part-title">六、已有研究基础、承担优势和项目实施的风险及应对策略</span>
                    <n-tooltip trigger="hover">
                      <template #trigger>
                        <n-icon>
                          <QuestionFilled />
                        </n-icon>
                      </template>
                      研究基础、承担优势包括与项目有关的前期研究状况、实验设备及设备条件、近三年主持或主研的科研成果，获奖及发表论文情况，产学研结合情况等。不超过1000字
                    </n-tooltip>
                  </n-divider>

                  <div id="d0901" class="declaration-part-subtitle">
                    <n-h4 prefix="bar"><strong>已有研究基础、承担优势和项目实施的风险及应对策略</strong></n-h4>
                    <n-descriptions bordered label-class="descriptions-label" label-style="display: none;" :column="8">
                      <n-descriptions-item :span="8">
                        <n-form-item path="richText.conditionRisk">
                          <wang-editor
                            v-model="saveForm.richText.conditionRisk"
                            placeholder="请输入已有研究基础、承担优势和项目实施的风险及应对策略(研究基础、承担优势包括与项目有关的前期研究状况、实验设备及设备条件、近三年主持或主研的科研成果，获奖及发表论文情况，产学研结合情况等(不超过1000字)"
                            :readonly="readonly"
                            :max-length="1000"
                          />
                        </n-form-item>
                      </n-descriptions-item>
                    </n-descriptions>
                  </div>

                  <div id="d0902" class="declaration-part-subtitle">
                    <n-h4 prefix="bar"><strong>相关数据或成果佐证资料上传</strong></n-h4>
                    <n-form-item path="swotAttachments">
                      <!--                      <n-upload-->
                      <!--                        ref="swotAttachmentsRef"-->
                      <!--                        v-model:file-list="swotAttachmentConfig.fileList"-->
                      <!--                        :list-type="swotAttachmentConfig.listType"-->
                      <!--                        :create-thumbnail-url="swotAttachmentConfig.createThumbnailUrl"-->
                      <!--                        :accept="swotAttachmentConfig.accept"-->
                      <!--                        :multiple="swotAttachmentConfig.multiple"-->
                      <!--                        :max="swotAttachmentConfig.max"-->
                      <!--                        :disabled="swotAttachmentConfig.disabled"-->
                      <!--                        :default-upload="swotAttachmentConfig.defaultUpload"-->
                      <!--                        :show-download-button="swotAttachmentConfig.showDownloadButton"-->
                      <!--                        :custom-request="swotAttachmentConfig.customRequest"-->
                      <!--                        @change="swotAttachmentConfig.change"-->
                      <!--                        @download="swotAttachmentConfig.download"-->
                      <!--                        @remove="swotAttachmentConfig.remove"-->
                      <!--                      >-->
                      <!--                        <n-upload-dragger>-->
                      <!--                          <div style="margin-bottom: 12px">-->
                      <!--                            <n-icon size="48" :depth="3">-->
                      <!--                              <UploadFilled />-->
                      <!--                            </n-icon>-->
                      <!--                          </div>-->
                      <!--                          <n-text style="font-size: 16px"> 点击或者拖动文件到该区域来上传 </n-text>-->
                      <!--                          <n-p depth="3" style="margin: 8px 0 0 0">-->
                      <!--                            {{ swotAttachmentConfig.tips }}-->
                      <!--                          </n-p>-->
                      <!--                        </n-upload-dragger>-->
                      <!--                      </n-upload>-->
                      <yt-upload
                        v-model:file-list="saveForm.swotAttachments"
                        :upload-fn="uploadRmsFile"
                        accept=".pdf,.doc,.docx,.xls,.xlsx,.zip,.rar,.7z,image/*"
                        :maxSize="5 * 1024"
                        :limit="5"
                        :disabled="operationType === ProjectOperationType.DETAILS"
                        drag
                        @before-upload="disableSaveDraft"
                        @success="enableSaveDraft"
                        @error="enableSaveDraft"
                      />
                    </n-form-item>
                    <!--                    <n-button-->
                    <!--                      v-if="!swotAttachmentConfig.defaultUpload"-->
                    <!--                      type="info"-->
                    <!--                      style="margin-top: 5px"-->
                    <!--                      @click="swotAttachmentConfig.submit"-->
                    <!--                      :disabled="swotAttachmentConfig.disabledConfirm"-->
                    <!--                    >-->
                    <!--                      确认上传-->
                    <!--                    </n-button>-->
                  </div>
                </div>

                <!--                七、项目进度及预期目标-->
                <div class="declaration-part" id="d11">
                  <n-divider title-placement="center">
                    <span class="declaration-part-title">七、项目进度及预期目标</span>
                    <n-tooltip trigger="hover">
                      <template #trigger>
                        <n-icon style="cursor: pointer">
                          <QuestionFilled />
                        </n-icon>
                      </template>
                      以每半年为时间段，目标可量化可考核
                    </n-tooltip>
                  </n-divider>
                  <!--                  <n-h4 prefix="bar"><strong>目标规划与期望（不超过1000字）</strong></n-h4>-->
                  <!--                  <n-descriptions bordered-->
                  <!--                                  label-class="descriptions-label"-->
                  <!--                                  label-style="display: none;"-->
                  <!--                                  :column="8">-->
                  <!--                    <n-descriptions-item :span="8">-->
                  <!--                      <n-form-item path="richText.expectedObjectives">-->
                  <!--                        <wang-editor v-model="saveForm.richText.expectedObjectives"-->
                  <!--                                     placeholder="请输入目标规划与期望(不超过1000字)"-->
                  <!--                                     :readonly="readonly"-->
                  <!--                                     :max-length="1000" />-->
                  <!--                      </n-form-item>-->
                  <!--                    </n-descriptions-item>-->
                  <!--                  </n-descriptions>-->
                  <div id="d1101" class="declaration-part-subtitle">
                    <n-h4 prefix="bar"><strong>分年度研究内容和考核指标（按每半年度填写）</strong></n-h4>
                    <!--                    <n-flex justify="center" :size="48" style="margin-bottom: 24px" v-if="!readonly">-->
                    <!--                      <n-popconfirm placement="left" @positive-click="deleteAllResearchIndex()">-->
                    <!--                        <template #trigger>-->
                    <!--                          <j-icon name="delete" style="cursor: pointer" :width="25" :height="25" />-->
                    <!--                        </template>-->
                    <!--                        <span>移除全部</span>-->
                    <!--                      </n-popconfirm>-->
                    <!--                      <n-popover placement="right">-->
                    <!--                        <template #trigger>-->
                    <!--                          <j-icon-->
                    <!--                            name="add2"-->
                    <!--                            style="cursor: pointer"-->
                    <!--                            :width="25"-->
                    <!--                            :height="25"-->
                    <!--                            @click="appendResearchIndex"-->
                    <!--                          />-->
                    <!--                        </template>-->
                    <!--                        添加-->
                    <!--                      </n-popover>-->
                    <!--                    </n-flex>-->
                    <!--                    <j-n-data-table bordered :columns="researchAndAssessmentColumns" :data="saveForm.researchIndexData" />-->
                    <editable
                      ref="researchIndexRef"
                      v-model:data="saveForm.researchIndexData"
                      :columns="researchAndAssessmentColumns"
                      :min="1"
                      :hide-all-action-button="readonly && operationType !== ProjectOperationType.FILL_BRIEF"
                      hide-copy-button
                      hide-sort-button
                      hide-add-button
                      return-serial
                      serial-field="semiAnnualPeriod"
                      serial-title="阶段"
                      show-tooltip
                    />
                    <n-descriptions
                      bordered
                      size="large"
                      label-align="center"
                      label-placement="left"
                      label-class="descriptions-label"
                      :column="8"
                    >
                      <n-descriptions-item :span="8" label="其他说明">
                        <n-form-item path="researchIndexSupply.otherDescr">
                          <n-input
                            class="enlarged"
                            size="large"
                            type="textarea"
                            maxlength="200"
                            show-count
                            clearable
                            :autosize="{ minRows: 3 }"
                            :disabled="operationType === ProjectOperationType.DETAILS"
                            v-model:value="saveForm.researchIndexSupply.otherDescr"
                          />
                        </n-form-item>
                      </n-descriptions-item>
                    </n-descriptions>
                  </div>

                  <div
                    id="d1102"
                    class="declaration-part-subtitle"
                    v-if="operationType !== ProjectOperationType.APPLY_2023 && !isProject2023"
                  >
                    <n-h4 prefix="bar"><strong>预期目标(不超过1000字)</strong></n-h4>
                    <n-descriptions bordered label-class="descriptions-label" label-style="display: none;" :columns="8">
                      <n-descriptions-item :span="8">
                        <n-form-item path="researchIndexSupply.expectations">
                          <wang-editor
                            v-model="saveForm.researchIndexSupply.expectations"
                            placeholder="请输入预期目标(不超过1000字)"
                            :readonly="readonly"
                            :max-length="1000"
                          />
                        </n-form-item>
                      </n-descriptions-item>
                    </n-descriptions>
                  </div>
                </div>

                <!--                八、实验研究仪器/设备登记-->
                <div class="declaration-part" id="d13">
                  <n-divider title-placement="center">
                    <span class="declaration-part-title">八、实验研究仪器/设备登记</span>
                  </n-divider>
                  <!--                  <n-flex v-if="!readonly" style="margin-bottom: 24px" justify="center" :size="48">-->
                  <!--                    <n-popconfirm placement="left" @positive-click="deleteAllEquRegNode()">-->
                  <!--                      <template #trigger>-->
                  <!--                        <j-icon name="delete" style="cursor: pointer" :width="25" :height="25" />-->
                  <!--                      </template>-->
                  <!--                      <span>移除全部</span>-->
                  <!--                    </n-popconfirm>-->
                  <!--                    <n-popover placement="right">-->
                  <!--                      <template #trigger>-->
                  <!--                        <j-icon-->
                  <!--                          name="add2"-->
                  <!--                          style="cursor: pointer"-->
                  <!--                          :width="25"-->
                  <!--                          :height="25"-->
                  <!--                          @click="addEquipmentReg()"-->
                  <!--                        />-->
                  <!--                      </template>-->
                  <!--                      添加-->
                  <!--                    </n-popover>-->
                  <!--                  </n-flex>-->
                  <!--                  <j-n-data-table-->
                  <!--                    ref="instrumentsAndEquipmentRef"-->
                  <!--                    bordered-->
                  <!--                    :columns="instrumentsAndEquipmentColumns"-->
                  <!--                    :data="saveForm.equipmentRegData"-->
                  <!--                  />-->
                  <editable
                    ref="instrumentsEquipmentRef"
                    v-model:data="saveForm.equipmentRegData"
                    :columns="instrumentsAndEquipmentColumns"
                    :hide-all-action-button="readonly"
                    return-serial
                    serial-field="seq"
                    show-tooltip
                    hide-serial
                  />
                </div>

                <!--                九、经费预算-->
                <div class="declaration-part" id="d14">
                  <n-divider title-placement="center">
                    <span class="declaration-part-title">九、经费预算</span>
                    <n-tooltip trigger="hover">
                      <template #trigger>
                        <n-icon style="cursor: pointer">
                          <QuestionFilled />
                        </n-icon>
                      </template>
                      按申报项目目标任务需要据实填报
                    </n-tooltip>
                  </n-divider>
                  <h3 style="text-align: center; margin-bottom: 3%">经费预算总表(单位：万元)</h3>

                  <div id="d1401" class="declaration-part-subtitle">
                    <n-h4 prefix="bar"><strong>经费来源</strong></n-h4>
                    <n-descriptions
                      bordered
                      size="large"
                      label-align="center"
                      label-placement="top"
                      label-class="descriptions-label"
                      :column="3"
                    >
                      <n-descriptions-item label="申请项目专项经费">
                        <n-form-item path="rmsProjectFundingIncomeDto.applyProjFunds">
                          <!--                            <input-->
                          <!--                              class="nInput"-->
                          <!--                              type="number"-->
                          <!--                              min="0"-->
                          <!--                              v-model="saveForm.rmsProjectFundingIncomeDto.applyProjFunds"-->
                          <!--                              :disabled="readonly"-->
                          <!--                            />-->
                          <n-input-number
                            class="enlarged widened"
                            size="large"
                            v-model:value="saveForm.rmsProjectFundingIncomeDto.applyProjFunds"
                            :min="0"
                            :max="saveForm.rmsProjectFundingIncomeDto.projTotal"
                            clearable
                            :precision="2"
                            :show-button="false"
                            :disabled="operationType === ProjectOperationType.DETAILS"
                            @update:value="(val: number) => saveForm.rmsProjectFundingIncomeDto.selfFundedFunds = new Decimal(saveForm.rmsProjectFundingIncomeDto.projTotal ?? 0).sub(val ?? 0).toNumber()"
                          >
                            <template #prefix>￥</template>
                            <template #suffix>万元</template>
                          </n-input-number>
                        </n-form-item>
                      </n-descriptions-item>
                      <n-descriptions-item label="自筹经费">
                        <n-form-item path="rmsProjectFundingIncomeDto.selfFundedFunds">
                          <!--                            <input-->
                          <!--                              class="nInput"-->
                          <!--                              type="number"-->
                          <!--                              min="0"-->
                          <!--                              v-model="saveForm.rmsProjectFundingIncomeDto.selfFundedFunds"-->
                          <!--                              :disabled="readonly"-->
                          <!--                            />-->
                          <n-input-number
                            class="enlarged widened"
                            size="large"
                            v-model:value="saveForm.rmsProjectFundingIncomeDto.selfFundedFunds"
                            :min="0"
                            :max="saveForm.rmsProjectFundingIncomeDto.projTotal"
                            clearable
                            :precision="2"
                            :show-button="false"
                            :disabled="operationType === ProjectOperationType.DETAILS"
                            @update:value="(val: number) => saveForm.rmsProjectFundingIncomeDto.applyProjFunds = new Decimal(saveForm.rmsProjectFundingIncomeDto.projTotal ?? 0).sub(new Decimal(val ?? 0)).toNumber()"
                          >
                            <template #prefix>￥</template>
                            <template #suffix>万元</template>
                          </n-input-number>
                        </n-form-item>
                      </n-descriptions-item>
                      <n-descriptions-item>
                        <template #label>
                          项目总经费
                          <n-popover trigger="hover">
                            <template #trigger>
                              <n-icon :size="14" style="cursor: pointer">
                                <QuestionFilled />
                              </n-icon>
                            </template>
                            请前往
                            <n-button type="info" text @click="anchorScrollTo('#d0201')">经费预算</n-button>
                            处填报
                          </n-popover>
                        </template>
                        <n-form-item path="rmsProjectFundingIncomeDto.projTotal">
                          <!--                            <input-->
                          <!--                              class="nInput"-->
                          <!--                              type="number"-->
                          <!--                              v-model="saveForm.rmsProjectFundingIncomeDto.projTotal"-->
                          <!--                              :min="-->
                          <!--                                saveForm.rmsProjectFundingIncomeDto.applyProjFunds +-->
                          <!--                                saveForm.rmsProjectFundingIncomeDto.selfFundedFunds-->
                          <!--                              "-->
                          <!--                              :disabled="readonly"-->
                          <!--                            />-->
                          <n-input-number
                            class="enlarged widened"
                            size="large"
                            v-model:value="saveForm.rmsProjectFundingIncomeDto.projTotal"
                            :min="0"
                            clearable
                            :precision="2"
                            :show-button="false"
                            :disabled="true"
                          >
                            <template #prefix>￥</template>
                            <template #suffix>万元</template>
                          </n-input-number>
                        </n-form-item>
                      </n-descriptions-item>
                    </n-descriptions>
                  </div>

                  <div id="d1402" class="declaration-part-subtitle">
                    <n-h4 prefix="bar"><strong>经费支出</strong></n-h4>
                    <j-n-data-table
                      :expanded-row-keys="expandedRowKeys"
                      :columns="expendProFundsColumns"
                      :data="saveForm.fundsExpenseTreeData"
                      :row-key="fundsExpenseRowKey"
                    />
                  </div>
                </div>

                <!--                十、项目申报单位、合作单位及主要研究人员情况-->
                <div class="declaration-part" id="d15">
                  <n-divider title-placement="center">
                    <span class="declaration-part-title">十、项目申报单位、合作单位及主要研究人员情况</span>
                  </n-divider>

                  <div id="d1501" class="declaration-part-subtitle">
                    <n-h4 prefix="bar"><strong>申报单位</strong></n-h4>
                    <n-descriptions
                      bordered
                      size="large"
                      label-align="center"
                      label-placement="left"
                      label-class="descriptions-label"
                      :column="4"
                    >
                      <n-descriptions-item :span="2" label="单位名称">
                        <n-form-item path="projectOrgStructure.acName">
                          <input :disabled="readonly" v-model="saveForm.projectOrgStructure.acName" class="nInput" />
                        </n-form-item>
                      </n-descriptions-item>
                      <n-descriptions-item :span="2" label="组织机构代码">
                        <n-form-item path="projectOrgStructure.acCode">
                          <input :disabled="readonly" v-model="saveForm.projectOrgStructure.acCode" class="nInput" />
                        </n-form-item>
                      </n-descriptions-item>
                      <n-descriptions-item :span="2" label="地址">
                        <n-form-item path="projectOrgStructure.acAddr">
                          <input :disabled="readonly" v-model="saveForm.projectOrgStructure.acAddr" class="nInput" />
                        </n-form-item>
                      </n-descriptions-item>
                      <n-descriptions-item :span="2" label="邮政编码">
                        <n-form-item path="projectOrgStructure.acPostal">
                          <input :disabled="readonly" v-model="saveForm.projectOrgStructure.acPostal" class="nInput" />
                        </n-form-item>
                      </n-descriptions-item>
                      <n-descriptions-item :span="4" label="单位类别">
                        <n-form-item path="projectOrgStructure.acType">
                          <input :disabled="readonly" v-model="saveForm.projectOrgStructure.acType" class="nInput" />
                        </n-form-item>
                      </n-descriptions-item>
                      <n-descriptions-item :span="4" label="单位主管部门">
                        <n-form-item path="projectOrgStructure.acDept">
                          <input :disabled="readonly" v-model="saveForm.projectOrgStructure.acDept" class="nInput" />
                        </n-form-item>
                      </n-descriptions-item>
                      <n-descriptions-item :span="2" label="负责人">
                        <n-form-item path="projectOrgStructure.acRespe">
                          <input :disabled="readonly" v-model="saveForm.projectOrgStructure.acRespe" class="nInput" />
                        </n-form-item>
                      </n-descriptions-item>
                      <n-descriptions-item :span="2" label="联系部门">
                        <n-form-item path="projectOrgStructure.acRespeDept">
                          <input
                            :disabled="readonly"
                            v-model="saveForm.projectOrgStructure.acRespeDept"
                            class="nInput"
                          />
                        </n-form-item>
                      </n-descriptions-item>
                      <n-descriptions-item label="联系人">
                        <n-form-item path="projectOrgStructure.acContact">
                          <input :disabled="readonly" v-model="saveForm.projectOrgStructure.acContact" class="nInput" />
                        </n-form-item>
                      </n-descriptions-item>
                      <n-descriptions-item label="联系人座机">
                        <n-form-item path="projectOrgStructure.acContactLandline">
                          <input
                            :disabled="readonly"
                            v-model="saveForm.projectOrgStructure.acContactLandline"
                            class="nInput"
                          />
                        </n-form-item>
                      </n-descriptions-item>
                      <n-descriptions-item :span="2" label="联系人手机">
                        <n-form-item path="projectOrgStructure.acContactPhone">
                          <input
                            :disabled="readonly"
                            v-model="saveForm.projectOrgStructure.acContactPhone"
                            class="nInput"
                          />
                        </n-form-item>
                      </n-descriptions-item>
                      <n-descriptions-item>
                        <template #label>
                          职工人数
                          <n-tooltip trigger="hover">
                            <template #trigger>
                              <n-icon size="12" style="cursor: pointer">
                                <QuestionFilled />
                              </n-icon>
                            </template>
                            数据由系统自动获取
                          </n-tooltip>
                        </template>
                        <n-form-item path="projectOrgStructure.empCount">
                          <input
                            ref="empCountRef"
                            :disabled="readonly"
                            v-model="saveForm.projectOrgStructure.empCount"
                            class="nInput"
                          />
                        </n-form-item>
                      </n-descriptions-item>
                      <n-descriptions-item label="单位性质">
                        <n-form-item path="projectOrgStructure.orgNature">
                          <input :disabled="readonly" v-model="saveForm.projectOrgStructure.orgNature" class="nInput" />
                        </n-form-item>
                      </n-descriptions-item>
                      <n-descriptions-item :span="2" label="上级行政主管部门">
                        <n-form-item path="projectOrgStructure.upperAdminDept">
                          <input
                            :disabled="readonly"
                            v-model="saveForm.projectOrgStructure.upperAdminDept"
                            class="nInput"
                          />
                        </n-form-item>
                      </n-descriptions-item>
                    </n-descriptions>
                  </div>

                  <div id="d1502" class="declaration-part-subtitle">
                    <n-h4 prefix="bar"><strong>合作单位</strong></n-h4>
                    <!--                    <n-flex v-if="!readonly" style="margin-bottom: 24px" justify="center" :size="48">-->
                    <!--                      <n-popconfirm placement="left" @positive-click="deleteAllCooperativeUnits()">-->
                    <!--                        <template #trigger>-->
                    <!--                          <j-icon name="delete" style="cursor: pointer" :width="25" :height="25" />-->
                    <!--                        </template>-->
                    <!--                        <span>移除全部</span>-->
                    <!--                      </n-popconfirm>-->
                    <!--                      <n-popover placement="right">-->
                    <!--                        <template #trigger>-->
                    <!--                          <j-icon-->
                    <!--                            name="add2"-->
                    <!--                            style="cursor: pointer"-->
                    <!--                            :width="25"-->
                    <!--                            :height="25"-->
                    <!--                            @click="addCooperativeUnitsLine()"-->
                    <!--                          />-->
                    <!--                        </template>-->
                    <!--                        添加-->
                    <!--                      </n-popover>-->
                    <!--                    </n-flex>-->
                    <!--                    <j-n-data-table-->
                    <!--                      ref="cooperativeUnitsRef"-->
                    <!--                      bordered-->
                    <!--                      :columns="cooperativeUnitsColumns"-->
                    <!--                      :data="saveForm.cooperativeUnitsData"-->
                    <!--                    />-->
                    <editable
                      ref="cooperativeUnitRef"
                      v-model:data="saveForm.cooperativeUnitsData"
                      :columns="cooperativeUnitsColumns"
                      :hide-all-action-button="readonly"
                      return-serial
                      serial-field="seq"
                      show-tooltip
                    />
                  </div>

                  <div id="d1503" class="declaration-part-subtitle">
                    <n-h4 prefix="bar">
                      <strong>项目负责人</strong>
                      (此处仅供展示，请前往
                      <n-button type="info" text @click="anchorScrollTo('#d16')">课题组主要成员</n-button>
                      填报)
                    </n-h4>
                    <n-descriptions
                      bordered
                      size="large"
                      label-align="center"
                      label-placement="left"
                      label-class="descriptions-label"
                      label-style="width: 11%; "
                      :column="4"
                    >
                      <n-descriptions-item label="姓名">
                        <n-form-item path="projectOrgStructure.projectResp">
                          <input disabled v-model="saveForm.projectOrgStructure.projectResp" class="nInput" />
                        </n-form-item>
                      </n-descriptions-item>
                      <n-descriptions-item label="性别">
                        <n-form-item path="projectOrgStructure.prSex">
                          <n-radio-group v-model:value="saveForm.projectOrgStructure.prSex" disabled>
                            <n-flex justify="space-between">
                              <n-radio
                                v-for="item in options.GENDERS"
                                class="enlarged"
                                size="large"
                                :key="item.value"
                                :value="item.value"
                                :label="item.label"
                              />
                            </n-flex>
                          </n-radio-group>
                        </n-form-item>
                      </n-descriptions-item>
                      <n-descriptions-item label="出生年月">
                        <n-form-item path="projectOrgStructure.prBirthday">
                          <n-date-picker
                            class="enlarged widened"
                            type="month"
                            size="large"
                            v-model:formatted-value="saveForm.projectOrgStructure.prBirthday"
                            :is-date-disabled="(current: number) => current > Date.now()"
                            disabled
                          />
                        </n-form-item>
                      </n-descriptions-item>
                      <n-descriptions-item label="学历(学位)" content-style="width: 130px; ">
                        <n-form-item path="projectOrgStructure.prAcademic">
                          <n-select
                            class="enlarged"
                            size="large"
                            v-model:value="saveForm.projectOrgStructure.prAcademic"
                            :options="options.ACADEMIC_DEGREE"
                            disabled
                          />
                        </n-form-item>
                      </n-descriptions-item>
                      <n-descriptions-item label="职称">
                        <n-form-item path="projectOrgStructure.prTitle">
                          <input disabled v-model="saveForm.projectOrgStructure.prTitle" class="nInput" />
                        </n-form-item>
                      </n-descriptions-item>
                      <n-descriptions-item label="手机">
                        <n-form-item path="projectOrgStructure.prPhone">
                          <input disabled v-model="saveForm.projectOrgStructure.prPhone" class="nInput" />
                        </n-form-item>
                      </n-descriptions-item>
                      <n-descriptions-item label="从事专业">
                        <n-form-item path="projectOrgStructure.prProfession">
                          <input disabled v-model="saveForm.projectOrgStructure.prProfession" class="nInput" />
                        </n-form-item>
                      </n-descriptions-item>
                    </n-descriptions>
                  </div>

                  <div id="d1504" class="declaration-part-subtitle">
                    <n-h4 prefix="bar"><strong>项目人数</strong></n-h4>
                    <n-descriptions
                      bordered
                      size="large"
                      label-align="center"
                      label-placement="left"
                      label-class="descriptions-label"
                      label-style="width: 10%; "
                      :column="4"
                    >
                      <n-descriptions-item :span="4">
                        <template #label>
                          项目总人数
                          <n-tooltip trigger="hover">
                            <template #trigger>
                              <n-icon style="cursor: pointer">
                                <QuestionFilled />
                              </n-icon>
                            </template>
                            根据项目组主要成员信息自动计算
                          </n-tooltip>
                        </template>
                        <n-form-item path="projectOrgStructure.projectTeamCount">
                          <input
                            class="nInput"
                            type="number"
                            min="0"
                            v-model="saveForm.projectOrgStructure.projectTeamCount"
                            disabled
                          />
                        </n-form-item>
                      </n-descriptions-item>
                      <n-descriptions-item label="高级">
                        <n-form-item path="projectOrgStructure.proSeniorCt">
                          <!--                            <input-->
                          <!--                                class="nInput"-->
                          <!--                                type="number"-->
                          <!--                                min="0"-->
                          <!--                                :max="maxValue.senior"-->
                          <!--                                v-model="saveForm.projectOrgStructure.proSeniorCt"-->
                          <!--                            />-->
                          <n-input-number
                            class="enlarged widened"
                            size="large"
                            clearable
                            v-model:value="saveForm.projectOrgStructure.proSeniorCt"
                            :min="0"
                            :max="saveForm.projectOrgStructure.projectTeamCount"
                            :precision="0"
                            :show-button="false"
                            :disabled="readonly"
                          />
                        </n-form-item>
                      </n-descriptions-item>
                      <n-descriptions-item label="中级">
                        <n-form-item path="projectOrgStructure.proMiddleCt">
                          <!--                            <input-->
                          <!--                                class="nInput"-->
                          <!--                                type="number"-->
                          <!--                                min="0"-->
                          <!--                                v-model="saveForm.projectOrgStructure.proMiddleCt"-->
                          <!--                            />-->
                          <n-input-number
                            class="enlarged widened"
                            size="large"
                            clearable
                            v-model:value="saveForm.projectOrgStructure.proMiddleCt"
                            :min="0"
                            :max="saveForm.projectOrgStructure.projectTeamCount"
                            :precision="0"
                            :show-button="false"
                            :disabled="readonly"
                          />
                        </n-form-item>
                      </n-descriptions-item>
                      <n-descriptions-item label="初级">
                        <n-form-item path="projectOrgStructure.proJuniorCt">
                          <!--                            <input-->
                          <!--                                class="nInput"-->
                          <!--                                type="number"-->
                          <!--                                min="0"-->
                          <!--                                v-model="saveForm.projectOrgStructure.proJuniorCt"-->
                          <!--                            />-->
                          <n-input-number
                            class="enlarged widened"
                            size="large"
                            clearable
                            v-model:value="saveForm.projectOrgStructure.proJuniorCt"
                            :min="0"
                            :max="saveForm.projectOrgStructure.projectTeamCount"
                            :precision="0"
                            :show-button="false"
                            :disabled="readonly"
                          />
                        </n-form-item>
                      </n-descriptions-item>
                      <n-descriptions-item label="其它">
                        <n-form-item path="projectOrgStructure.proOtherCt">
                          <!--                            <input-->
                          <!--                                class="nInput"-->
                          <!--                                type="number"-->
                          <!--                                min="0"-->
                          <!--                                v-model="saveForm.projectOrgStructure.proOtherCt"-->
                          <!--                                disabled-->
                          <!--                            />-->
                          <n-input-number
                            class="enlarged widened"
                            size="large"
                            clearable
                            v-model:value="saveForm.projectOrgStructure.proOtherCt"
                            :min="0"
                            :max="saveForm.projectOrgStructure.projectTeamCount"
                            :precision="0"
                            :show-button="false"
                            :disabled="readonly"
                          />
                        </n-form-item>
                      </n-descriptions-item>
                    </n-descriptions>
                  </div>
                </div>

                <!--                课题组主要人员情况-->
                <div class="declaration-part" id="d16">
                  <n-divider title-placement="center">
                    <span class="declaration-part-title">课题组主要人员情况</span>
                    <n-tooltip trigger="hover">
                      <template #trigger>
                        <n-icon style="cursor: pointer">
                          <QuestionFilled />
                        </n-icon>
                      </template>
                      含课题负责人
                    </n-tooltip>
                  </n-divider>

                  <!-- d1601跳转 -->
                  <div id="d1601" class="declaration-part-subtitle">
                    <n-h4 prefix="bar">
                      <n-flex justify="space-between" align="center">
                        <strong
                          >课题组主要成员（
                          <span style="color: red">该顺序与职称评审相关，请谨慎填报！！！</span>
                          ）
                        </strong>
                        <n-button text type="primary" @click="printResearchers">
                          <template #icon>
                            <n-icon><PrintOutline /></n-icon>
                          </template>
                          打印
                        </n-button>
                      </n-flex>
                    </n-h4>
                    
                    <editable
                      ref="mainMemberRef"
                      v-model:data="saveForm.researchersData"
                      :columns="researchersOfGroupColumns"
                      :hide-all-action-button="readonly && operationType !== ProjectOperationType.FILL_BRIEF"
                      :hide-action-button-index="[0]"
                      :min="1"
                      return-serial
                      serial-field="seq"
                      show-tooltip
                    />
                  </div>

                  <!-- 新建项目时课题组成员签字附件 -->
                  <div id="d1602" class="declaration-part-subtitle">
                    <n-h4 prefix="bar"><strong>课题组成员签字附件</strong></n-h4>
                    <n-form-item path="researchersSignAttachments">
                      <!--                      <n-upload-->
                      <!--                        ref="researchersSignAttachmentsRef"-->
                      <!--                        v-model:file-list="researchersSignAttachmentsConfig.fileList"-->
                      <!--                        :list-type="researchersSignAttachmentsConfig.listType"-->
                      <!--                        :create-thumbnail-url="researchersSignAttachmentsConfig.createThumbnailUrl"-->
                      <!--                        :accept="researchersSignAttachmentsConfig.accept"-->
                      <!--                        :multiple="researchersSignAttachmentsConfig.multiple"-->
                      <!--                        :max="researchersSignAttachmentsConfig.max"-->
                      <!--                        :custom-request="researchersSignAttachmentsConfig.customRequest"-->
                      <!--                        :disabled="researchersSignAttachmentsConfig.disabled"-->
                      <!--                        :show-download-button="researchersSignAttachmentsConfig.showDownloadButton"-->
                      <!--                        @remove="researchersSignAttachmentsConfig.remove"-->
                      <!--                        @change="researchersSignAttachmentsConfig.change"-->
                      <!--                        @download="researchersSignAttachmentsConfig.download"-->
                      <!--                      >-->
                      <!--                        <n-upload-dragger>-->
                      <!--                          <div style="margin-bottom: 12px">-->
                      <!--                            <n-icon size="48" :depth="3">-->
                      <!--                              <UploadFilled />-->
                      <!--                            </n-icon>-->
                      <!--                          </div>-->
                      <!--                          <n-text style="font-size: 16px"> 点击或者拖动文件到该区域来上传 </n-text>-->
                      <!--                          <n-p depth="3" style="margin: 8px 0 0 0">-->
                      <!--                            {{ researchersSignAttachmentsConfig.tips }}-->
                      <!--                          </n-p>-->
                      <!--                        </n-upload-dragger>-->
                      <!--                      </n-upload>-->
                      <yt-upload
                        v-model:file-list="saveForm.researchersSignAttachments"
                        :upload-fn="uploadRmsFile"
                        :maxSize="5 * 1024"
                        :disabled="operationType === ProjectOperationType.DETAILS"
                        accept="image/*"
                        drag
                        auto-upload
                        @before-upload="disableSaveDraft"
                        @success="enableSaveDraft"
                        @error="enableSaveDraft"
                      />
                    </n-form-item>
                  </div>
                </div>
                <!-- 技术查新 -->
                <div class="declaration-part" v-if="operationType === ProjectOperationType.PROJECT_DECLARATION || operationType === ProjectOperationType.PROCESS_DETAILS">
                  <n-divider title-placement="center">
                    <span id="d18" class="declaration-part-title">技术查新</span>
                  </n-divider>
                  <n-h4 prefix="bar"><strong>技术查新相关附件</strong></n-h4>
                  <n-form-item label="技术查新附件" path="techQueryAttachment">
                    <yt-upload
                      v-model:file-list="saveForm.techQueryAttachment"
                      :upload-fn="uploadRmsFile"
                      accept=".pdf,.doc,.docx,.xls,.xlsx,.zip,.rar,.7z,image/*"
                      :limit="10"
                      :disabled="props.operationType === ProjectOperationType.DETAILS"
                      drag
                      @before-upload="disableSaveDraft"
                      @success="enableSaveDraft"
                      @error="enableSaveDraft"
                    />
                  </n-form-item>
                </div>
                <!-- 伦理审批 -->
                 <!-- <div class="declaration-part" v-if="operationType === ProjectOperationType.PROJECT_DECLARATION || operationType === ProjectOperationType.PROCESS_DETAILS">
                  <n-divider title-placement="center">
                    <span id="d18" class="declaration-part-title">伦理审批附件</span>
                  </n-divider>
                  <n-h4 prefix="bar"><strong>伦理审批相关附件</strong></n-h4>
                  <n-form-item label="伦理审批附件" path="ethicsApprovalAttachment">
                    <yt-upload
                      v-model:file-list="saveForm.ethicsApprovalAttachment"
                      :upload-fn="uploadRmsFile"
                      accept=".pdf,.doc,.docx,.xls,.xlsx,.zip,.rar,.7z,image/*"
                      :limit="10"
                      :disabled="operationType === ProjectOperationType.PROCESS_DETAILS"
                      drag
                      @before-upload="disableSaveDraft"
                      @success="enableSaveDraft"
                      @error="enableSaveDraft"
                    />
                  </n-form-item>
                 </div> -->
                 <!-- 申报书备案 -->
                  <div class="declaration-part" v-if="operationType === ProjectOperationType.PROJECT_DECLARATION || operationType === ProjectOperationType.PROCESS_DETAILS">
                      <n-divider title-placement="center">
                      <span id="d18" class="declaration-part-title">申报书备案附件(必传)</span>
                    </n-divider>
                    <n-h4 prefix="bar"><strong>伦理审批相关附件</strong></n-h4>
                    <n-form-item label="伦理审批附件" path="applicationDeclarationAttachment">
                      <yt-upload
                        v-model:file-list="saveForm.applicationDeclarationAttachment"
                        :upload-fn="uploadRmsFile"
                        accept=".pdf,.doc,.docx,.xls,.xlsx,.zip,.rar,.7z,image/*"
                        :limit="10"
                        :disabled="props.operationType === ProjectOperationType.DETAILS"
                        drag
                        @before-upload="disableSaveDraft"
                        @success="enableSaveDraft"
                        @error="enableSaveDraft"
                      />
                    </n-form-item>
                  </div>
                  <!-- 承诺书相关附件 -->
                    <div class="declaration-part" v-if="operationType === ProjectOperationType.PROJECT_DECLARATION || operationType === ProjectOperationType.PROCESS_DETAILS">
                       <n-divider title-placement="center">
                        <span id="d17" class="declaration-part-title">诚信承诺书相关附件</span>
                      </n-divider>
                      <n-h4 prefix="bar"><strong>承诺书附件</strong></n-h4>
                        <n-form-item label="承诺书附件" path="promiseAttachment">
                      <yt-upload
                        v-model:file-list="saveForm.promiseAttachment"
                        :upload-fn="uploadRmsFile"
                        accept=".pdf,.doc,.docx,.xls,.xlsx,.zip,.rar,.7z,image/*"
                        :limit="5"
                        :disabled="props.operationType === ProjectOperationType.DETAILS"
                        drag
                        @before-upload="disableSaveDraft"
                        @success="enableSaveDraft"
                        @error="enableSaveDraft"
                      />
                      </n-form-item>
                    </div>

                <div
                  class="declaration-part"
                  id="d18"
                  v-if="
                    operationType === ProjectOperationType.PROCESS_DETAILS &&
                    (saveForm.intendedUnitId || saveForm.intendedUnitId === 0)
                  "
                >
                  <n-divider title-placement="center">
                    <span class="declaration-part-title">意向单位</span>
                  </n-divider>

                  <n-grid :cols="2" :x-gap="24">
                    <n-gi>
                      <n-form-item
                        label="意向单位"
                        path="intendedUnit"
                        label-placement="left"
                        label-style="font-size: 14px;"
                        show-label
                      >
                        <n-select v-model:value="saveForm.intendedUnitId" :options="options.INTENDED_UNIT" filterable />
                      </n-form-item>
                    </n-gi>
                    <n-gi>
                      <n-form-item label="单位名称" path="intendedUnitOther" v-if="saveForm.intendedUnitId === 0">
                        <n-input
                          v-model:value="saveForm.intendedUnitOther"
                          :maxlength="50"
                          placeholder="请输入单位名称"
                        />
                      </n-form-item>
                    </n-gi>
                    <template v-if="saveForm.intendedUnitId || saveForm.intendedUnitId === 0">
                      <n-gi>
                        <n-form-item
                          label="科研专项"
                          path="intendedUnitSpecialId"
                          label-placement="left"
                          label-style="font-size: 14px;"
                          show-label
                        >
                          <n-select
                            v-model:value="saveForm.intendedUnitSpecialId"
                            :options="options.INTENDED_UNIT_SPECIAL"
                            filterable
                          />
                        </n-form-item>
                      </n-gi>
                      <n-gi>
                        <n-form-item
                          label="专项名称"
                          path="intendedUnitSpecialOther"
                          v-if="saveForm.intendedUnitSpecialId === 0"
                        >
                          <n-input
                            v-model:value="saveForm.intendedUnitSpecialOther"
                            :maxlength="50"
                            placeholder="请输入专项名称"
                          />
                        </n-form-item>
                      </n-gi>
                    </template>
                  </n-grid>
                </div>

                <div
                  class="declaration-part"
                  id="d19"
                  v-if="
                    (saveForm.promiseAttachments?.length > 0 || operationType === ProjectOperationType.FILL_BRIEF) &&
                    !props.hidePromise
                  "
                >
                  <n-h4 prefix="bar"><strong>任务书填报</strong></n-h4>
                  <n-form-item label="承诺书" path="promiseAttachments">
                    <yt-upload
                      v-model:file-list="saveForm.promiseAttachments"
                      :upload-fn="uploadRmsFile"
                      :maxSize="5 * 1024"
                      :disabled="readonly && operationType !== ProjectOperationType.FILL_BRIEF"
                      accept=".pdf,.doc,.docx,image/*"
                      drag
                      auto-upload
                    />
                  </n-form-item>
                </div>

                <!-- 项目申报书上传部分，仅在 APPLY_2023 模式下显示 -->
                <div
                  class="declaration-part"
                  id="d99"
                  v-if="operationType === ProjectOperationType.APPLY_2023 || isProject2023"
                >
                  <n-divider title-placement="center">
                    <span class="declaration-part-title">项目申报书上传</span>
                  </n-divider>
                  <div class="declaration-part-subtitle"> 请上传项目申报书文件（支持PDF、Word、Excel格式） </div>
                  <n-form-item path="applicationFiles" class="mt-4">
                    <yt-upload
                      v-model:file-list="saveForm.applicationFiles"
                      :upload-fn="uploadRmsFile"
                      accept=".pdf,.doc,.docx,.xls,.xlsx"
                      :maxSize="10 * 1024"
                      auto-upload
                      drag
                    />
                  </n-form-item>
                </div>
              </div>
            </div>
          </div>
        </n-form>
      </n-grid-item>
      <n-grid-item :span="1">
        <j-preview v-model:show="preview.show" :bucket="preview.bucket" :oss-path="preview.ossPath" />
        <n-affix
          ref="operationButtonRef"
          position="absolute"
          :top="53"
          :listen-to="() => containerRef"
          style="right: 2%; width: 18%"
        >
          <n-flex vertical style="margin: 20px 0 20px 0">
            <n-button
              v-if="projectId && operationType !== ProjectOperationType.PROCESS_DETAILS"
              type="primary"
              :loading="loading.downloadDeclaration"
              :disabled="disabled.downloadDeclaration"
              @click="downloadDeclaration"
            >
              下载申报书
            </n-button>
            <n-button
              v-if="!readonly || operationType === ProjectOperationType.FILL_BRIEF"
              type="info"
              :loading="loading.saveDraft"
              :disabled="disabled.saveDraft"
              @click="saveDraft"
            >
              {{ operationType === ProjectOperationType.FILL_BRIEF ? '保存任务书' : '保存详情草稿' }}
            </n-button>
            <!--            <n-button type="warning"-->
            <!--                      :loading="loading.submitDeclaration"-->
            <!--                      @click="submitDeclaration"-->
            <!--                      v-if="!readonly">-->
            <!--              提交申报信息-->
            <!--            </n-button>-->
          </n-flex>
        </n-affix>
        <!--  导航栏  -->
        <n-anchor
          ref="anchorRef"
          affix
          :listen-to="() => containerRef"
          :trigger-top="24"
          :top="anchorTop"
          :bound="72"
          style="z-index: 100; height: 70%; width: 18%; overflow: scroll"
        >
          <recursive-anchor-link :data="anchorList" :anchor="anchorRef" />
        </n-anchor>
      </n-grid-item>
    </n-grid>
  </div>
</template>
<script lang="ts" setup>
  import { computed, ComputedRef, h, onMounted, ref, watch } from 'vue'
  import { getOptionsOfSelections, queryRmsCheckBoxSelection } from '@/api/rms/config/CheckBoxSelectionWeb'
  import { queryRmsResearchTopic } from '@/api/rms/topicManager/ResearchTopicWeb'
  import {
    AnchorInst,
    DataTableColumn,
    DataTableInst,
    FormInst,
    FormItemRule,
    FormValidationError,
    NButton,
    NCheckbox,
    NDatePicker,
    NFormItem,
    NIcon,
    NInput,
    NInputNumber,
    NRadio,
    NRadioGroup,
    NSelect,
    NTooltip,
    UploadCustomRequestOptions,
    UploadFileInfo,
    UploadInst,
    UploadSettledFileInfo,
  } from 'naive-ui'
  import {
    generateDeclaration,
    projectInfoSupply,
    queryProjectDetails,
    submitBrief,
  } from '@/api/rms/projectApply/ProjectMainInfoWeb'
  import JPGlobal from '@/types/common/jglobal'
  import { StoreSysDict } from '@/types/modules/sys'
  import { Option } from '@jtypes'
  import { RegExpType } from '@/types/enums/enums.ts'
  import { getFutureYear, getRangeShortcuts, ifNull } from '@/utils'
  import { QuestionFilled } from '@element-plus/icons-vue'
  import { getSourceFileUrlOutsideChain } from '@/api/common/common.ts'
  import {
    CooperativeUnit,
    DeclarationValidateKey,
    Expenditure,
    ExpenditureTreeNode,
    FundingSource,
    InstrumentsEquipment,
    MainMember,
    OrganizationalStructure,
    PerformanceGoal,
    ProjectDetailProps,
    ProjectInfo,
    ProjectOperationType,
    ResearchIndex,
    ResearchIndexSupply,
    RichText,
  } from '@/types/modules/rms/project-declare/projectDetail.ts'
  import { AnchorItem, Attachment, RecursiveRules, RmsOptions } from '@/types/modules/rms'
  import { FormattedValueDatetimeRange } from '@/views/modules/rms/projectApplication/types/interface.ts'
  import { queryRmsFundingBudgetCfg } from '@/api/rms/config/FundingBudgetCfgWeb.ts'
  import RecursiveAnchorLink from '@/views/modules/rms/components/recursive-anchor-link/index.vue'
  import Editable from '@/views/modules/rms/components/editable/index.vue'
  import { EditableColumn, EditableInst } from '@/views/modules/rms/components/editable/'
  import { queryEmployeeDetailsData, queryEmployeeTreeDict, withIncumbentNum } from '@/api/hrm/hrmEmp.ts'
  import { uploadRmsFile } from '@/api/rms/common/FileWeb.ts'
  import WangEditor from '@/views/modules/rms/components/wang-editor/index.vue'
  import YtUpload from '@/views/modules/rms/components/upload/index.vue'
  import axios from 'axios'
  import {
    RmsIntendedUnit,
    RmsIntendedUnitSpecial,
    RmsPerformanceIndicator,
    RmsProjectPerformanceGoal,
    RmsResearchTopic,
  } from '@/types/modules/rms/entity/inex.ts'
  import { queryRmsPerformanceIndicator } from '@/api/rms/config/PerformanceIndicatorWeb.ts'
  import {
    PerformanceIndicatorDataType,
    PerformanceIndicatorDataTypeEnum,
    RmsPerformanceIndicatorTree,
    RmsProjectPerformanceGoalVOTreeNode,
    RmsProjectPerformanceGoalVO,
    RmsProjectVO,
  } from '@/types/modules/rms/project-management'
  import { useUserStore } from '@/store'
  import JBusEmpSearch from '@/components/common/business/hrm/empSearch/index.vue'
  import Decimal from 'decimal.js'
  import { queryRmsIntendedUnit } from '@/api/rms/config/IntendedUnitWeb.ts'
  import { queryRmsIntendedUnitSpecial } from '@/api/rms/config/IntendedUnitSpecialWeb.ts'
  import RmsInputNumber from '@/views/modules/rms/projectApplication/components/input-number.vue'
  import { queryRmsProjectPerformanceGoalsByProjectId } from '@/api/rms/projectApply/ProjectPerformanceGoalWeb.ts'
  import { CogOutline, PersonCircleOutline, PrintOutline } from '@vicons/ionicons5'
  import { log } from 'mathjs'

  // type BaseForm = Pick<
  //   RmsProjectVO,
  //   | 'id'
  //   | 'projectName'
  //   | 'projectLeader'
  //   | 'telephone'
  //   | 'projectLevel'
  //   | 'topicCategory'
  //   | 'researchType'
  //   | 'externalCooperation'
  //   | 'projectStartDate'
  //   | 'projectEndDate'
  //   | 'projectInfo'
  //   | 'swotAttachments'
  //   | 'researchersSignAttachments'
  //   | 'rmsProjectPerformanceGoals'
  //   | 'performanceGoals'
  //   | 'researchIndexData'
  //   | 'researchIndexSupply'
  //   | 'equipmentRegData'
  //   | 'rmsProjectFundingIncomeDto'
  //   | 'fundsExpenseData'
  //   | 'projectOrgStructure'
  //   | 'cooperativeUnitsData'
  //   | 'researchersData'
  //   | 'richText'
  // >
  //
  // interface DeclareForm extends BaseForm {
  //   // 项目起止时间范围，仅用于
  //   projectDateRange: FormattedValueDatetimeRange
  // }

  interface SaveForm {
    // 项目ID
    id?: number
    // 项目名称
    projectName: string
    // 项目负责人
    projectLeader: string
    // 项目负责人用户名
    projectLeaderUsername: string
    // 联系电话
    telephone: string
    // 申报级别
    projectLevel: string
    // 课题类别
    topicCategory: string
    // 研究类型
    researchType: string
    // 是否与外单位合作
    externalCooperation: string
    // 项目起止时间范围，仅用于
    projectDateRange: FormattedValueDatetimeRange
    // 项目起始时间
    projectStartDate: string
    // 项目终止时间
    projectEndDate: string
    // 意向单位ID
    intendedUnitId?: number
    // 意向单位 #其他 意向单位ID为0时
    intendedUnitOther?: string
    // 科研专项ID
    intendedUnitSpecialId?: number
    // 科研专项 #其他 科研专项ID为0时
    intendedUnitSpecialOther?: string
    // 承诺书附件 #任务书
    promiseAttachments?: Attachment[]
    // 项目信息
    projectInfo: ProjectInfo
    // 六、已有研究基础、承担优势和项目实施的风险及应对策略的相关数据或成果佐证资料
    swotAttachments: Attachment[]
    // 项目绩效目标
    rmsProjectPerformanceGoalData: RmsProjectPerformanceGoal[]
    rmsProjectPerformanceGoals: PerformanceGoal
    performanceGoals: Record<string, PerformanceIndicatorDataType>
    // 分年度研究内容与考核指标
    researchIndexData: ResearchIndex[]
    // 研究内容与考核指标的补充说明与预期目标
    researchIndexSupply: ResearchIndexSupply
    //项目研究设备/仪器登记
    equipmentRegData: InstrumentsEquipment[]
    //项目收入经费
    rmsProjectFundingIncomeDto: FundingSource
    // 项目支出经费
    fundsExpenseData: Expenditure[]
    // 项目支出经费树形结构数据
    fundsExpenseTreeData: ExpenditureTreeNode[]
    // 项目组织结构与团队概况
    projectOrgStructure: OrganizationalStructure
    // 合作单位
    cooperativeUnitsData: CooperativeUnit[]
    // 课题组研究人员
    researchersData: MainMember[]
    // 课题组成员签字附件
    researchersSignAttachments: Attachment[]
    // 富文本信息
    richText: RichText
    // 项目申报书文件（仅用于23年项目申报）
    applicationFiles?: Attachment[]
    // 项目申报类型：0-正常填报，1-23年项目申报
    projectApplyType?: string

    // 其他键值对
    [p: string]: any
  }

  type SaveFormRules = RecursiveRules<SaveForm>

  const props = withDefaults(defineProps<ProjectDetailProps>(), {
    readonly: false,
    showPromiseAnchor: false,
    operationType: ProjectOperationType.ADD,
    hidePromise: false,
    /** 技术查新 */
    techQuery: false,
    /** 伦理审批 */
    ethicsApproval: false,
    /** 申报书备案 */
    applicationDeclaration: false,
  })

  const handleUpdateProjectLeader = (option: any) => {
    if (option && !props.readonly) {
      saveForm.value.projectLeaderUsername = option.empCode
      saveForm.value.projectLeader = option.empName
      saveForm.value.telephone = option.phone
      saveForm.value.researchersData[0].name = saveForm.value.projectLeader
      saveForm.value.researchersData[0].username = option.empCode
      saveForm.value.researchersData[0].sex = option.sex === '318' ? '1' : option.sex === '319' ? '2' : null
      saveForm.value.researchersData[0].birthday = JPGlobal.formatDatetime(option.birthday, 'yyyy-MM')
      saveForm.value.researchersData[0].idCard = option.icdCard
      saveForm.value.projectOrgStructure.projectResp = saveForm.value.projectLeader
      saveForm.value.projectOrgStructure.prSex = saveForm.value.researchersData[0].sex
      saveForm.value.projectOrgStructure.prBirthday = saveForm.value.researchersData[0].birthday
      saveForm.value.researchersData[0].workUnit = '中江县人民医院'

      console.log('option', option)
      // 查询学位信息
      queryEmployeeDetailsData({ empId: option.id }).then(res => {
        queryEmployeeTreeDict({ codeType: 'DEGREE_TYPE' }).then(res1 => {
          // 查找学位选项的函数
          const findDegreeOption = (treeData: any[], degreeId: string): any | null => {
            for (const node of treeData) {
              // 检查当前节点id是否匹配
              if (node.code === degreeId || node.id === Number(degreeId)) {
                return node
              }
              // 递归查找子节点
              if (node.children && node.children.length > 0) {
                const found = findDegreeOption(node.children, degreeId)
                if (found) return found
              }
            }
            return null
          }

          // 获取最高学历的学位ID
          const degreeId = res.data.educationList[res.data.educationList.length - 1].degree

          // 在学位树中查找对应选项
          const degreeOption = findDegreeOption(res1.data, degreeId)

          if (degreeOption) {
            // 获取学位的父级标签
            const parentCodeLabel = degreeOption.parentCodeLabel || degreeOption.label

            // 在ACADEMIC_DEGREE选项中查找匹配的标签
            const matchedOption = options.value.ACADEMIC_DEGREE.find(option => option.label === parentCodeLabel)

            if (matchedOption) {
              // 如果找到匹配的选项，使用其value值
              saveForm.value.researchersData[0].academicDegree = String(matchedOption.value)
              saveForm.value.projectOrgStructure.prAcademic = String(matchedOption.value)
            } else {
              // 如果没找到匹配项，使用原来的逻辑
              saveForm.value.researchersData[0].academicDegree = degreeOption.parentId
                ? String(degreeOption.parentId)
                : String(degreeOption.id)
              saveForm.value.projectOrgStructure.prAcademic = saveForm.value.researchersData[0].academicDegree
            }
          } else {
            // 如果没找到匹配项，尝试直接使用degreeId，确保是字符串类型
            saveForm.value.researchersData[0].academicDegree = String(degreeId)
            saveForm.value.projectOrgStructure.prAcademic = String(degreeId)
          }
        })
      })

      mainMemberRef.value?.reload()
    }
  }

  /**
   * 日期范围快捷选项
   */
  const rangeShortcuts = {
    // 项目起止时间
    projectDateRange: getRangeShortcuts({
      displayOrder: ['future_year'],
      text: {
        future_year: '未来一年',
      },
      custom: () => {
        return {
          未来两年: getFutureYear(2),
          未来三年: getFutureYear(3),
        }
      },
    }),
  }

  const calculateStages = (projectDateRange: [string, string]): ResearchIndex[] => {
    const datetimeFormat = 'yyyy-MM-dd'
    const startDate = new Date(projectDateRange[0])
    const endDate = new Date(projectDateRange[1])
    const stages: ResearchIndex[] = []
    let currentStartDate = startDate

    let serial = 1
    while (currentStartDate.getTime() < endDate.getTime()) {
      let currentEndDate = new Date(currentStartDate)
      currentEndDate.setMonth(currentStartDate.getMonth() + 6)
      currentEndDate.setDate(currentEndDate.getDate() - 1)
      if (currentEndDate.getTime() > endDate.getTime()) {
        stages.push({
          seq: serial,
          semiAnnualPeriod: serial,
          startTime: JPGlobal.formatDatetime(currentStartDate, datetimeFormat),
          endTime: JPGlobal.formatDatetime(endDate, datetimeFormat),
          researchContent: null,
          assessmentIndex: null,
        })
        break
      }
      stages.push({
        seq: serial,
        semiAnnualPeriod: serial,
        startTime: JPGlobal.formatDatetime(currentStartDate, datetimeFormat),
        endTime: JPGlobal.formatDatetime(currentEndDate, datetimeFormat),
        researchContent: null,
        assessmentIndex: null,
      })
      currentStartDate = new Date(currentEndDate)
      currentStartDate.setDate(currentEndDate.getDate() + 1)
      serial++
    }
    return stages
  }

  const updateProjectDateRange = (value: [string, string] | null, timestampValue: [number, number] | null) => {
    saveForm.value.projectDateRange = value
    saveForm.value.researchIndexData = calculateStages(value)
    researchIndexRef.value?.reload()
    if (value) {
      saveForm.value.projectStartDate = value[0]
      saveForm.value.projectEndDate = value[1]
    } else {
      saveForm.value.projectStartDate = null
      saveForm.value.projectEndDate = null
    }
  }

  const updateProjectBudget = (value: number | null) => {
    saveForm.value.projectInfo.budget = value
    saveForm.value.rmsProjectFundingIncomeDto.projTotal = value
  }

  // 封面页脚年月
  const showYearMonth = ref<string>(null)
  // 封面标题年份
  const showYear = ref<string>(null)

  /**
   * 按钮loading
   */
  const loading = ref<{
    // 下载申报书
    downloadDeclaration: boolean
    // 保存详情草稿
    saveDraft: boolean
    // 提交申报信息
    submitDeclaration: boolean
  }>({
    downloadDeclaration: false,
    saveDraft: false,
    submitDeclaration: false,
  })

  /**
   * 按钮禁用
   */
  const disabled = ref<{
    // 下载申报书
    downloadDeclaration: boolean
    // 保存详情草稿
    saveDraft: boolean
  }>({
    downloadDeclaration: false,
    saveDraft: false,
  })

  /**
   * 启用保存按钮
   */
  const enableSaveDraft = () => {
    disabled.value.saveDraft = false
  }

  /**
   * 禁用保存按钮
   */
  const disableSaveDraft = () => {
    disabled.value.saveDraft = true
  }

  // 表单信息
  const saveForm = ref<SaveForm>({
    id: null,
    projectName: null,
    projectLeader: null,
    projectLeaderUsername: null,
    telephone: null,
    projectLevel: null,
    topicCategory: null,
    researchType: null,
    externalCooperation: null,
    projectDateRange: null,
    projectStartDate: null,
    projectEndDate: null,
    intendedUnitId: null,
    intendedUnitOther: null,
    intendedUnitSpecialId: null,
    intendedUnitSpecialOther: null,
    promiseAttachments: [],
    projectInfo: {
      innovationType: null,
      industryAcademiaCollab: null,
      ipStatus: null,
      resultLevel: null,
      resultFormat: null,
      intellectualProperty: {
        inventPatentCnt: null,
        utilPatentCnt: null,
        otherPatentNum: null,
      },
      techStdDev: null,
      budget: null,
      projectDescr: null,
    },
    swotAttachments: [],
    performanceGoals: {},
    rmsProjectPerformanceGoalData: [],
    projectApplyType: null,
    rmsProjectPerformanceGoals: {
      resultForm: null,
      inventPatentCt: null,
      inventApplicationCt: null,
      utilityPatentCt: null,
      utilityApplicationCt: null,
      interStndaCt: null,
      ctyindastyStndaCt: null,
      companyStndaCt: null,
      newDrugCertifyCt: null,
      newVarietyCertifyCt: null,
      softwareCprightCt: null,
      ndClinicalPermitCt: null,
      tclsMedcDeviceRegistCt: null,
      tclsMedcDeviceRegistLic: null,
      publicationCt: null,
      citationCt: null,
      bookCt: null,
      otherTchInnovateTgs: null,
      promotionTgs: null,
      trainingSessionCt: null,
      techManagerCount: null,
      medcTchPromoterCt: null,
      keyTchCt: null,
      tchName: null,
      keyEquipmentCt: null,
      equipmentName: null,
      uniqueResourceCt: null,
      resourceName: null,
      academicianCt: null,
      speclAllowanceEptCt: null,
      ctryOutstandiTfdCt: null,
      ctryOutstandiTltCt: null,
      yangtzeScholarCt: null,
      newCenturyTltCt: null,
      provinOutstandEptCt: null,
      provinAcdmicLeaderCt: null,
      pvlAcademicLdrCandid: null,
      thousandTltPlanCt: null,
      tenThousandPlanCt: null,
      seniorTitle: null,
      intermediateTitle: null,
      phdStudentCt: null,
      postdocEntryCt: null,
      mastersStudentCt: null,
      postdocExitCt: null,
      profPhdStudentCt: null,
      mastersGradCt: null,
      bachelorsGradCt: null,
      postdocRecruitsCt: null,
      phdEmployedCt: null,
      mastersEmployedCt: null,
      bachelorsEmployedCt: null,
      assocDegEmployedCt: null,
      socialImpactGoals: null,
      interimReportsCt: null,
      annualReportsCt: null,
      finalReportCt: null,
    },
    researchIndexData: [],
    researchIndexSupply: {
      otherDescr: null,
      expectations: null,
    },
    equipmentRegData: [],
    rmsProjectFundingIncomeDto: {
      applyProjFunds: null,
      selfFundedFunds: null,
      projTotal: null,
    },
    fundsExpenseData: [],
    //项目支出经费树形数据
    fundsExpenseTreeData: [],

    projectOrgStructure: {
      acName: null,
      acCode: null,
      acAddr: null,
      acPostal: null,
      acType: null,
      acDept: null,
      acRespe: null,
      acRespeDept: null,
      acContact: null,
      acContactLandline: null,
      acContactPhone: null,
      empCount: null,
      orgNature: null,
      upperAdminDept: null,
      projectResp: null,
      prSex: null,
      prBirthday: null,
      prAcademic: null,
      prTitle: null,
      prPhone: null,
      prProfession: null,
      projectTeamCount: null,
      proSeniorCt: null,
      proMiddleCt: null,
      proJuniorCt: null,
      proOtherCt: null,
    },
    cooperativeUnitsData: [],
    researchersData: [],
    researchersSignAttachments: [],
    richText: {
      technicalProposal: null,
      researchBackground: null,
      projectInnovation: null,
      projectBenefits: null,
      conditionRisk: null,
      expectedObjectives: null,
    },
    applicationFiles: [],
    // 技术查新附件
    techQueryAttachment: [],
    // 伦理审批附件
    ethicsApprovalAttachment: [],
    // 申报书备案附件
    applicationDeclarationAttachment: [],
    // 承诺书附件
    promiseAttachment: []
  })

  /**
   * 表单校验规则
   */
  const saveFormRules: SaveFormRules = {
    projectName: [
      { required: true, message: '请输入项目名称', key: DeclarationValidateKey.DRAFT },
      { max: 50, message: '项目名称不能超过50个字符', key: DeclarationValidateKey.DRAFT },
    ],
    projectLeader: [
      { required: true, message: '请输入项目负责人' },
      { max: 50, message: '项目负责人不能超过50个字符' },
    ],
    projectLeaderUsername: [
      { required: true, message: '请选择项目负责人' },
      { max: 50, message: '项目负责人工号不能超过50个字符' },
    ],
    telephone: [
      { required: true, message: '请输入联系方式' },
      { pattern: RegExpType.MOBILE, message: '请输入正确的手机号', key: DeclarationValidateKey.DRAFT },
    ],
    projectLevel: [{ required: true, message: '请选择项目级别' }],
    topicCategory: [{ required: true, message: '请选择课题类别' }],
    researchType: [{ required: true, message: '请选择研究类型' }],
    externalCooperation: [{ required: true, message: '请选择是否外单位合作' }],
    projectDateRange: [{ required: true, message: '请选择项目时间', type: 'array' }],
    projectInfo: {
      innovationType: [{ required: true, message: '请选择创新类型' }],
      industryAcademiaCollab: [{ required: true, message: '请选择产学研联合' }],
      ipStatus: [{ required: true, message: '请选择知识产权状况' }],
      resultLevel: [{ required: true, message: '请选择成果水平' }],
      resultFormat: [{ required: true, message: '请选择成果形式', type: 'array' }],
      intellectualProperty: {
        inventPatentCnt: [{ required: true, message: '请填写发明专利专利项数', type: 'number' }],
        utilPatentCnt: [{ required: true, message: '请填写实用型新型专利项数', type: 'number' }],
        otherPatentNum: [{ required: true, message: '请填写其他专利项数', type: 'number' }],
      },
      techStdDev: [{ required: true, message: '请选择技术标准' }],
      budget: [
        { required: true, message: '请填写项目经费预算', type: 'number', key: DeclarationValidateKey.PREPARATION },
      ],
      projectDescr: [{ required: true, message: '请填写项目描述' }],
    },
    swotAttachments: [{ required: true, message: '请上传相关数据或成果佐证资料', type: 'array' }],
    rmsProjectPerformanceGoals: {
      resultForm: [{ required: true, message: '请选择成果形式', type: 'array' }],
      inventPatentCt: [{ required: true, message: '请填写发明专利授权项数', type: 'number' }],
      inventApplicationCt: [{ required: true, message: '请填写发明专利受理项数', type: 'number' }],
      utilityPatentCt: [{ required: true, message: '请填写实用新型专利授权项数', type: 'number' }],
      utilityApplicationCt: [{ required: true, message: '请填写实用新型专利受理项数', type: 'number' }],
      interStndaCt: [{ required: true, message: '请填写国际标准项数', type: 'number' }],
      ctyindastyStndaCt: [{ required: true, message: '请填写国家、行业标准项数', type: 'number' }],
      companyStndaCt: [{ required: true, message: '请填写地方、企业标准项数', type: 'number' }],
      newDrugCertifyCt: [{ required: true, message: '请填写新药证书项数', type: 'number' }],
      newVarietyCertifyCt: [{ required: true, message: '请填写新品种审定证书项数', type: 'number' }],
      softwareCprightCt: [{ required: true, message: '请填写计算机软件著作权登记证书项数', type: 'number' }],
      ndClinicalPermitCt: [{ required: true, message: '请填写新药临床批件项数', type: 'number' }],
      tclsMedcDeviceRegistCt: [{ required: true, message: '请填写三类医疗器械注册受理证明项数', type: 'number' }],
      tclsMedcDeviceRegistLic: [{ required: true, message: '请填写三类医疗器械临床试验许可项数', type: 'number' }],
      publicationCt: [{ required: true, message: '请填写公开发表篇数', type: 'number' }],
      citationCt: [{ required: true, message: '请填写引用次数', type: 'number' }],
      bookCt: [{ required: true, message: '请填写出版专著部数', type: 'number' }],
      otherTchInnovateTgs: [{ required: true, message: '请填写其他科技创新技术项数', type: 'number' }],
      promotionTgs: [{ required: true, message: '请填写推广应用目标', type: 'number' }],
      trainingSessionCt: [{ required: true, message: '请填写培训班举办期数', type: 'number' }],
      techManagerCount: [{ required: true, message: '请填写培训科技管理人员人数', type: 'number' }],
      medcTchPromoterCt: [{ required: true, message: '请填写培训医疗技术、推广人员人数', type: 'number' }],
      keyTchCt: [{ required: true, message: '请填写引进关键技术项数', type: 'number' }],
      tchName: [{ required: true, message: '请填写技术名称' }],
      keyEquipmentCt: [{ required: true, message: '请填写引进关键设备台数', type: 'number' }],
      equipmentName: [{ required: true, message: '请填写设备名称' }],
      uniqueResourceCt: [{ required: true, message: '请填写引进特有资源种数', type: 'number' }],
      resourceName: [{ required: true, message: '请填写资源名称' }],
      academicianCt: [{ required: true, message: '请填写院士人数', type: 'number' }],
      speclAllowanceEptCt: [{ required: true, message: '请填写享受国务院政府特殊津贴专家人数', type: 'number' }],
      ctryOutstandiTfdCt: [{ required: true, message: '请填写国家杰出青年科学基金人数', type: 'number' }],
      ctryOutstandiTltCt: [{ required: true, message: '请填写全国杰出专业技术人才人数', type: 'number' }],
      yangtzeScholarCt: [{ required: true, message: '请填写长江学者人数', type: 'number' }],
      newCenturyTltCt: [{ required: true, message: '请填写新世纪人才人数', type: 'number' }],
      provinOutstandEptCt: [{ required: true, message: '请填写省有突出贡献的优秀专家人数', type: 'number' }],
      provinAcdmicLeaderCt: [{ required: true, message: '请填写省学术和技术带头人人数', type: 'number' }],
      pvlAcademicLdrCandid: [{ required: true, message: '请填写省学术和技术带头人后备人选数', type: 'number' }],
      thousandTltPlanCt: [{ required: true, message: '请填写千人计划人数', type: 'number' }],
      tenThousandPlanCt: [{ required: true, message: '请填写万人计划人数', type: 'number' }],
      seniorTitle: [{ required: true, message: '请填写高级职称人数', type: 'number' }],
      intermediateTitle: [{ required: true, message: '请填写中级职称人数', type: 'number' }],
      phdStudentCt: [{ required: true, message: '请填写博士后进站人数', type: 'number' }],
      postdocEntryCt: [{ required: true, message: '请填写在读博士研究生人数', type: 'number' }],
      mastersStudentCt: [{ required: true, message: '请填写在读硕士研究生人数', type: 'number' }],
      postdocExitCt: [{ required: true, message: '请填写博士后出站人数', type: 'number' }],
      profPhdStudentCt: [{ required: true, message: '请填写毕业博士研究生人数', type: 'number' }],
      mastersGradCt: [{ required: true, message: '请填写毕业硕士研究生人数', type: 'number' }],
      bachelorsGradCt: [{ required: true, message: '请填写毕业学士人数', type: 'number' }],
      postdocRecruitsCt: [{ required: true, message: '请填写博士后人数', type: 'number' }],
      phdEmployedCt: [{ required: true, message: '请填写博士研究生人数', type: 'number' }],
      mastersEmployedCt: [{ required: true, message: '请填写硕士研究生人数', type: 'number' }],
      bachelorsEmployedCt: [{ required: true, message: '请填写本科生人数', type: 'number' }],
      assocDegEmployedCt: [{ required: true, message: '请填写专科生人数', type: 'number' }],
      socialImpactGoals: [
        {
          required: true,
          message: '请填写技术及产品应用形成的公益性贡献和价值',
          key: DeclarationValidateKey.PREPARATION,
        },
      ],
      interimReportsCt: [{ required: true, message: '请填写中期报告篇数', type: 'number' }],
      annualReportsCt: [{ required: true, message: '请填写年度报告篇数', type: 'number' }],
      finalReportCt: [{ required: true, message: '请填写最终报告篇数', type: 'number' }],
    },
    researchIndexData: {
      // 需要在saveForm.researchIndexData数组中添加timeRange字段绑定才可进行校验
      timeRange: [
        { required: true, message: '请选择起止时间', type: 'array', key: DeclarationValidateKey.PREPARATION },
      ],
      startTime: [{ required: true, message: '请选择开始时间', key: DeclarationValidateKey.PREPARATION }],
      endTime: [{ required: true, message: '请选择终止时间', key: DeclarationValidateKey.PREPARATION }],
      researchContent: [{ required: true, message: '请填写研究内容', key: DeclarationValidateKey.PREPARATION }],
      assessmentIndex: [{ required: true, message: '请填写考核指标', key: DeclarationValidateKey.PREPARATION }],
    },
    researchIndexSupply: {
      otherDescr: [{ required: true, message: '请填写其他说明' }],
      expectations: [{ required: true, message: '请填写预期目标' }],
    },
    equipmentRegData: {
      equName: [{ required: true, message: '请填写设备名称' }],
      equModel: [{ required: true, message: '请填写设备型号' }],
      fyInstrUsageCnt: [{ required: true, message: '请填写年使用次数', type: 'number' }],
      secInstrUsageCnt: [{ required: true, message: '请填写月使用次数', type: 'number' }],
    },
    rmsProjectFundingIncomeDto: {
      applyProjFunds: [{ required: true, message: '请填写申请经费', type: 'number' }],
      selfFundedFunds: [{ required: true, message: '请填写自筹经费', type: 'number' }],
      projTotal: [{ required: true, message: '请填写项目总经费', type: 'number' }],
    },
    fundsExpenseTreeData: {
      projectSpecialFunding: [
        { required: true, message: '请填写项目专项经费', type: 'number' },
        {
          validator(rule: FormItemRule, value: number, other: any) {
            console.log('rule ==> ', rule)
            console.log('value ==> ', value)
            console.log('other ==> ', other)
            return new Error('error')
          },
          trigger: 'blur',
        },
      ],
    },
    projectOrgStructure: {
      acName: [{ required: true, message: '请填写项目申报单位名称' }],
      acCode: [{ required: true, message: '请填写项目申报组织机构代码' }],
      acAddr: [{ required: true, message: '请填写项目申报单位地址' }],
      acPostal: [
        { required: true, message: '请填写项目申报单位邮编' },
        { pattern: RegExpType.ZIP_CODE, message: '请填写正确的邮政编码', key: DeclarationValidateKey.DRAFT },
      ],
      acType: [{ required: true, message: '请选择项目申报单位类别' }],
      acDept: [{ required: true, message: '请填写项目申报单位主管部门' }],
      acRespe: [{ required: true, message: '请填写负责人' }],
      acRespeDept: [{ required: true, message: '请填写联系部门' }],
      acContact: [{ required: true, message: '请填写联系人' }],
      acContactLandline: [
        { required: true, message: '请填写联系人座机' },
        {
          pattern: RegExpType.LANDLINE,
          message: '请填写正确的座机号码(需包含区号)',
          key: DeclarationValidateKey.DRAFT,
        },
      ],
      acContactPhone: [
        { required: true, message: '请填写联系人手机' },
        { pattern: RegExpType.MOBILE, message: '请填写正确的手机号码', key: DeclarationValidateKey.DRAFT },
      ],
      empCount: [{ required: true, message: '请填写职工人数', type: 'number' }],
      orgNature: [{ required: true, message: '请填写单位性质' }],
      upperAdminDept: [{ required: true, message: '请填写上级行政主管部门' }],
      projectResp: [{ required: true, message: '请填写项目负责人姓名' }],
      prSex: [{ required: true, message: '请选择性别' }],
      prBirthday: [{ required: true, message: '请选择出生年月' }],
      prAcademic: [{ required: true, message: '请选择学历（学位）' }],
      prTitle: [{ required: true, message: '请选择职称' }],
      prPhone: [
        { required: true, message: '请填写手机号' },
        { pattern: RegExpType.MOBILE, message: '请填写正确的手机号码', key: DeclarationValidateKey.DRAFT },
      ],
      prProfession: [{ required: true, message: '请填写从事专业' }],
      projectTeamCount: [{ required: true, message: '请填写项目人数' }],
      proSeniorCt: [{ required: true, message: '请填写高级人员人数', type: 'number' }],
      proMiddleCt: [{ required: true, message: '请填写中级人员人数', type: 'number' }],
      proJuniorCt: [{ required: true, message: '请填写初级人员人数', type: 'number' }],
      proOtherCt: [{ required: true, message: '请填写其他人员人数', type: 'number' }],
    },
    cooperativeUnitsData: {
      unitName: [{ required: true, message: '请填写名称', key: DeclarationValidateKey.PREPARATION }],
      divide: [{ required: true, message: '请填写在本项目中的分工' }],
      contactPerson: [{ required: false, message: '请填写联系人' }],
      contactPhone: [{ required: false, message: '请填写联系方式' }],
      attachments: [{ required: true, message: '请上传附件', type: 'array', key: DeclarationValidateKey.PREPARATION }],
    },
    researchersData: {
      internal: [{ required: true, message: '请选择是否为内部人员', type: 'boolean' }],
      username: [{ required: true, message: '请选择成员' }],
      name: [{ required: true, message: '请填写姓名' }],
      sex: [{ required: true, message: '请选择性别' }],
      birthday: [{ required: true, message: '请选择出生年月' }],
      workUnit: [{ required: true, message: '请填写工作单位' }],
      idCard: [
        { required: true, message: '请填写身份证号' },
        {
          pattern: RegExpType.CITIZEN_ID,
          message: '请输入正确的身份证号码',
          trigger: ['blur', 'change'],
          key: DeclarationValidateKey.DRAFT,
        },
      ],
      academicDegree: [{ required: true, message: '请选择学历' }],
      professionalTitle: [{ required: true, message: '请选择职称' }],
      currentProfessional: [{ required: true, message: '请填写现从事专业' }],
      division: [{ required: true, message: '请填写课题中的分工' }],
      studyTime: [{ required: true, message: '请选择研究时间' }],
      signature: [{ required: true, message: '请填写签名' }],
    },
    researchersSignAttachments: [
      { required: true, message: '请上传课题组成员签字附件', type: 'array', key: DeclarationValidateKey.PREPARATION },
    ],
    promiseAttachments: [
      { required: true, message: '请上传项目诚信承诺书', type: 'array', key: DeclarationValidateKey.BRIEF },
    ],
    richText: {
      technicalProposal: [{ required: true, message: '请填写项目现有技术指标（产品参数）、经济指标' }],
      researchBackground: [{ required: true, message: '请填写立项研究' }],
      projectInnovation: [{ required: true, message: '请填写创新性分析' }],
      projectBenefits: [{ required: true, message: '请填写项目前景' }],
      conditionRisk: [{ required: true, message: '请填写SWOT分析' }],
      expectedObjectives: [{ required: true, message: '请填写目标规划与期望' }],
    },
    applicationFiles: [{ required: true, message: '请上传项目申报书文件', type: 'array' }],
    applicationDeclarationAttachment: [{ required: true, message: '请上传申报书备案附件', type: 'array' }],
  }

  /**
   * 上传配置
   */
  interface UploadConfig {
    // 受控文件列表
    fileList?: UploadFileInfo[]
    // 文件列表的内建样式
    listType?: 'text' | 'image' | 'image-card'
    // 自定义文件缩略图，如果返回了 <code>undefined</code>，会使用默认的缩略图展示逻辑
    createThumbnailUrl?: (file: File | null, fileInfo: UploadSettledFileInfo) => Promise<string> | string | undefined
    // 限制上传文件数量
    max?: number
    // 接受的文件类型
    accept?: string
    // 是否支持多个文件
    multiple?: boolean
    // 自定义上传方法
    customRequest?: (options: UploadCustomRequestOptions) => void
    // 是否禁用
    disabled?: boolean
    // 选择文件时候是否默认上传
    defaultUpload?: boolean
    // 组件状态变化的回调，组件的任何文件状态变化都会触发回调
    change?: (options: { file: UploadFileInfo; fileList: Array<UploadFileInfo>; event?: Event }) => void
    // 是否显示下载按钮（在 finished 后展示）
    showDownloadButton?: boolean
    /**
     * 点击文件下载按钮的回调函数，返回 <code>false</code>、<code>Promise resolve false</code>、<code>Promise rejected</code> 时会取消本次下载
     */
    download?: (file: UploadFileInfo) => Promise<boolean> | boolean | any
    /**
     * 文件删除回调，返回 <code>false</code>、<code>Promise resolve false</code>、<code>Promise rejected</code> 时会取消本次删除
     */
    remove?: (options: {
      file: UploadFileInfo
      fileList: Array<UploadFileInfo>
      index: number
    }) => Promise<boolean> | boolean | any
    /**
     * 点击重试的回调函数，返回 <code>false</code>、<code>Promise resolve false</code>、<code>Promise rejected</code> 时会取消本次重试
     */
    retry?: (options: { file: UploadFileInfo }) => Promise<boolean | void> | boolean | void
    // 上传框中的提示文字
    tips?: ComputedRef<string>
    // 是否禁用确认上传按钮
    disabledConfirm?: ComputedRef<boolean>
    // 提交上传文件
    submit?: () => void
  }

  /**
   * 获取文件外链
   * @param objName 对象名
   * @param bucket 桶名
   */
  const getFileUrl: (objName: string, bucket?: string) => Promise<string> = (
    objName: string,
    bucket: string = 'rms'
  ) => {
    return new Promise(async (resolve, reject) => {
      const { code, data } = await getSourceFileUrlOutsideChain({
        bucket: bucket,
        path: objName,
      })
      if (code !== 200 || data === '-1') {
        return reject('Failed to get file URL.')
      }
      return resolve(data)
    })
  }

  const createThumbnailUrl: (
    file: File | null,
    fileInfo: UploadSettledFileInfo
  ) => Promise<string> | string | undefined = async (file: File | null, fileInfo: UploadSettledFileInfo) => {
    try {
      if (file) {
        const fileReader = new FileReader()
        fileReader.readAsDataURL(file!)
        return await new Promise(resolve => {
          // @ts-ignore
          fileReader.onloadend = () => resolve(fileReader.result)
        })
      } else {
        const parts = fileInfo.fullPath.split('/')
        return await getFileUrl(parts.slice(1).join('/'), parts[0])
      }
    } catch (e) {
      return undefined
    }
  }

  const swotAttachmentsRef = ref<UploadInst | null>(null)

  /**
   * swot分析 上传附件配置
   */
  const swotAttachmentConfig = ref<UploadConfig>({
    fileList: saveForm.value.swotAttachments || [],
    listType: 'image',
    createThumbnailUrl,
    max: 5,
    accept: '.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.zip,.rar,.7z,image/*',
    multiple: true,
    customRequest: async ({ file, onFinish, onError, onProgress }: UploadCustomRequestOptions) => {
      // 上传开始时显示保存按钮加载动画以防止用户因为网络原因在未上传完成之前点击保存
      loading.value.saveDraft = true
      try {
        const { data } = await uploadRmsFile(file.file, onProgress)
        loading.value.saveDraft = false
        window.$message.success('上传成功')
        onFinish()
        const fullPath = data.bucket + '/' + data.path
        // 修改fullPath为mino地址，便于预览
        swotAttachmentConfig.value.fileList.forEach(item => {
          if (item.id === file.id) {
            item.fullPath = fullPath
          }
        })
        saveForm.value.swotAttachments?.push({
          id: file.id,
          name: file.name,
          size: file.file.size,
          type: file.file.type,
          fullPath,
          status: 'finished',
        })
      } catch (e) {
        loading.value.saveDraft = false
        onError()
      }
    },
    disabled: props.readonly,
    defaultUpload: false,
    change: (options: { file: UploadFileInfo; fileList: Array<UploadFileInfo>; event?: Event }) => {
      swotAttachmentConfig.value.fileList = options.fileList
    },
    showDownloadButton: true,
    download: async file => {
      const parts = file.fullPath.split('/')
      handleShowPreview(parts[0], parts.slice(1).join('/'))
      // 阻止默认的下载行为
      return false
    },
    remove: async (options: { file: UploadFileInfo; fileList: Array<UploadFileInfo>; index: number }) => {
      console.log(options)
      saveForm.value.swotAttachments = saveForm.value.swotAttachments.filter(item => item.id !== options.file.id)
      swotAttachmentConfig.value.fileList?.splice(options.index, 1)
      // 阻止默认的移除行为
      return false
    },
    tips: computed(() => {
      if (swotAttachmentConfig.value.accept) {
        return `支持上传 ${swotAttachmentConfig.value.accept} 等格式文件，最多上传 ${
          swotAttachmentConfig.value.max || 1
        } 个文件`
      } else {
        return `支持上传任意格式文件，最多上传 ${swotAttachmentConfig.value.max || 1} 个文件`
      }
    }),
    disabledConfirm: computed(() => {
      return (
        props.readonly ||
        swotAttachmentConfig.value.disabled ||
        swotAttachmentConfig.value.fileList.length === 0 ||
        swotAttachmentConfig.value.fileList.every((item: UploadFileInfo) => item.status !== 'pending')
      )
    }),
    submit: () => {
      swotAttachmentsRef.value?.submit()
    },
  })

  const researchersSignAttachmentsRef = ref<UploadInst | null>(null)

  /**
   * 课题组成员签字附件 配置
   */
  const researchersSignAttachmentsConfig = ref<UploadConfig>({
    fileList: saveForm.value.researchersSignAttachments || [],
    listType: 'image',
    createThumbnailUrl,
    multiple: false,
    max: 1,
    accept: '.pdf,image/*',
    change: async (options: { file: UploadFileInfo; fileList: Array<UploadFileInfo>; event?: Event }) => {
      researchersSignAttachmentsConfig.value.fileList = options.fileList
    },
    customRequest: async ({ file, onFinish, onError, onProgress }: UploadCustomRequestOptions) => {
      loading.value.saveDraft = true
      try {
        const { data } = await uploadRmsFile(file.file, onProgress)
        loading.value.saveDraft = false
        window.$message.success('上传成功')
        onFinish()
        const fullPath = data.bucket + '/' + data.path
        researchersSignAttachmentsConfig.value.fileList.forEach(item => {
          if (item.id === file.id) {
            item.fullPath = fullPath
          }
          return item
        })
        saveForm.value.researchersSignAttachments?.push({
          id: file.id,
          name: file.name,
          size: file.file.size,
          type: file.file.type,
          fullPath,
          status: 'finished',
        })
      } catch (e) {
        loading.value.saveDraft = false
        onError()
      }
    },
    disabled: props.readonly,
    remove: async (options: { file: UploadFileInfo; fileList: Array<UploadFileInfo>; index: number }) => {
      saveForm.value.researchersSignAttachments = saveForm.value.researchersSignAttachments.filter(
        item => item.id !== options.file.id
      )
      researchersSignAttachmentsConfig.value.fileList?.splice(options.index, 1)
      return false
    },
    showDownloadButton: true,
    download: async file => {
      const parts = file.fullPath.split('/')
      handleShowPreview(parts[0], parts.slice(1).join('/'))
      return false
    },
    tips: computed(() => {
      if (researchersSignAttachmentsConfig.value.accept) {
        return `支持上传 ${researchersSignAttachmentsConfig.value.accept} 等格式文件，最多上传 ${
          researchersSignAttachmentsConfig.value.max || 1
        } 个文件`
      } else {
        return `支持上传任意格式文件，最多上传 ${researchersSignAttachmentsConfig.value.max || 1} 个文件`
      }
    }),
    disabledConfirm: computed(() => {
      return (
        props.readonly ||
        researchersSignAttachmentsConfig.value.disabled ||
        researchersSignAttachmentsConfig.value.fileList.length === 0 ||
        researchersSignAttachmentsConfig.value.fileList.every((item: UploadFileInfo) => item.status !== 'pending')
      )
    }),
    submit: () => {
      researchersSignAttachmentsRef.value?.submit()
    },
  })

  const preview = ref<{
    /*是否显示*/
    show: boolean
    /*桶名称*/
    bucket: string | null
    /*oss路径*/
    ossPath: string | null
  }>({
    show: false,
    bucket: null,
    ossPath: null,
  })

  /**
   * 处理显示预览框
   * @param bucket 存储桶
   * @param path minio路径
   */
  const handleShowPreview = (bucket: string, path: string) => {
    preview.value.show = true
    preview.value.bucket = bucket
    preview.value.ossPath = path
  }

  /**
   * 预览项目申报书
   */
  const downloadDeclaration = async () => {
    loading.value.downloadDeclaration = true
    try {
      const { code, data, message } = await generateDeclaration(saveForm.value)
      loading.value.downloadDeclaration = false
      if (code === 200) {
        handleShowPreview('temp', data)
      } else {
        window.$message.error(message)
      }
    } catch (e) {
      loading.value.downloadDeclaration = false
    }
  }

  const fundsExpenseFlattenData = computed(() => {
    return JPGlobal.flattenTree(saveForm.value.fundsExpenseTreeData)
  })

  /**
   * 需要展开的行ID
   */
  const expandedRowKeys = computed<number[]>(() => {
    return fundsExpenseFlattenData.value
      .filter(item => item.children)
      .map(item => {
        return item.fundingId
      })
  })

  // 树形表格行key
  const fundsExpenseRowKey = (row: ExpenditureTreeNode) => row.fundingId

  const buildFundsExpenseList = (fundsExpenseTreeData: Array<ExpenditureTreeNode>) => {
    fundsExpenseTreeData.forEach(item => {
      if (item.projectSpecialFunding || item.selfFunded || item.subjectTotalFunding) {
        saveForm.value.fundsExpenseData.push({
          fundingId: item.fundingId,
          projectSpecialFunding: item.projectSpecialFunding,
          selfFunded: item.selfFunded,
          subjectTotalFunding: item.subjectTotalFunding,
        })
      }
      if (item.children) {
        buildFundsExpenseList(item.children)
      }
    })
  }

  const saveFormRef = ref<FormInst | null>(null)

  /**
   * 表单校验
   * @param keys 校验的key
   */
  const validateForm = (keys: DeclarationValidateKey[]) => {
    return new Promise(resolve => {
      saveFormRef.value?.validate(
        async (
          errors: Array<FormValidationError> | undefined,
          extra: { warnings: Array<FormValidationError> | undefined }
        ) => {
          if (errors) {
            window.$message.error(errors[0][0].message)
            resolve(false)
          } else {
            resolve(true)
          }
        },
        (rule: FormItemRule) => {
          return keys.includes(rule?.key as DeclarationValidateKey)
        }
      )
    })
  }

  const prepare = async () => {
    // 原数据置为空后属性数据列表重新赋值
    saveForm.value.fundsExpenseData = []
    buildFundsExpenseList(saveForm.value.fundsExpenseTreeData)

    // 设置项目申报类型
    if (props.operationType === ProjectOperationType.APPLY_2023) {
      saveForm.value.projectApplyType = '1'
    } else if (!saveForm.value.projectApplyType) {
      saveForm.value.projectApplyType = '0'
    }

    // 进行表单校验（仅对key为draft的数据进行校验）
    const keys: DeclarationValidateKey[] = [DeclarationValidateKey.DRAFT]
    if (props.operationType === ProjectOperationType.FILL_BRIEF) {
      keys.push(DeclarationValidateKey.BRIEF)
    }
    // 23年项目申报时，验证项目申报书文件
    if (props.operationType === ProjectOperationType.APPLY_2023) {
      keys.push(DeclarationValidateKey.BRIEF)
      // 验证项目申报书文件是否上传
      if (!saveForm.value.applicationFiles || saveForm.value.applicationFiles.length === 0) {
        window.$message.error('请上传项目申报书文件')
        loading.value.saveDraft = false
        return false
      }
    }
    const valid = await validateForm(keys)
    if (!valid) {
      loading.value.saveDraft = false
      return false
    }
    // 将新版绩效目标数据写入到旧版绩效目标中，申报书Word需要使用该表数据
    saveForm.value.rmsProjectPerformanceGoalData.forEach((item: RmsProjectPerformanceGoalVO) => {
      if (item.performanceIndicator.dataType && (item.indicatorValue || item.indicatorValue === 0)) {
        saveForm.value.rmsProjectPerformanceGoals[item.performanceIndicator.fieldName] = item.indicatorValue
      }
    })
    return true
  }

  //保存草稿
  const saveDraft = async () => {
    loading.value.saveDraft = true
    const ps = await prepare()
    if (!ps) {
      loading.value.saveDraft = false
      return false
    }
    try {
      const { data } = await projectInfoSupply(saveForm.value)
      loading.value.saveDraft = false
      window.$message.success('保存成功')
      // 若saveForm.value.id为空则为新建页的首次保存操作：数据库执行插入操作，此时需要回填绩效目标部分的ID
      if (!saveForm.value.id) {
        const { data: performanceGoals } = await queryRmsProjectPerformanceGoalsByProjectId({ projectId: data })
        saveForm.value.rmsProjectPerformanceGoalData = performanceGoals
        transPerformanceGoals(performanceGoals)
      }
      // 回填项目ID 用于在新建操作后继续保存提交
      saveForm.value.id = data
      // 更新项目原始数据
      originalData.value = JPGlobal.deepCopy(saveForm.value)
      return true
    } catch (e) {
      loading.value.saveDraft = false
      return false
    }
  }

  /**
   * TODO:提交申报书，暂时没有需求
   */
  const submitDeclaration = () => {
    loading.value.submitDeclaration = true
    saveFormRef.value?.validate(async errors => {
      if (!errors) {
        // 提交信息
        // const { code } = await submitPrjDeclaration(saveForm.value)
        loading.value.submitDeclaration = false
        window.$message.info('提交申报...')
      } else {
        loading.value.saveDraft = false
      }
    })
  }

  const handleSubmitBrief = async () => {
    const ps = await prepare()
    console.log(ps)
    if (!ps) return false
    try {
      await submitBrief(saveForm.value)
      window.$message.success('提交成功')
      return true
    } catch (e) {
      return false
    }
  }

  const operationButtonRef = ref(null)

  //tp1 侧边导航
  const anchorRef = ref<AnchorInst | null>(null)

  const anchorTop = ref(0)

  nextTick(() => {
    anchorTop.value = operationButtonRef.value?.$el.offsetHeight + operationButtonRef.value?.top + 40
  })

  const anchorScrollTo = (href: string) => {
    anchorRef.value?.scrollTo(href)
  }

  const anchorList = computed(() => {
    const performanceGoals: AnchorItem[] = performanceGoalsTree.value.map((goal, index) => ({
      title: index + 1 + '. ' + goal.performanceIndicator.label,
      href: '#d06' + (index + 1).toString().padStart(2, '0'),
      className: goal.indicatorValue ? 'filled-item' : 'unfilled-item',
    }))

    // 检查各部分是否已填写数据
    const hasProjectInfo = saveForm.value.projectName || saveForm.value.projectLeader || saveForm.value.telephone
    const hasTechnicalProposal = saveForm.value.richText?.technicalProposal
    const hasResearchBackground = saveForm.value.richText?.researchBackground
    const hasProjectInnovation = saveForm.value.richText?.projectInnovation
    const hasProjectBenefits = saveForm.value.richText?.projectBenefits
    const hasConditionRisk = saveForm.value.richText?.conditionRisk
    const hasSwotAttachments = saveForm.value.swotAttachments?.length > 0
    const hasResearchIndex = saveForm.value.researchIndexData?.some(
      item => item.researchContent || item.assessmentIndex
    )
    const hasExpectations = saveForm.value.researchIndexSupply?.expectations
    const hasEquipmentReg = saveForm.value.equipmentRegData?.length > 0
    const hasFundingSource =
      saveForm.value.rmsProjectFundingIncomeDto?.applyProjFunds ||
      saveForm.value.rmsProjectFundingIncomeDto?.selfFundedFunds
    const hasFundsExpense = saveForm.value.fundsExpenseData?.length > 0
    const hasOrgStructure = saveForm.value.projectOrgStructure?.acName || saveForm.value.projectOrgStructure?.acCode
    const hasCooperativeUnits = saveForm.value.cooperativeUnitsData?.length > 0
    const hasResearchers = saveForm.value.researchersData?.length > 0
    const hasResearchersSign = saveForm.value.researchersSignAttachments?.length > 0
    const hasApplicationFiles = saveForm.value.applicationFiles?.length > 0

    // 定义必填项
    const requiredItems = {
      projectInfo: true,
      technicalProposal: props.operationType !== ProjectOperationType.APPLY_2023 && !props.isProject2023,
      researchBackground: props.operationType !== ProjectOperationType.APPLY_2023 && !props.isProject2023,
      projectInnovation: props.operationType !== ProjectOperationType.APPLY_2023 && !props.isProject2023,
      projectBenefits: props.operationType !== ProjectOperationType.APPLY_2023 && !props.isProject2023,
      conditionRisk: props.operationType !== ProjectOperationType.APPLY_2023 && !props.isProject2023,
      swotAttachments: props.operationType !== ProjectOperationType.APPLY_2023 && !props.isProject2023,
      researchIndex: props.operationType !== ProjectOperationType.APPLY_2023 && !props.isProject2023,
      expectations: props.operationType !== ProjectOperationType.APPLY_2023 && !props.isProject2023,
      equipmentReg: true,
      fundingSource: true,
      fundsExpense: true,
      orgStructure: true,
      researchers: true,
      applicationFiles: props.operationType === ProjectOperationType.APPLY_2023 || props.isProject2023,
    }

    // 获取项目状态类名
    const getStatusClassName = (hasData: any, isRequired: any): string => {
      if (hasData) return 'filled-item'
      return isRequired ? 'required-item' : 'unfilled-item'
    }

    // 基础导航项，始终显示
    const list: AnchorItem[] = [
      {
        title: '封面',
        href: '#d01',
        className: getStatusClassName(hasProjectInfo, requiredItems.projectInfo),
      },
      {
        title: '项目信息',
        href: '#d02',
        className: getStatusClassName(saveForm.value.projectInfo?.innovationType, requiredItems.projectInfo),
      },
    ]

    // 在非23年项目申报模式下显示的导航项
    if (props.operationType !== ProjectOperationType.APPLY_2023 && !props.isProject2023) {
      list.push(
        {
          title: '一、项目现有技术指标（产品参数）、经济指标；项目研究主要目标、研究内容、技术关键、技术路线和应用方案',
          href: '#d04',
          className: getStatusClassName(hasTechnicalProposal, requiredItems.technicalProposal),
        },
        {
          title: '二、立项的必要性及国内外研究现状、发展趋势和知识产权状况分析',
          href: '#d05',
          className: hasResearchBackground ? 'filled-item' : '',
        },
        {
          title: '三、项目绩效目标',
          href: '#d06',
          children: performanceGoals,
          className: performanceGoals.some(goal => goal.className === 'filled-item') ? 'filled-item' : '',
        },
        {
          title: '四、项目的创新性',
          href: '#d07',
          className: hasProjectInnovation ? 'filled-item' : '',
        },
        {
          title: '五、项目应用前景和预期经济、社会效益',
          href: '#d08',
          className: hasProjectBenefits ? 'filled-item' : '',
        },
        {
          title: '六、已有研究基础、承担优势和项目实施的风险及应对策略',
          href: '#d09',
          children: [
            {
              title: '已有研究基础、承担优势和项目实施的风险及应对策略',
              href: '#d0901',
              className: hasConditionRisk ? 'filled-item' : '',
            },
            {
              title: '相关数据或成果佐证资料上传',
              href: '#d0902',
              className: hasSwotAttachments ? 'filled-item' : '',
            },
          ],
          className: hasConditionRisk || hasSwotAttachments ? 'filled-item' : '',
        },
        {
          title: '七、项目进度及预期目标',
          href: '#d11',
          children: [
            {
              title: '分年度研究内容和考核指标',
              href: '#d1101',
              className: hasResearchIndex ? 'filled-item' : '',
            },
            {
              title: '预期目标',
              href: '#d1102',
              className: hasExpectations ? 'filled-item' : '',
            },
          ],
          className: hasResearchIndex || hasExpectations ? 'filled-item' : '',
        },
        {
          title: '八、实验研究仪器/设备登记',
          href: '#d13',
          className: hasEquipmentReg ? 'filled-item' : '',
        },
        {
          title: '九、经费预算',
          href: '#d14',
          children: [
            {
              title: '经费来源',
              href: '#d1401',
              className: hasFundingSource ? 'filled-item' : '',
            },
            {
              title: '经费支出',
              href: '#d1402',
              className: hasFundsExpense ? 'filled-item' : '',
            },
          ],
          className: hasFundingSource || hasFundsExpense ? 'filled-item' : '',
        },
        {
          title: '十、项目申报单位、合作单位及主要研究人员情况',
          href: '#d15',
          children: [
            {
              title: '申报单位',
              href: '#d1501',
              className: hasOrgStructure ? 'filled-item' : '',
            },
            {
              title: '合作单位',
              href: '#d1502',
              className: hasCooperativeUnits ? 'filled-item' : '',
            },
            {
              title: '项目负责人',
              href: '#d1503',
              className: hasProjectInfo ? 'filled-item' : '',
            },
            {
              title: '项目人数',
              href: '#d1504',
              className: hasOrgStructure ? 'filled-item' : '',
            },
          ],
          className: hasOrgStructure || hasCooperativeUnits ? 'filled-item' : '',
        }
      )
    } else {
      // 在23年项目申报模式下，使用不同的序号
      list.push(
        {
          title: '一、实验研究仪器/设备登记',
          href: '#d13',
          className: hasEquipmentReg ? 'filled-item' : '',
        },
        {
          title: '二、经费预算',
          href: '#d14',
          children: [
            {
              title: '经费来源',
              href: '#d1401',
              className: hasFundingSource ? 'filled-item' : '',
            },
            {
              title: '经费支出',
              href: '#d1402',
              className: hasFundsExpense ? 'filled-item' : '',
            },
          ],
          className: hasFundingSource || hasFundsExpense ? 'filled-item' : '',
        },
        {
          title: '三、项目申报单位、合作单位及主要研究人员情况',
          href: '#d15',
          children: [
            {
              title: '申报单位',
              href: '#d1501',
              className: hasOrgStructure ? 'filled-item' : '',
            },
            {
              title: '合作单位',
              href: '#d1502',
              className: hasCooperativeUnits ? 'filled-item' : '',
            },
            {
              title: '项目负责人',
              href: '#d1503',
              className: hasProjectInfo ? 'filled-item' : '',
            },
            {
              title: '项目人数',
              href: '#d1504',
              className: hasOrgStructure ? 'filled-item' : '',
            },
          ],
          className: hasOrgStructure || hasCooperativeUnits ? 'filled-item' : '',
        }
      )
    }

    // 以下导航项在所有模式下都显示，但不包含序号
    list.push({
      title: '课题组主要人员情况',
      href: '#d16',
      children: [
        {
          title: '课题组主要成员',
          href: '#d1601',
          className: hasResearchers ? 'filled-item' : '',
        },
        {
          title: '课题组成员签字附件',
          href: '#d1602',
          className: hasResearchersSign ? 'filled-item' : '',
        },
      ],
      className: hasResearchers || hasResearchersSign ? 'filled-item' : '',
    })

    // 技术查新导航栏
    if(props.techQuery) {
      list.push({
        title: '技术查新',
        href: '#d18',
        className: 'filled-item',
      })
    }

    if(props.ethicsApproval) {
      list.push({
        title: '伦理审批',
        href: '#d19',
        className: 'filled-item',
      })
    }

    if(props.applicationDeclaration) {
      list.push({
        title: '申报书备案',
        href: '#d20',
        className: 'filled-item',
      })
    }
    if (props.showPromiseAnchor) {
      list.push({
        title: '申报人承诺',
        href: '#d17',
        className: 'promise-item',
      })
    }

    // 在 APPLY_2023 模式下添加项目申报书上传导航项
    if (props.operationType === ProjectOperationType.APPLY_2023 || props.isProject2023) {
      list.push({
        title: '项目申报书上传',
        href: '#d99',
        className: hasApplicationFiles ? 'filled-item red-text' : 'red-text', // 添加自定义类名
      })
    }

    return list
  })

  //tp1 详情填报显示
  const containerRef = ref<HTMLElement | undefined>(undefined)

  /**
   * 追加一行研究内容与考核指标
   */
  const appendResearchIndex = () => {
    const newNode: ResearchIndex = {
      seq: saveForm.value.researchIndexData.length + 1, // seq设置为当前数组的长度加1
      semiAnnualPeriod: 1,
      timeRange: null,
      startTime: null,
      endTime: null,
      researchContent: null,
      assessmentIndex: null,
    }
    // 将新节点添加到数组中
    saveForm.value.researchIndexData.push(newNode)
  }
  //删除所有阶段计划节点
  const deleteAllResearchIndex = () => {
    while (saveForm.value.researchIndexData.length > 0) {
      saveForm.value.researchIndexData.pop()
    }
  }
  /**
   * 删除单个阶段计划节点
   * @param index 当前行的索引
   */
  const deleteCurrentNode = (index: number) => {
    // 出栈该元素
    saveForm.value.researchIndexData.splice(index, 1)
    // 更新剩余元素的seq
    saveForm.value.researchIndexData.forEach((item, i) => {
      item.seq = i + 1 // 因为seq是从1开始的
    })
  }

  const researchIndexRef = ref<EditableInst | null>(null)

  const researchAndAssessmentColumns: EditableColumn<ResearchIndex>[] = [
    // {title: '序号', key: 'seq', align: 'center', width: 50, fixed: 'left', render: (row: ResearchIndex, index: number) => index + 1},
    // {
    //   title: '阶段',
    //   key: 'semiAnnualPeriod',
    //   align: 'center',
    //   minWidth: 150,
    //   // defaultValue: (index) => {
    //   //   return index + 1
    //   // },
    //   render: (row: ResearchIndex, index: number) => {
    //     return h(
    //         NFormItem,
    //         {
    //           ignorePathChange: true,
    //           path: `researchIndexData[${index}].semiAnnualPeriod`,
    //           rule: saveFormRules.researchIndexData.semiAnnualPeriod
    //         },
    //         () => h(
    //             NInputNumber,
    //             {
    //               class: 'enlarged',
    //               size: 'large',
    //               value: row.semiAnnualPeriod = row.semiAnnualPeriod || index + 1, // 默认值设置为当前索引加1
    //               min: 1,
    //               // disabled: true,
    //               onUpdateValue: (val: number| null) => {
    //                 row.semiAnnualPeriod = val
    //               }
    //             },
    //         )
    //     )
    //   }
    // },
    // 渲染为日期范围选择器时，无法进行数据校验，需要绑定saveForm.researchIndexData[${index}].timeRange字段进行校验，或者拆成两个普通日期组件，然后进行校验
    // {
    //   title: '起止时间',
    //   key: 'timeRange',
    //   align: 'center',
    //   minWidth: 200,
    //   render: (row: ResearchIndex, index: number) => {
    //     return h(
    //       NFormItem,
    //       {
    //         ignorePathChange: true,
    //         path: `researchIndexData[${index}].timeRange`,
    //         rule: saveFormRules.researchIndexData.timeRange,
    //       },
    //       () => {
    //         return h(
    //           NDatePicker,
    //           {
    //             class: 'enlarged',
    //             size: 'large',
    //             formattedValue: row.timeRange,
    //             type: 'daterange',
    //             startPlaceholder: '起始时间',
    //             endPlaceholder: '终止时间',
    //             shortcuts: getRangeShortcuts({
    //               displayOrder: ['this_month', 'this_year', 'future_month', 'future_half_year', 'future_year'],
    //               text: {
    //                 future_month: '即日起一月',
    //                 future_half_year: '即日起半年',
    //                 future_year: '即日起一年',
    //               },
    //             }),
    //             onUpdateFormattedValue: (val: [string, string] | null) => {
    //               row.timeRange = val
    //               if (val) {
    //                 row.startTime = val[0]
    //                 row.endTime = val[1]
    //               } else {
    //                 row.startTime = null
    //                 row.endTime = null
    //               }
    //             },
    //           },
    //           {}
    //         )
    //       }
    //     )
    //   },
    // },
    {
      title: '起始时间',
      key: 'startTime',
      align: 'center',
      minWidth: 120,
      render: (row: ResearchIndex, index: number) => {
        // 如果startTime为空，则根据当前行的索引计算默认起始时间
        if (!row.startTime) {
          if (index === 0) {
            // 首行的起始时间应该为当前时间
            row.startTime = JPGlobal.timestampToTime(new Date().getTime())
          } else {
            // 除首行外，起始时间均为前一行的结束时间加一天
            row.startTime = JPGlobal.timestampToTime(
              new Date(saveForm.value.researchIndexData[index - 1].endTime).getTime() + 24 * 60 * 60 * 1000
            )
          }
        }
        return h(
          NFormItem,
          {
            ignorePathChange: true,
            path: `researchIndexData[${index}].startTime`,
            rule: saveFormRules.researchIndexData.startTime,
          },
          () =>
            h(NDatePicker, {
              class: 'enlarged',
              size: 'large',
              formattedValue: row.startTime,
              type: 'date',
              placeholder: '选择起始日期',
              disabled: props.readonly && props.operationType !== ProjectOperationType.FILL_BRIEF,
              isDateDisabled: (
                current: number,
                detail:
                  | {
                      type: 'date'
                      year: number
                      month: number
                      date: number
                    }
                  | { type: 'month'; year: number; month: number }
                  | { type: 'year'; year: number }
                  | {
                      type: 'quarter'
                      year: number
                      quarter: number
                    }
                  | { type: 'input' }
              ) => {
                console.log('current', JPGlobal.formatDatetime(current))
                return (
                  (current >= new Date(row.endTime).getTime() &&
                    current < new Date(saveForm.value.projectStartDate).getTime()) ||
                  (index !== 0 && current <= new Date(saveForm.value.researchIndexData[index - 1]?.endTime).getTime())
                )
              },
              onUpdateFormattedValue: (val: string | null) => {
                row.startTime = val
              },
            })
        )
      },
    },
    {
      title: '结束时间',
      key: 'endTime',
      align: 'center',
      minWidth: 120,
      render: (row: ResearchIndex, index: number) => {
        // 如果endTime为空，则计算默认结束时间
        if (!row.endTime) {
          const endTime = new Date(row.startTime)
          endTime.setMonth(endTime.getMonth() + 6, endTime.getDate() - 1)
          row.endTime = JPGlobal.timestampToTime(endTime.getTime())
        }
        return h(
          NFormItem,
          {
            ignorePathChange: true,
            path: `researchIndexData[${index}].endTime`,
            rule: saveFormRules.researchIndexData.endTime,
            disabled: props.readonly || props.operationType === ProjectOperationType.FILL_BRIEF,
          },
          () =>
            h(NDatePicker, {
              class: 'enlarged',
              size: 'large',
              formattedValue: row.endTime,
              type: 'date',
              placeholder: '选择结束日期',
              disabled: props.readonly && props.operationType !== ProjectOperationType.FILL_BRIEF,
              isDateDisabled: (
                current: number,
                detail:
                  | {
                      type: 'date'
                      year: number
                      month: number
                      date: number
                    }
                  | { type: 'month'; year: number; month: number }
                  | { type: 'year'; year: number }
                  | {
                      type: 'quarter'
                      year: number
                      quarter: number
                    }
                  | { type: 'input' }
              ) => {
                return (
                  current < new Date(row.startTime).getTime() ||
                  (index !== saveForm.value.researchIndexData.length - 1 &&
                    current >= new Date(saveForm.value.researchIndexData[index + 1]?.startTime).getTime())
                )
              },
              onUpdateFormattedValue: (val: string | null) => {
                row.endTime = val
              },
            })
        )
      },
    },
    {
      title: '研究内容',
      key: 'researchContent',
      align: 'left',
      minWidth: 200,
      render: (row: ResearchIndex, index: number) => {
        return h(
          NFormItem,
          {
            ignorePathChange: true,
            path: `researchIndexData[${index}].researchContent`,
            rule: saveFormRules.researchIndexData.researchContent,
          },
          () => {
            return h(
              NInput,
              {
                class: 'enlarged',
                size: 'large',
                type: 'textarea',
                value: row.researchContent,
                disabled: props.operationType === ProjectOperationType.DETAILS,
                maxlength: 200,
                showCount: true,
                autosize: { minRows: 3 },
                clearable: true,
                onUpdateValue: (val: string | null) => {
                  row.researchContent = val
                },
              },
              {}
            )
          }
        )
      },
    },
    {
      title: '考核指标',
      key: 'assessmentIndex',
      align: 'left',
      minWidth: 200,
      render: (row: ResearchIndex, index: number) => {
        return h(
          NFormItem,
          {
            ignorePathChange: true,
            path: `researchIndexData[${index}].assessmentIndex`,
            rule: saveFormRules.researchIndexData.assessmentIndex,
          },
          () => {
            return h(
              NInput,
              {
                class: 'enlarged',
                size: 'large',
                type: 'textarea',
                value: row.assessmentIndex,
                disabled: props.operationType === ProjectOperationType.DETAILS,
                maxlength: 200,
                showCount: true,
                autosize: { minRows: 3 },
                clearable: true,
                onUpdateValue: (val: string | null) => {
                  row.assessmentIndex = val
                },
              },
              {}
            )
          }
        )
      },
    },
    // {
    //   title: '操作', key: 'operation', width: 20, fixed: 'right', align: 'center',
    //   render: (row: any, idx: number) => {
    //     if (!props.readonly) {
    //       return h(JIcon, {
    //         name: 'delete',
    //         style: 'width:20px; height:20px',
    //         onClick: (e: Event) => {
    //           e.stopPropagation()
    //           deleteCurrentNode(idx)
    //         },
    //       })
    //     }
    //   },
    // }
  ]

  //研究仪器设备登记
  const instrumentsAndEquipmentRef = ref<DataTableInst | null>(null)

  //新增一行仪器设备登记记录
  const addEquipmentReg = () => {
    const newNodeEquReg: InstrumentsEquipment = {
      seq: saveForm.value.equipmentRegData.length + 1, // seq设置为当前数组的长度加1
      equName: null,
      equModel: null,
      fyInstrUsageCnt: null,
      secInstrUsageCnt: null,
    }
    // 将新节点添加到数组中
    saveForm.value.equipmentRegData.push(newNodeEquReg)
  }
  const deleteAllEquRegNode = () => {
    while (saveForm.value.equipmentRegData.length > 0) {
      saveForm.value.equipmentRegData.pop()
    }
  }
  /**
   * 删除单个仪器设备登记记录
   * @param index 当前行的索引
   */
  const deleteCurrentEquRegNode = (index: number) => {
    // 出栈该元素
    saveForm.value.equipmentRegData.splice(index, 1)
    // 更新剩余元素的seq
    saveForm.value.equipmentRegData.forEach((item, i) => {
      item.seq = i + 1 // 因为seq是从1开始的
    })
  }

  const instrumentsEquipmentRef = ref<EditableInst | null>(null)

  const instrumentsAndEquipmentColumns: EditableColumn<InstrumentsEquipment>[] = [
    // {title: '序号', key: 'seq', align: 'center', width: 50, fixed: 'left'},
    {
      title: '仪器/设备名称',
      key: 'equName',
      align: 'center',
      width: 150,
      render: (row: InstrumentsEquipment, index: number) => {
        return h(
          NFormItem,
          {
            ignorePathChange: true,
            path: `equipmentRegData[${index}].equName`,
            rule: saveFormRules.equipmentRegData.equName,
          },
          () => {
            return h(NInput, {
              class: 'enlarged',
              size: 'large',
              value: row.equName,
              maxlength: 100,
              onUpdateValue: (val: string | null) => {
                row.equName = val
              },
            })
          }
        )
      },
    },
    {
      title: '仪器/设备型号',
      key: 'equModel',
      align: 'center',
      width: 150,
      render: (row: InstrumentsEquipment, index: number) => {
        return h(
          NFormItem,
          {
            ignorePathChange: true,
            path: `equipmentRegData[${index}].equModel`,
            rule: saveFormRules.equipmentRegData.equModel,
          },
          () => {
            return h(NInput, {
              class: 'enlarged',
              size: 'large',
              value: row.equModel,
              maxlength: 100,
              onUpdateValue: (val: string | null) => {
                row.equModel = val
              },
            })
          }
        )
      },
    },
    {
      title: '仪器/设备预计使用例数',
      key: 'instrUsageCnt',
      align: 'center',
      children: [
        {
          title: '第一年',
          key: 'fyInstrUsageCnt',
          align: 'center',
          width: 180,
          render: (row: InstrumentsEquipment, index: number) => {
            return h(
              NFormItem,
              {
                ignorePathChange: true,
                path: `equipmentRegData[${index}].fyInstrUsageCnt`,
                rule: saveFormRules.equipmentRegData.fyInstrUsageCnt,
              },
              () => {
                return h(
                  NInputNumber,
                  {
                    class: 'enlarged',
                    size: 'large',
                    value: row.fyInstrUsageCnt,
                    min: 0,
                    precision: 0,
                    onUpdateValue: (val: number | null) => {
                      row.fyInstrUsageCnt = val
                    },
                  },
                  {}
                )
              }
            )
          },
        },
        {
          title: '第二年',
          key: 'secInstrUsageCnt',
          align: 'center',
          width: 180,
          render: (row: InstrumentsEquipment, index: number) => {
            return h(
              NFormItem,
              {
                ignorePathChange: true,
                path: `equipmentRegData[${index}].secInstrUsageCnt`,
                rule: saveFormRules.equipmentRegData.secInstrUsageCnt,
              },
              () => {
                return h(
                  NInputNumber,
                  {
                    class: 'enlarged',
                    size: 'large',
                    value: row.secInstrUsageCnt,
                    min: 0,
                    precision: 0,
                    onUpdateValue: (val: number | null) => {
                      row.secInstrUsageCnt = val
                    },
                  },
                  {}
                )
              }
            )
          },
        },
      ],
    },
    // {
    //   title: '操作', key: 'operation', width: 20, fixed: 'right', align: 'center',
    //   render: (row: any, idx: number) => {
    //     if (!props.readonly) {
    //       return h(JIcon, {
    //         name: 'delete',
    //         style: 'width:20px; height:20px',
    //         onClick: (e: Event) => {
    //           e.stopPropagation()
    //           deleteCurrentEquRegNode(idx)
    //         },
    //       })
    //     }
    //   },
    // }
  ]

  //项目经费支出
  const expendProFundsColumns = ref<DataTableColumn<ExpenditureTreeNode>[]>([
    {
      title: '概算科目名称',
      key: 'fundingName',
      align: 'left',
    },
    {
      title: '项目专项经费',
      key: 'projectSpecialFunding',
      align: 'center',
      minWidth: 80,
      render: (row: ExpenditureTreeNode) => {
        const feedback = ref('')
        const status = ref<'success' | 'warning' | 'error'>('success')
        return h(
          NFormItem,
          {
            ignorePathChange: true,
            feedback: feedback.value,
          },
          () =>
            h(
              NInputNumber,
              {
                class: 'enlarged',
                size: 'large',
                status: status.value,
                value: row.projectSpecialFunding,
                disabled: props.readonly && props.operationType !== ProjectOperationType.FILL_BRIEF,
                min: 0,
                max: row.parentFundingId
                  ? fundsExpenseFlattenData.value.find(f => f.fundingId === row.parentFundingId)?.projectSpecialFunding
                  : saveForm.value.rmsProjectFundingIncomeDto.applyProjFunds,
                clearable: true,
                precision: 2,
                showButton: false,
                onUpdateValue: (val: number | null) => {
                  // TODO 校验当前类型的所有值之和是否超过父级项目专项经费
                  console.log(fundsExpenseFlattenData.value)
                  // 计算 fundsExpenseFlattenData 中fundingId = row.parentFundingId 的projectSpecialFunding总和
                  const total = fundsExpenseFlattenData.value
                    .filter(f => f.parentFundingId === row.parentFundingId)
                    .reduce(
                      (acc, cur) => acc.add(new Decimal(cur.projectSpecialFunding ?? 0)),
                      new Decimal(0)
                    ) as Decimal
                  if (
                    total.greaterThan(
                      row.parentFundingId
                        ? fundsExpenseFlattenData.value.find(f => f.fundingId === row.parentFundingId)
                            ?.projectSpecialFunding
                        : saveForm.value.rmsProjectFundingIncomeDto.applyProjFunds
                    )
                  ) {
                    feedback.value = '自筹经费不能超过父级概算科目经费'
                    status.value = 'error'
                  } else {
                    feedback.value = ''
                    status.value = 'success'
                  }
                  console.log(total.toNumber())
                  row.projectSpecialFunding = val
                  row.subjectTotalFunding = new Decimal(ifNull(val, 0))
                    .add(new Decimal(ifNull(row.selfFunded, 0)))
                    .toNumber()
                },
              },
              {
                prefix: () => '￥',
                suffix: () => '万元',
              }
            )
        )
      },
    },
    {
      title: '自筹经费',
      key: 'selfFunded',
      align: 'center',
      minWidth: 80,
      render: (row: ExpenditureTreeNode) => {
        return h(
          NInputNumber,
          {
            class: 'enlarged',
            size: 'large',
            value: row.selfFunded,
            disabled: props.readonly && props.operationType !== ProjectOperationType.FILL_BRIEF,
            min: 0,
            max: row.parentFundingId
              ? fundsExpenseFlattenData.value.find(f => f.fundingId === row.parentFundingId)?.selfFunded
              : saveForm.value.rmsProjectFundingIncomeDto.selfFundedFunds,
            clearable: true,
            precision: 2,
            showButton: false,
            onUpdateValue: (val: number | null) => {
              row.selfFunded = val
              row.subjectTotalFunding = new Decimal(ifNull(row.projectSpecialFunding, 0))
                .add(new Decimal(ifNull(val, 0)))
                .toNumber()
              // row.subjectTotalFunding = ifNull(row.projectSpecialFunding, 0) + ifNull(val, 0)
            },
          },
          {
            prefix: () => '￥',
            suffix: () => '万元',
          }
        )
      },
    },
    {
      title: '科目总经费',
      key: 'subjectTotalFunding',
      align: 'center',
      minWidth: 80,
      render: (row: ExpenditureTreeNode) => {
        return h(
          NInputNumber,
          {
            class: 'enlarged',
            size: 'large',
            value: row.subjectTotalFunding,
            min: 0,
            max: row.parentFundingId ? row.subjectTotalFunding : saveForm.value.rmsProjectFundingIncomeDto.projTotal,
            clearable: true,
            precision: 2,
            showButton: false,
            disabled: true,
            onUpdateValue: (val: number | null) => {
              row.subjectTotalFunding = val
            },
          },
          {
            prefix: () => '￥',
            suffix: () => '万元',
          }
        )
      },
    },
  ])

  //合作单位
  const cooperativeUnitsRef = ref<DataTableInst | null>(null)

  //新增一行
  const addCooperativeUnitsLine = () => {
    const newNodeCoopUnits: CooperativeUnit = {
      seq: saveForm.value.cooperativeUnitsData.length + 1, // seq设置为当前数组的长度加1
      unitName: null,
      divide: null,
      attachments: [],
    }
    // 将新节点添加到数组中
    saveForm.value.cooperativeUnitsData.push(newNodeCoopUnits)
  }
  //删除所有
  const deleteAllCooperativeUnits = () => {
    while (saveForm.value.cooperativeUnitsData.length > 0) {
      saveForm.value.cooperativeUnitsData.pop()
    }
  }

  /**
   * 删除当前行
   * @param index 当前行的索引
   */
  const deleteCurCooperativeUnits = (index: number) => {
    // 出栈该元素
    saveForm.value.cooperativeUnitsData.splice(index, 1)
    // 更新剩余元素的seq
    saveForm.value.cooperativeUnitsData.forEach((item, i) => {
      item.seq = i + 1 // 因为seq是从1开始的
    })
  }

  const cooperativeUnitRef = ref<EditableInst | null>(null)

  const cooperativeUnitsColumns: EditableColumn<CooperativeUnit>[] = [
    // {title: '顺序', key: 'seq', align: 'center', minWidth: 50, fixed: 'left'},
    {
      title: '单位名称',
      key: 'unitName',
      align: 'center',
      minWidth: 200,
      render: (row: CooperativeUnit, index: number) => {
        return h(
          NFormItem,
          {
            ignorePathChange: true,
            path: `cooperativeUnitsData[${index}].unitName`,
            rule: saveFormRules.cooperativeUnitsData.unitName,
          },
          () => {
            return h(
              NInput,
              {
                class: 'enlarged',
                size: 'large',
                value: row.unitName,
                maxlength: 100,
                onUpdateValue: (val: string | null) => {
                  row.unitName = val
                },
              },
              {}
            )
          }
        )
      },
    },
    {
      title: '在本项目中分工',
      key: 'divide',
      align: 'left',
      minWidth: 300,
      render: (row: CooperativeUnit, index: number) => {
        return h(
          NFormItem,
          {
            ignorePathChange: true,
            path: `cooperativeUnitsData[${index}].divide`,
            rule: saveFormRules.cooperativeUnitsData.divide,
          },
          () => {
            return h(
              NInput,
              {
                class: 'enlarged',
                size: 'large',
                type: 'textarea',
                value: row.divide,
                maxlength: 200,
                showCount: true,
                onUpdateValue: (val: string | null) => {
                  row.divide = val
                },
              },
              {}
            )
          }
        )
      },
    },
    {
      title: '联系人',
      key: 'contactPerson',
      align: 'left',
      minWidth: 150,
      render: (row: CooperativeUnit, index: number) => {
        return h(
          NFormItem,
          {
            ignorePathChange: true,
            path: `cooperativeUnitsData[${index}].contactPerson`,
            rule: saveFormRules.cooperativeUnitsData.contactPerson,
          },
          () => {
            return h(
              NInput,
              {
                class: 'enlarged',
                size: 'large',
                value: row.contactPerson,
                maxlength: 50,
                onUpdateValue: (val: string | null) => {
                  row.contactPerson = val
                },
              },
              {}
            )
          }
        )
      },
    },
    {
      title: '联系方式',
      key: 'contactPhone',
      align: 'left',
      minWidth: 150,
      render: (row: CooperativeUnit, index: number) => {
        return h(
          NFormItem,
          {
            ignorePathChange: true,
            path: `cooperativeUnitsData[${index}].contactPhone`,
            rule: saveFormRules.cooperativeUnitsData.contactPhone,
          },
          () => {
            return h(
              NInput,
              {
                class: 'enlarged',
                size: 'large',
                value: row.contactPhone,
                maxlength: 20,
                onUpdateValue: (val: string | null) => {
                  row.contactPhone = val
                },
              },
              {}
            )
          }
        )
      },
    },
    {
      title: '附件',
      key: 'attachments',
      align: 'left',
      minWidth: 400,
      render: (row: CooperativeUnit, index: number) => {
        return h(
          NFormItem,
          {
            ignorePathChange: true,
            path: `cooperativeUnitsData[${index}].attachments`,
            rule: saveFormRules.cooperativeUnitsData.attachments,
          },
          () =>
            h(YtUpload, {
              fileList: row.attachments,
              uploadFn: uploadRmsFile,
              disabled: props.readonly,
              accept: '.pdf,.doc,.docx,image/*',
              autoUpload: true,
              maxSize: 5 * 1024,
              limit: 5,
              onUpdateFileList: (val: Array<Attachment>) => {
                row.attachments = val
              },
              onBeforeUpload: () => {
                disableSaveDraft()
              },
              onSuccess: () => {
                enableSaveDraft()
              },
              onError: () => {
                enableSaveDraft()
              },
            })
          // () => {
          //   const config: UploadConfig = {
          //     max: 5
          //   }
          //   return h(
          //       NUpload,
          //       {
          //         listType: 'image',
          //         createThumbnailUrl,
          //         multiple: config.max > 1,
          //         max: config.max,
          //         defaultFileList: row.attachments,
          //         disabled: props.readonly,
          //         customRequest: async ({ file, onFinish, onError, onProgress }: UploadCustomRequestOptions) => {
          //           loading.value.saveDraft = true
          //           try {
          //             const { data } = await uploadRmsFile(file.file, onProgress)
          //             loading.value.saveDraft = false
          //             window.$message.success('上传成功')
          //             onFinish()
          //             row.attachments?.push({
          //               id: file.id,
          //               name: file.name,
          //               size: file.file.size,
          //               type: file.file.type,
          //               fullPath: data.bucket + '/' + data.path,
          //               status: 'finished',
          //             })
          //           } catch (e) {
          //             loading.value.saveDraft = false
          //             onError()
          //           }
          //         },
          //         onRemove: async (options: { file: UploadFileInfo, fileList: Array<UploadFileInfo>, index: number }) => {
          //           row.attachments = row.attachments?.filter((item) => item.id !== options.file.id)
          //         },
          //         showDownloadButton: true,
          //         onDownload: async (file) => {
          //           const parts = file.fullPath.split('/')
          //           handleShowPreview(parts[0], parts.slice(1).join('/'))
          //           return false
          //         }
          //       },
          //       () => h(
          //           NButton,
          //           {
          //             disabled: props.readonly || row.attachments?.length >= config.max,
          //           },
          //           {
          //             default: '选择文件',
          //             icon: () => h(
          //                 NIcon,
          //                 {
          //                   size: 24,
          //                 },
          //                 () => h(UploadFilled)
          //             )
          //           }
          //       )
          //   )
          // }
        )
      },
    },
    // {
    //   title: '操作', key: 'operation', minWidth: 50, fixed: 'right', align: 'center',
    //   render: (row: any, idx: number) => {
    //     if (!props.readonly) {
    //       return h(JIcon, {
    //         name: 'delete',
    //         style: 'width:20px; height:20px',
    //         onClick: (e: Event) => {
    //           e.stopPropagation()
    //           deleteCurCooperativeUnits(idx)
    //         },
    //       })
    //     }
    //   },
    // }
  ]

  const researchersOfGroupRef = ref<DataTableInst | null>(null)

  //新增一行记录
  const addResearchersLine = () => {
    const newNodeResearchers: MainMember = {
      seq: saveForm.value.researchersData.length + 1, // seq设置为当前数组的长度加1
      internal: true,
      name: null,
      username: null,
      sex: null,
      birthday: null,
      workUnit: null,
      idCard: null,
      academicDegree: null,
      professionalTitle: null,
      currentProfessional: null,
      division: null,
      studyTime: null,
      signature: null,
    }
    // 将新节点添加到数组中
    saveForm.value.researchersData.push(newNodeResearchers)
  }

  /**
   * 删除所有记录（首行数据为项目负责人的数据，不可删除）
   */
  const deleteAllResearchers = () => {
    while (saveForm.value.researchersData.length > 1) {
      saveForm.value.researchersData.pop()
    }
    // while (saveForm.value.researchersData.length > 0) {
    //   saveForm.value.researchersData.pop();
    // }
  }
  /**
   * 删除当前行记录
   * @param index 当前行的索引
   */
  const deleteCurResearchers = (index: number) => {
    if (index < 0) {
      return
    }
    // 出栈该元素
    saveForm.value.researchersData.splice(index, 1)
    // 更新剩余元素的seq
    saveForm.value.researchersData.forEach((item, i) => {
      item.seq = i + 1 // 因为seq是从1开始的
    })
  }

  const mainMemberRef = ref<EditableInst | null>(null)

  const researchersOfGroupColumns = ref<EditableColumn<MainMember>[]>([
    // {title: '序号', key: 'seq', align: 'center', width: 50, fixed: 'left', render: (row: MainMember, index: number) => index + 1},
    // {
    //   title: '档案管理员',
    //   key: 'manageable',
    //   align: 'center',
    //   width: 100,
    //   fixed: 'left',
    //   render: (row: MainMember, index: number) => {
    //     row.manageable = index === 0
    //     return h(
    //         NTooltip,
    //         {
    //           trigger: 'hover',
    //         },
    //         {
    //           default: () => '将TA设为档案管理员',
    //           trigger: () => h(
    //               NButton,
    //               {
    //                 text: true,
    //                 type: row.manageable ? 'warning' : 'default',
    //                 style: {
    //                   cursor: row.manageable ? 'not-allowed' : 'pointer'
    //                 },
    //                 onClick: row.manageable ? undefined : () => {
    //                   saveForm.value.researchersData.forEach((item, i) => item.manageable = i === index)
    //                   // mainMemberRef.value?.reload();
    //                   // row.manageable = true;
    //                 },
    //                 renderIcon: () => h(
    //                     NIcon,
    //                     {
    //                       size: 24,
    //                       component: PersonCircleOutline,
    //                     }
    //                 )
    //               }
    //           )
    //         }
    //     )
    //   }
    // },
    {
      title: '是否内部人员',
      key: 'internal',
      align: 'center',
      width: 135,
      defaultValue: true,
      render: (row: MainMember, index: number) => {
        return h(
          NFormItem,
          {
            ignorePathChange: true,
            path: `researchersData[${index}].internal`,
            rule: saveFormRules.researchersData.internal,
          },
          () =>
            h(
              NRadioGroup,
              {
                class: 'enlarged',
                size: 'large',
                value: row.internal,
                name: 'internal',
                disabled: props.readonly || index === 0,
                onUpdateValue: (val: boolean) => {
                  console.log(val)
                  // 当选择外部人员时，清空用户名
                  if (!val) {
                    row.username = null
                  } else {
                    // 当选择内部人员时，设置默认学位为博士，职称为高级
                  }
                  row.internal = val
                  console.log(row.internal)
                },
              },
              () =>
                [
                  { label: '是', value: true },
                  { label: '否', value: false },
                ].map(item =>
                  h(NRadio, {
                    label: item.label,
                    value: item.value,
                  })
                )
            )
        )
      },
    },
    {
      title: '姓名',
      key: 'name',
      align: 'center',
      width: 240,
      render: (row: MainMember, index: number) => {
        return h(
          NFormItem,
          {
            ignorePathChange: true,
            path: `researchersData[${index}].username`,
            rule: saveFormRules.researchersData.username,
          },
          () => {
            if (row.internal) {
              return h(
                JBusEmpSearch,
                {
                  class: 'enlarged',
                  size: 'large',
                  value: row.username,
                  disabled: props.readonly || index === 0,
                  // onUpdateValue: (val: string | null) => {
                  //   row.username = val
                  //   saveForm.value.projectLeaderUsername = val
                  // },
                  onChangeValue: (val: any) => {
                    if (index === 0) {
                      // 此处若更新ProjectLeaderUsername会导致循环，所以禁用首行人员选择器，需要修改必须在封面处修改
                      // handleUpdateProjectLeader(val)
                    } else {
                      row.username = val.empCode
                      row.name = val.empName
                      row.sex = val.sex === '318' ? '1' : val.sex === '319' ? '2' : null
                      row.birthday = JPGlobal.formatDatetime(val.birthday, 'yyyy-MM')
                      row.idCard = val.icdCard
                      row.workUnit = '中江县人民医院'
                      queryEmployeeDetailsData({ empId: val.id }).then(res => {
                        //学位选择
                        // row.academicDegree = res.data.educationList[res.data.educationList.length - 1].degree
                        queryEmployeeTreeDict({ codeType: 'DEGREE_TYPE' }).then(res1 => {
                          // 查找学位选项的函数
                          const findDegreeOption = (treeData: any[], degreeId: string): any | null => {
                            for (const node of treeData) {
                              // 检查当前节点id是否匹配
                              if (node.code === degreeId || node.id === Number(degreeId)) {
                                return node
                              }
                              // 递归查找子节点
                              if (node.children && node.children.length > 0) {
                                const found = findDegreeOption(node.children, degreeId)
                                if (found) return found
                              }
                            }
                            return null
                          }

                          // 获取最高学历的学位ID
                          const degreeId = res.data.educationList[res.data.educationList.length - 1].degree

                          // 在学位树中查找对应选项
                          const degreeOption = findDegreeOption(res1.data, degreeId)

                          if (degreeOption) {
                            // 获取学位的父级标签
                            const parentCodeLabel = degreeOption.parentCodeLabel || degreeOption.label

                            // 在ACADEMIC_DEGREE选项中查找匹配的标签
                            const matchedOption = options.value.ACADEMIC_DEGREE.find(
                              option => option.label === parentCodeLabel
                            )

                            if (matchedOption) {
                              // 如果找到匹配的选项，使用其value值

                              row.academicDegree = String(matchedOption.value)
                            } else {
                              // 如果没找到匹配项，使用原来的逻辑
                              row.academicDegree = degreeOption.parentId
                                ? String(degreeOption.parentId)
                                : String(degreeOption.id)
                            }
                          } else {
                            // 如果没找到匹配项，尝试直接使用degreeId，确保是字符串类型
                            row.academicDegree = String(degreeId)
                          }
                        })
                      })
                    }
                  },
                },
                {}
              )
            } else {
              return h(
                NInput,
                {
                  class: 'enlarged',
                  size: 'large',
                  value: row.name,
                  disabled: props.readonly,
                  maxlength: 50,
                  onUpdateValue: (val: string | null) => {
                    row.name = val
                  },
                },
                {}
              )
            }
          }
        )
      },
    },
    {
      title: '性别',
      key: 'sex',
      align: 'center',
      width: 135,
      render: (row: MainMember, index: number) => {
        return h(
          NFormItem,
          {
            ignorePathChange: true,
            path: `researchersData[${index}].sex`,
            rule: saveFormRules.researchersData.sex,
          },
          () =>
            h(
              NRadioGroup,
              {
                class: 'enlarged',
                size: 'large',
                value: row.sex,
                disabled: props.readonly,
                name: 'sex',
                onUpdateValue: (val: string | null) => {
                  row.sex = val
                  if (index === 0) {
                    saveForm.value.projectOrgStructure.prSex = val
                  }
                },
              },
              () => {
                return options.value.GENDERS?.map(item => {
                  return h(
                    NRadio,
                    {
                      key: item.value,
                      value: item.value,
                    },
                    item.label
                  )
                })
              }
            )
        )
      },
    },
    {
      title: '出生年月',
      key: 'birthday',
      align: 'center',
      width: 130,
      render: (row: MainMember, index: number) => {
        return h(
          NFormItem,
          {
            ignorePathChange: true,
            path: `researchersData[${index}].birthday`,
            rule: saveFormRules.researchersData.birthday,
          },
          () =>
            h(NDatePicker, {
              class: 'enlarged',
              size: 'large',
              formattedValue: row.birthday,
              disabled: props.readonly,
              type: 'month',
              isDateDisabled: (date: number) => date > Date.now(),
              onUpdateFormattedValue: (val: string | null) => {
                row.birthday = val
                if (index === 0) {
                  saveForm.value.projectOrgStructure.prBirthday = val
                }
              },
            })
        )
      },
    },
    {
      title: '工作单位',
      key: 'workUnit',
      align: 'center',
      width: 180,
      render: (row: MainMember, index: number) => {
        return h(
          NFormItem,
          {
            ignorePathChange: true,
            path: `researchersData[${index}].workUnit`,
            rule: saveFormRules.researchersData.workUnit,
          },
          () =>
            h(
              NInput,
              {
                class: 'enlarged',
                size: 'large',
                value: row.workUnit,
                disabled: props.readonly,
                maxlength: 50,
                onUpdateValue: (val: string | null) => {
                  row.workUnit = val
                },
              },
              {}
            )
        )
      },
    },
    {
      title: '身份证号',
      key: 'idCard',
      align: 'center',
      width: 210,
      render: (row: MainMember, index: number) => {
        return h(
          NFormItem,
          {
            ignorePathChange: true,
            path: `researchersData[${index}].idCard`,
            rule: saveFormRules.researchersData.idCard,
          },
          () =>
            h(
              NInput,
              {
                class: 'enlarged',
                size: 'large',
                value: row.idCard,
                disabled: props.readonly,
                minlength: 18,
                maxlength: 18,
                onUpdateValue: (val: string | null) => {
                  row.idCard = val
                },
              },
              {}
            )
        )
      },
    },
    {
      title: '学位',
      key: 'academicDegree',
      align: 'center',
      width: 120,
      render: (row: MainMember, index: number) => {
        return h(
          NFormItem,
          {
            ignorePathChange: true,
            path: `researchersData[${index}].academicDegree`,
            rule: saveFormRules.researchersData.academicDegree,
          },
          () =>
            h(
              NSelect,
              {
                class: 'enlarged',
                size: 'large',
                value: row.academicDegree,
                disabled: props.readonly,
                options: options.value.ACADEMIC_DEGREE,
                onUpdateValue: (val: string | null) => {
                  row.academicDegree = val
                  if (index === 0) {
                    saveForm.value.projectOrgStructure.prAcademic = val
                  }
                },
              },
              {}
            )
        )
      },
    },
    {
      title: '职称',
      key: 'professionalTitle',
      align: 'center',
      width: 180,
      render: (row: MainMember, index: number) => {
        return h(
          NFormItem,
          {
            ignorePathChange: true,
            path: `researchersData[${index}].professionalTitle`,
            rule: saveFormRules.researchersData.professionalTitle,
          },
          // () => h(
          //     NTreeSelect,
          //     {
          //       value: row.professionalTitle,
          //       options: options.value.JOB_TITLE,
          //       keyField: 'value',
          //       checkStrategy: 'child',
          //       onUpdateValue: (value: string | number | Array<string | number> | null, option: TreeSelectOption | null | Array<TreeSelectOption | null>, meta: { node: TreeOption | null, action: 'select' | 'unselect' | 'delete' | 'clear' }) => {
          //         row.professionalTitle = value
          //       }
          //     }
          // )
          () =>
            h(
              NInput,
              {
                class: 'enlarged',
                size: 'large',
                value: row.professionalTitle,
                disabled: props.readonly,
                maxlength: 50,
                onUpdateValue: (val: string | null) => {
                  row.professionalTitle = val
                  if (index === 0) {
                    saveForm.value.projectOrgStructure.prTitle = val
                  }
                },
              },
              {}
            )
        )
      },
    },
    {
      title: '现从事专业',
      key: 'currentProfessional',
      align: 'center',
      width: 150,
      render: (row: MainMember, index: number) => {
        return h(
          NFormItem,
          {
            ignorePathChange: true,
            path: `researchersData[${index}].currentProfessional`,
            rule: saveFormRules.researchersData.currentProfessional,
          },
          () =>
            h(
              NInput,
              {
                class: 'enlarged',
                size: 'large',
                value: row.currentProfessional,
                disabled: props.readonly,
                maxlength: 30,
                onUpdateValue: (val: string | null) => {
                  row.currentProfessional = val
                  if (index === 0) {
                    saveForm.value.projectOrgStructure.prProfession = val
                  }
                },
              },
              {}
            )
        )
      },
    },
    {
      title: '课题中的分工',
      key: 'division',
      align: 'left',
      titleAlign: 'center',
      width: 150,
      render: (row: MainMember, index: number) => {
        return h(
          NFormItem,
          {
            ignorePathChange: true,
            path: `researchersData[${index}].division`,
            rule: saveFormRules.researchersData.division,
          },
          () =>
            h(
              NInput,
              {
                class: 'enlarged',
                size: 'large',
                value: row.division,
                disabled: props.readonly,
                type: 'textarea',
                maxlength: 50,
                showCount: true,
                autosize: {
                  minRows: 2,
                },
                onUpdateValue: (val: string | null) => {
                  row.division = val
                },
              },
              {}
            )
        )
      },
    },
    {
      title: '研究时间(月/年)',
      key: 'studyTime',
      align: 'center',
      width: 130,
      render: (row: MainMember, index: number) => {
        return h(
          NFormItem,
          {
            ignorePathChange: true,
            path: `researchersData[${index}].studyTime`,
            rule: saveFormRules.researchersData.studyTime,
          },
          () =>
            h(
              NDatePicker,
              {
                class: 'enlarged',
                size: 'large',
                formattedValue: row.studyTime,
                disabled: props.readonly,
                type: 'month',
                format: 'MM/yyyy',
                onUpdateFormattedValue: (val: string | null) => {
                  row.studyTime = val
                },
              },
              {}
            )
        )
      },
    },
    // {
    //   title: '签名', key: 'signature', align: 'center', width: 150, render: (row: MainMember, index: number) => {
    //     return h(
    //         NFormItem,
    //         {
    //           ignorePathChange: true,
    //           path: `researchersData[${index}].signature`,
    //           rule: saveFormRules.researchersData.signature
    //         },
    //         () => {
    //           return h(
    //               NInput,
    //               {
    //                 class: 'enlarged',
    //                 size: 'large',
    //                 value: row.signature,
    //                 maxlength: 50,
    //                 onUpdateValue: (val: string | null) => {
    //                   row.signature = val
    //                 },
    //               },
    //               {}
    //           )
    //         }
    //     )
    //   }
    // },
    // {
    //   title: '操作', key: 'operation', width: 50, fixed: 'right', align: 'center',
    //   render: (row: any, idx: number) => {
    //     if (!props.readonly && idx !== 0) {
    //       return h(JIcon, {
    //         name: 'delete',
    //         style: 'width:20px; height:20px',
    //         onClick: (e: Event) => {
    //           e.stopPropagation()
    //           // saveForm.value.researchersData.splice(idx, 1)
    //           deleteCurResearchers(idx)
    //         },
    //       })
    //     }
    //   },
    // }
  ])

  const options = ref<RmsOptions>({
    PROJECT_LEVEL: [],
    TOPIC_CATEGORY: [],
    RESEARCH_TYPE: [],
    EXTERNAL_COOPERATION: [],
    INNOVATION_TYPE: [],
    INDUSTRY_ACADEMIA_COLLAB: [],
    IP_STATUS: [],
    RESULTLEVEL: [],
    RESULT_FORMAT: [],
    TECH_STD_DEV: [],
    RESULT_FORM: [],
    GENDERS: [],
    ACADEMIC_DEGREE: [],
    JOB_TITLE: [],
  })

  const performanceIndicators = ref<RmsPerformanceIndicator[]>([])

  const performanceIndicatorTree = ref<RmsPerformanceIndicatorTree[]>([])

  const initOptions = async () => {
    const [{ data: topicCategories }, { data: intendedUnits }, { data: selections }, { data: pis }] = await axios.all([
      queryRmsResearchTopic({}),
      queryRmsIntendedUnit(),
      queryRmsCheckBoxSelection(),
      queryRmsPerformanceIndicator(),
    ])
    options.value.TOPIC_CATEGORY = topicCategories?.map((item: RmsResearchTopic) => {
      return <Option>{
        id: item.id.toString(),
        label: item.name,
        value: item.id.toString(),
      }
    })
    options.value.INTENDED_UNIT = intendedUnits?.map((item: RmsIntendedUnit) => {
      return <Option>{
        id: item.id,
        label: item.unitName,
        value: item.id,
      }
    })
    options.value.INTENDED_UNIT.push({
      id: 0,
      label: '其他',
      value: 0,
    })
    options.value.PROJECT_LEVEL = getOptionsOfSelections(selections, 'PROJECT_LEVEL')
    options.value.RESEARCH_TYPE = getOptionsOfSelections(selections, 'RESEARCH_TYPE')
    options.value.EXTERNAL_COOPERATION = getOptionsOfSelections(selections, 'EXTERNAL_COOPERATION')
    options.value.INNOVATION_TYPE = getOptionsOfSelections(selections, 'INNOVATION_TYPE')
    options.value.INDUSTRY_ACADEMIA_COLLAB = getOptionsOfSelections(selections, 'INDUSTRY_ACADEMIA_COLLAB')
    options.value.IP_STATUS = getOptionsOfSelections(selections, 'IP_STATUS')
    options.value.RESULTLEVEL = getOptionsOfSelections(selections, 'RESULTLEVEL')
    options.value.RESULT_FORMAT = getOptionsOfSelections(selections, 'RESULT_FORMAT')
    options.value.TECH_STD_DEV = getOptionsOfSelections(selections, 'TECH_STD_DEV')
    options.value.RESULT_FORM = getOptionsOfSelections(selections, 'RESULT_FORM')
    options.value.GENDERS = JPGlobal.getDictByType('SEX')?.map((item: StoreSysDict) => {
      return <Option>{
        id: item.value,
        label: item.label,
        value: item.value,
      }
    })
    options.value.ACADEMIC_DEGREE = JPGlobal.getDictByType('ACADEMIC_DEGREE')?.map((item: StoreSysDict) => {
      return <Option>{
        id: item.value,
        label: item.label,
        value: item.value,
      }
    })
    options.value.JOB_TITLE = [
      {
        label: '高级',
        value: 'senior',
        children: [
          {
            label: '主任医师',
            value: 'doctor',
            parent: 'senior',
          },
          {
            label: '副主任医师',
            value: 'assistant',
            parent: 'senior',
          },
        ],
      },
      {
        label: '中级',
        value: 'intermediate',
        children: [
          {
            label: '主治医师',
            value: 'attending',
            parent: 'intermediate',
          },
        ],
      },
      {
        label: '初级',
        value: 'junior',
        children: [
          {
            label: '医师',
            value: 'doctor',
            parent: 'junior',
          },
          {
            label: '医士',
            value: 'medical_doctor',
            parent: 'junior',
          },
        ],
      },
      {
        label: '无',
        value: 'none',
      },
    ]
    // console.log(pis.map(pi => {
    //   return {
    //     id: pi.id,
    //     label: pi.label,
    //     num: pis.filter(item => item.parentId === pi.id).length,
    //     value: null
    //   }
    // }).filter(pi => pi.num === 0));
    // console.log(pis.filter((pi: RmsPerformanceIndicator) => pi.dataType > 0 && !pis.find((item: RmsPerformanceIndicator) => item.parentId === pi.id)))
    // pis.filter((pi: RmsPerformanceIndicator) => pi.dataType > 0 && !pis.find((item: RmsPerformanceIndicator) => item.parentId === pi.id)).forEach((item: RmsPerformanceIndicator) => {
    //   saveForm.value.performanceGoals[item.fieldName] = null
    // })
    // console.log(saveForm.value.performanceGoals)
    // saveForm.value.performanceGoals = pis.map((pi: RmsPerformanceIndicator) => ({
    //   id: pi.id,
    //   label: pi.label,
    //   leaf: !pis.find(item => item.parentId === pi.id),
    //   value: null
    // }))
    performanceIndicators.value = pis.filter(
      (pi: RmsPerformanceIndicator) =>
        pi.dataType > 0 && !pis.find((item: RmsPerformanceIndicator) => item.parentId === pi.id)
    )
    console.log(performanceIndicators.value)
    performanceIndicatorTree.value = JPGlobal.getTreeNode(pis, 'id', 'parentId')
  }

  watchEffect(async () => {
    const { data } = await queryRmsIntendedUnitSpecial({ unitId: saveForm.value.intendedUnitId })
    options.value.INTENDED_UNIT_SPECIAL = data?.map((item: RmsIntendedUnitSpecial) => {
      return <Option>{
        id: item.id,
        label: item.name,
        value: item.id,
      }
    })
    options.value.INTENDED_UNIT_SPECIAL.push({
      id: 0,
      label: '其他',
      value: 0,
    })
  })

  const init = () => {
    let today = new Date()
    let year = today.getFullYear()
    let month = (today.getMonth() + 1).toString().padStart(2, '0')
    let day = today.getDate().toString().padStart(2, '0')
    if (month.startsWith('0')) {
      month = month.slice(1, 2)
    }
    showYearMonth.value = `${year}年${month}月`
    showYear.value = `${year}年`
  }

  /**
   * 初始化研究指标数据
   */
  const initResearchIndexData = () => {
    if (saveForm.value.researchIndexData.length === 0) {
      saveForm.value.researchIndexData.push({
        seq: 1,
        semiAnnualPeriod: 1,
        timeRange: null,
        startTime: null,
        endTime: null,
        researchContent: null,
        assessmentIndex: null,
      })
    }
  }

  // 项目原始数据，用于任务书填报时限制绩效目标仅能向上调整，但不能向下调整
  // 目前仅在页面加载时获取了，若需要点击保存后立即更新初始数据，请在saveDraft()方法中将新数据写入到该字段中
  const originalData = ref<RmsProjectVO>({})

  const performanceGoalsTree = ref<RmsProjectPerformanceGoalVOTreeNode[]>([])

  const transPerformanceGoals = (goals: RmsProjectPerformanceGoalVO[]) => {
    // performanceGoalsTree.value = JPGlobal.getTreeNode(
    //   goals.map((item, index) => ({
    //     ...item,
    //     index,
    //   })),
    //   'indicatorId',
    //   'parentIndicatorId',
    //   'label',
    //   'sort'
    // )
    performanceGoalsTree.value = JPGlobal.array2Tree<RmsProjectPerformanceGoalVO>(
      goals.map((item, index) => ({
        ...item,
        index,
      })),
      0,
      {
        idKey: 'indicatorId',
        parentIdKey: 'parentIndicatorId',
        sort: (a, b) => a.performanceIndicator.sort - b.performanceIndicator.sort,
      }
    )
  }

  const detailsInit = async () => {
    // 禁用操作按钮
    disabled.value.downloadDeclaration = true
    disabled.value.saveDraft = true

    const { data } = await queryProjectDetails({ id: props.projectId })
    // 存储项目的原始数据，采用深拷贝，避免因为页面数据的变化影响原始数据
    originalData.value = JPGlobal.deepCopy(data)
    for (const key in data) {
      // 检查 saveForm.value 是否有相同的键
      if (saveForm.value.hasOwnProperty(key)) {
        saveForm.value[key] = data[key]
      }
    }
    transPerformanceGoals(data.rmsProjectPerformanceGoalData)
    console.log(performanceGoalsTree.value)
    // 如果researchIndexData 为空，则添加一个默认值
    initResearchIndexData()
    // 初始化时间范围选择器数据
    saveForm.value.projectDateRange =
      data.projectStartDate && data.projectEndDate ? [data.projectStartDate, data.projectEndDate] : null
    data.researchIndexData?.forEach((item: ResearchIndex, index: number) => {
      saveForm.value.researchIndexData[index].timeRange =
        item?.startTime && item?.endTime ? [item.startTime, item.endTime] : null
    })
    // 初始化职工人数
    // await initEmpNum()
    swotAttachmentConfig.value.fileList = saveForm.value.swotAttachments || []
    saveForm.value.cooperativeUnitsData.forEach(item => {
      if (!item.attachments) {
        item.attachments = []
      }
    })
    researchersSignAttachmentsConfig.value.fileList = saveForm.value.researchersSignAttachments || []

    // 重载editable
    researchIndexRef.value?.reload()
    instrumentsEquipmentRef.value?.reload()
    cooperativeUnitRef.value?.reload()
    mainMemberRef.value?.reload()

    // 恢复操作按钮
    disabled.value.downloadDeclaration = false
    disabled.value.saveDraft = false
  }

  const initFundsExpenseTree = async () => {
    let { code, data } = await queryRmsFundingBudgetCfg({})
    if (code === 200) {
      data = data.map(item => ({
        ...item,
        fundingId: item.id,
      }))
      data.forEach(item => {
        delete item.id
      })
      saveForm.value.fundsExpenseTreeData = JPGlobal.getTreeNode(data, 'fundingId', 'parentId', 'fundingName')
    }
  }

  const empCountRef = ref<HTMLInputElement | null>(null)

  const { getUserInfo } = useUserStore()

  /**
   * 初始化申报信息
   */
  const initDeclareInfo = async () => {
    // 项目负责人信息
    console.log(getUserInfo)
    saveForm.value.projectLeaderUsername = getUserInfo.username
    // 申报单位信息
    saveForm.value.projectOrgStructure.acName = '中江县人民医院'
    saveForm.value.projectOrgStructure.acCode = 'H51062300184'
    saveForm.value.projectOrgStructure.acAddr = '中江县凯江镇大北街96号, 中江县南华镇一环路南段999号'
    saveForm.value.projectOrgStructure.acPostal = '618100'
    saveForm.value.projectOrgStructure.acType = '国家三级甲等综合医院'
    saveForm.value.projectOrgStructure.acDept = '科教科'
    saveForm.value.projectOrgStructure.acRespe = '王铸'
    saveForm.value.projectOrgStructure.acRespeDept = '科教科'
    saveForm.value.projectOrgStructure.acContact = '纪函玎'
    saveForm.value.projectOrgStructure.acContactLandline = '0838-7202370'
    saveForm.value.projectOrgStructure.acContactPhone = '13890248189'
    // 获取在职人数
    const { data } = await withIncumbentNum({})
    saveForm.value.projectOrgStructure.empCount = data
    saveForm.value.projectOrgStructure.orgNature = '事业单位'
    saveForm.value.projectOrgStructure.upperAdminDept = '中江县卫生健康局'
  }

  // 如果researchersData 为空，则添加一个默认值：首行数据为项目负责人信息
  const initResearchers = () => {
    if (saveForm.value.researchersData.length === 0) {
      saveForm.value.researchersData.push({
        seq: 1,
        internal: true,
        name: null,
        username: null,
        sex: null,
        birthday: null,
        workUnit: null,
        idCard: null,
        academicDegree: null,
        professionalTitle: null,
        currentProfessional: null,
        division: null,
        studyTime: null,
        signature: null,
      })
    }
  }

  const initPerformanceGoals = async () => {
    const { data } = await queryRmsProjectPerformanceGoalsByProjectId({ projectId: props.projectId ?? 0 })
    if (data.length > 0) {
      saveForm.value.rmsProjectPerformanceGoalData = data
      transPerformanceGoals(data)
    }
  }

  onMounted(() => {
    init()
    initOptions()
  })

  watch(
    () => props.projectId,
    () => {
      console.log('projectId change', props.projectId)
      saveForm.value.id = props.projectId
      if (props.projectId) {
        detailsInit()
      } else {
        initPerformanceGoals()
        initResearchIndexData()
        initFundsExpenseTree()
        // initEmpNum()
        console.log('start init declare unit info')
        initDeclareInfo()
        initResearchers()
      }
    },
    {
      immediate: true,
    }
  )

  watch(
    () => saveForm.value.researchersData.length,
    (newVal, oldVal) => {
      saveForm.value.projectOrgStructure.projectTeamCount = newVal
      saveForm.value.projectOrgStructure.proOtherCt =
        newVal -
        ifNull(saveForm.value.projectOrgStructure.proSeniorCt, 0) -
        ifNull(saveForm.value.projectOrgStructure.proMiddleCt, 0) -
        ifNull(saveForm.value.projectOrgStructure.proJuniorCt, 0)
    },
    { immediate: true }
  )

  watchEffect(() => {
    saveForm.value.projectOrgStructure.prPhone = saveForm.value.telephone
  })

  // watch(
  //   [() => saveForm.value.projectLeader, () => saveForm.value.telephone, () => saveForm.value.researchersData],
  //   (newValue, oldValue) => {
  //     const [leader, telephone, researchers] = newValue
  //     if (leader !== saveForm.value.projectOrgStructure.projectResp) {
  //       saveForm.value.projectOrgStructure.projectResp = leader
  //     }
  //     if (telephone !== saveForm.value.projectOrgStructure.prPhone) {
  //       saveForm.value.projectOrgStructure.prPhone = telephone
  //     }
  //     const leaderInfo = researchers[0]
  //     if (leaderInfo) {
  //       const { sex, birthday, academicDegree, professionalTitle, currentProfessional } = leaderInfo
  //       if (sex !== saveForm.value.projectOrgStructure.prSex) {
  //         saveForm.value.projectOrgStructure.prSex = sex
  //       }
  //       if (birthday !== saveForm.value.projectOrgStructure.prBirthday) {
  //         saveForm.value.projectOrgStructure.prBirthday = birthday
  //       }
  //       if (academicDegree !== saveForm.value.projectOrgStructure.prAcademic) {
  //         saveForm.value.projectOrgStructure.prAcademic = academicDegree
  //       }
  //       if (professionalTitle !== saveForm.value.projectOrgStructure.prTitle) {
  //         saveForm.value.projectOrgStructure.prTitle = professionalTitle
  //       }
  //       if (currentProfessional !== saveForm.value.projectOrgStructure.prProfession) {
  //         saveForm.value.projectOrgStructure.prProfession = currentProfessional
  //       }
  //       saveForm.value.projectOrgStructure.projectTeamCount = researchers.length
  //       // saveForm.value.projectOrgStructure.proSeniorCt = researchers.filter(item => item.professionalTitle === '高级').length
  //       // saveForm.value.projectOrgStructure.proMiddleCt = researchers.filter(item => item.professionalTitle === '中级').length
  //       // saveForm.value.projectOrgStructure.proJuniorCt = researchers.filter(item => item.professionalTitle === '初级').length
  //       saveForm.value.projectOrgStructure.proOtherCt =
  //         researchers.length -
  //         ifNull(saveForm.value.projectOrgStructure.proSeniorCt, 0) -
  //         ifNull(saveForm.value.projectOrgStructure.proMiddleCt, 0) -
  //         ifNull(saveForm.value.projectOrgStructure.proJuniorCt, 0)
  //     }
  //   },
  //   {
  //     deep: true,
  //   }
  // )

  defineExpose({
    validateForm,
    handleSubmitBrief,
    saveDraft,
    saveForm,
  })
  //tmd 什么傻逼需求，tmd直接上传项目资料，简单填个表就行了嘛，搞你妈的照着word上面实现个一模一样的？人家用户都不一定想用这逼玩意儿 瞎勾八搞，一个人搞一个系统，还瞎几把增加工作量

  // 监听表单数据变化，更新导航栏状态
  watch(
    () => saveForm.value,
    () => {
      // 导航栏状态会通过 anchorList 计算属性自动更新
    },
    { deep: true }
  )

  const printResearchers = () => {
    // 创建打印内容
    const printWindow = window.open('', '_blank')

    if (!printWindow) {
      window.$message.error('打印窗口被阻止，请允许弹出窗口')
      return
    }

    // 生成打印样式和内容
    const title = saveForm.value.projectName || '课题组主要人员情况表'

    // 添加表格样式
    const style = `
      <style>
        body { font-family: SimSun, sans-serif; padding: 20px; }
        h2 { text-align: center; margin-bottom: 20px; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid black; padding: 8px; text-align: center; }
        th { background-color: #f2f2f2; }
        .signature-cell { height: 60px; }
        @media print {
          .no-print { display: none; }
          button { display: none; }
        }
      </style>
    `

    // 构建表头
    let tableHtml = `
      <table>
        <thead>
          <tr>
            <th>序号</th>
            <th>姓名</th>
            <th>性别</th>
            <th>出生年月</th>
            <th>工作单位</th>
            <th>身份证号</th>
            <th>学位</th>
            <th>职称</th>
            <th>现从事专业</th>
            <th>课题中的分工</th>
            <th>研究时间</th>
            <th>签字确认</th>
          </tr>
        </thead>
        <tbody>
    `

    // 添加表格数据
    saveForm.value.researchersData.forEach((item, index) => {
      tableHtml += `
        <tr>
          <td>${index + 1}</td>
          <td>${item.name || ''}</td>
          <td>${item.sex === '1' ? '男' : item.sex === '0' ? '女' : ''}</td>
          <td>${item.birthday || ''}</td>
          <td>${item.workUnit || ''}</td>
          <td>${item.idCard || ''}</td>
          <td>${item.academicDegree || ''}</td>
          <td>${item.professionalTitle || ''}</td>
          <td>${item.currentProfessional || ''}</td>
          <td>${item.division || ''}</td>
          <td>${item.studyTime || ''}</td>
          <td class="signature-cell"></td>
        </tr>
      `
    })

    // 关闭表格
    tableHtml += `
        </tbody>
      </table>
    `

    // 添加打印按钮
    const printButton = `
      <div class="no-print" style="text-align: center; margin-top: 20px;">
        <button onclick="window.print()">打印</button>
        <button onclick="window.close()">关闭</button>
      </div>
    `

    // 组合完整HTML
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>${title} - 课题组主要人员情况表</title>
        ${style}
      </head>
      <body>
        <h2>课题组主要人员情况表</h2>
        <p style="text-align: center; margin-bottom: 15px;">项目名称：${saveForm.value.projectName || ''}</p>
        <p style="text-align: right; margin-bottom: 15px;">打印日期：${new Date().toLocaleDateString()}</p>
        <p><span style="color: red;">注意：该顺序与职称评审相关，请谨慎填报！</span></p>
        ${tableHtml}
        <p style="margin-top: 20px; text-align: right;">课题负责人签字：______________ 日期：______________</p>
        <p style="margin-top: 10px;"><strong>说明：</strong>请各课题组成员在签字确认栏签字确认，签字后请将此表作为附件上传到系统中。</p>
        ${printButton}
      </body>
      </html>
    `

    // 写入HTML到新窗口
    printWindow.document.open()
    printWindow.document.write(html)
    printWindow.document.close()

    // 等待图片加载完成后自动打印
    printWindow.onload = function () {
      // 短暂延迟确保样式加载完成
      setTimeout(() => {
        printWindow.focus()
        // 如果需要自动打印，取消下面的注释
        // printWindow.print()
      }, 300)
    }
  }
</script>
<style scoped>
  .description {
    padding: 20px;
    margin-bottom: 20px;
    background-color: #f9f9f9;
    border-radius: 4px;
  }

  .cover {
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 4px;
  }

  .declaration-part {
    margin-bottom: 20px;
    padding-bottom: 10%;
  }

  .declaration-part-title {
    font-size: 20px;
    font-weight: bold;
  }

  .declaration-part-subtitle {
    font-size: 14px;
    color: #666;
    margin-top: 10px;
    padding-top: 18px;
  }

  .enlarged {
    font-size: 16px;
  }

  .widened {
    width: 200px;
  }

  .mt-4 {
    margin-top: 16px;
  }

  .mt-8 {
    margin-top: 32px;
  }

  /* 导航栏美化样式 */
  :deep(.filled-item) {
    position: relative;
  }

  :deep(.filled-item)::after {
    content: '✓';
    position: absolute;
    right: 8px;
    color: #18a058;
    font-weight: bold;
  }

  :deep(.filled-item .n-anchor-link__title) {
    color: #18a058 !important;
    font-weight: bold;
  }

  :deep(.promise-item .n-anchor-link__title) {
    color: #2080f0 !important;
    font-weight: bold;
  }

  :deep(.red-text .n-anchor-link__title) {
    color: red !important;
    font-weight: bold;
  }

  :deep(.n-anchor-link:hover .n-anchor-link__title) {
    color: #36ad6a !important;
  }

  :deep(.n-anchor-link.active .n-anchor-link__title) {
    color: #18a058 !important;
    font-weight: bold;
  }

  :deep(.n-anchor-link.active) {
    background-color: rgba(24, 160, 88, 0.1);
  }

  /* 添加未填写项的样式 */
  :deep(.unfilled-item) {
    position: relative;
  }

  :deep(.unfilled-item)::after {
    content: '!';
    position: absolute;
    right: 8px;
    color: #f0a020;
    font-weight: bold;
  }

  :deep(.unfilled-item .n-anchor-link__title) {
    color: #f0a020 !important;
  }

  /* 添加必填项的样式 */
  :deep(.required-item) {
    position: relative;
  }

  :deep(.required-item)::after {
    content: '*';
    position: absolute;
    right: 8px;
    color: #d03050;
    font-weight: bold;
  }

  :deep(.required-item .n-anchor-link__title) {
    color: #d03050 !important;
  }

  /* 添加导航栏项目的悬停效果 */
  :deep(.n-anchor-link) {
    transition: all 0.3s ease;
    border-radius: 4px;
    margin-bottom: 2px;
  }

  :deep(.n-anchor-link:hover) {
    background-color: rgba(24, 160, 88, 0.05);
  }

  /* 添加子项的缩进和样式 */
  :deep(.n-anchor-link .n-anchor-link) {
    padding-left: 12px;
    border-left: 1px solid #eee;
  }

  :deep(.n-anchor-link .n-anchor-link:hover) {
    border-left: 1px solid #18a058;
  }

  /* 添加已填写项的动画效果 */
  @keyframes pulse {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
    100% {
      transform: scale(1);
    }
  }

  :deep(.filled-item.active) {
    animation: pulse 0.5s ease;
  }

  .red-text {
    color: red !important;
    font-weight: bold;
  }

  .nInput {
    border: none; /* 移除默认边框 */
    border-bottom: 1px solid #a1a1a1; /* 设置浅灰色下划线 */
    width: 100%; /* 设置输入框的宽度为100% */
    height: 40px;
    padding: 0; /* 移除内边距 */
    box-sizing: border-box; /* 包含padding和border在内的总宽度和高度 */
    display: inline-block; /* 使输入框在同一行显示 */
    text-align: center;
  }

  .nInputInText {
    border: none; /* 移除默认边框 */
    border-bottom: 1px solid #a1a1a1; /* 设置浅灰色下划线 */
    width: 50px; /* 设置输入框的长度为50px */
    height: 20px;
    padding: 0; /* 移除内边距 */
    box-sizing: border-box; /* 包含padding和border在内的总宽度和高度 */
    display: inline-block; /* 使输入框在同一行显示 */
    text-align: center;
  }

  .nInput:focus {
    border-bottom-color: #90ee90; /* 焦点时设置浅绿色下边框 */
    box-shadow: 0 2px 0px rgba(144, 238, 144, 0.5); /* 焦点时仅在下边框添加浅绿色阴影 */
    outline: none; /* 移除聚焦时的默认轮廓线 */
  }

  .nInputInText:focus {
    border-bottom-color: #90ee90; /* 焦点时设置浅绿色下边框 */
    box-shadow: 0 2px 0px rgba(144, 238, 144, 0.5); /* 焦点时仅在下边框添加浅绿色阴影 */
    outline: none; /* 移除聚焦时的默认轮廓线 */
  }

  .n-checkbox-box__border {
    --n-size: 26px;
  }

  .n-checkbox-box >>> .n-checkbox-box__border .path {
    --n-size: 26px !important;
  }

  :deep(.widened) {
    width: 100%;
  }

  :deep(.enlarged) {
    zoom: 120%;
  }

  .enlarged-font-size {
    font-size: 18px;
  }

  :deep(.descriptions-label) {
    width: 15%;
    font-size: 18px;
  }

  :deep(.descriptions-label-required-asterisk)::after {
    content: ' *';
    color: red;
  }

  .descriptions-rich-text {
    width: 100%;
  }

  /* 解决侧边栏偶现横向滚动条的问题 */
  :deep(.n-anchor) {
    width: 99%;
  }

  /* 隐藏数字输入框的上下箭头 */
  input[type='number']::-webkit-outer-spin-button,
  input[type='number']::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  /* Firefox */
  input[type='number'] {
    -moz-appearance: textfield;
  }
</style>
