<template>
  <!-- 科研预算展示 -->
  <n-row :gutter="20">
    <n-col :span="24">
      <j-title-line title="预算控制"/>
      <j-n-data-table :columns="budgetColumns" :data="budgetData" min-height="100px"/>
    </n-col>
  </n-row>
</template>
<script lang="ts" setup>
import {CRUDColumnInterface} from "@/types/comps/crud.ts";
import {h, ref} from "vue";
import {propTypes} from "@/utils/bpmAdapter/propTypes.ts";
import {NTag} from "naive-ui";
import {queryEcsResearchFundingTask, queryResearchFundingBudget} from "@/api/ecs/reimMgt/researchFundingTask.ts";

const props = defineProps({
  projectId:propTypes.number.def(undefined),
  sum: {
    type: Number,
    required: true,
  }
})

let budgetData = ref<BudgetItem[]>([])
const allBudgetData = ref([])           //所有项目预算信息
let projectId = ref()                   //项目id

const budgetColumns = ref<CRUDColumnInterface[]>([
  {
    title: '项目名称',
    key: 'projectName',
    width: 100,
  },
  {
    title: '报销金额',
    key: 'reimAmt',
    width: 100,
  },
  {
    title: '已报销金额',
    key: 'alreadyUsedAmt',
    width: 100,
  },
  {
    title: '项目预算金额',
    key: 'budgetAmt',
    width: 100,
  },
  {
    title: '提醒',
    key: 'state',
    width: 100,
    align: 'center',
    render: (row: any) => {
      if (row.state) {
        switch (row.state) {
          case '1':
            return h(NTag, { type: 'success', size: 'small' }, () => '预算足够')
          case '2':
            return h(NTag, { type: 'error', size: 'small' }, () => '预算不足')
        }
      }
      return ''
    },
  },
])

onMounted(() => {
})

const apportionAmt = (projectId: any) => {
  let budgetOrg = allBudgetData.value.filter(e => e.projectId == projectId)
  budgetOrg.forEach(bo => {
    bo.reimAmt = props.sum
    if (parseFloat(bo.reimAmt + '') + parseFloat(bo.alreadyUsedAmt + '') < parseFloat(bo.budgetAmt + '')) {
      // 预算够用
      bo.state = '1'
    } else {
      // 预算不够
      bo.state = '2'
    }
  })
  budgetData.value = budgetOrg
}

watch(
    () => props.sum,
    sum => {
      //重新计算金额
      apportionAmt(projectId.value)
    }
)

watch(
    () => props.projectId,
    proId => {
      //查询科研报销任务task
      debugger
      queryEcsResearchFundingTask({id: props.projectId}).then(res => {
        if (res.code == 200) {
          projectId.value = res.data[0].projectId
          //查询科研项目已报销
          queryResearchFundingBudget({}).then((res1: any) => {
            if(res1.code == 200) {
              console.log('查询科研预算成功')
              allBudgetData.value = res1.data
              apportionAmt(projectId.value)
            }
          })
        }

      })
    }
)

</script>
<script lang="ts">
export default {
  name: '科研预算展示'
}
</script>