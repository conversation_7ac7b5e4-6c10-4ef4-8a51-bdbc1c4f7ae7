<template>
  <j-crud
    :queryMethod="queryMethodCpd"
    :queryForm="queryForm"
    :tabs="tabs"
    default-check-tab="0"
    name="采购报销"
    ref="crudRef"
    showAddButton
    :paging="paging"
    v-model:checked-row-keys="checkRowKeys"
    :show-operation-button="false"
    :default-expand-all="defaultExpandAll"
  >
    <template #extendButtons>
      <n-button type="info" @click="purcReim">报销</n-button>
      <n-button type="warning" @click="purcReimLow">不足100金额报销</n-button>
      <n-button type="success" @click="purcUploadPayReceipt">上传付款文件</n-button>
    </template>
    <template #extendFormItems>
      <n-form-item label="项目名称" path="itemName">
        <n-select
          :options="itemNameOptions"
          multiple
          filterable
          clearable
          :max-tag-count="1"
          @update:value="handleUpdateValue"
        />
      </n-form-item>
      <n-form-item label="报销标识" path="id">
        <n-input type="text" v-model:value="queryForm.id" clearable />
      </n-form-item>
      <n-form-item label="业务状态" path="busstas">
        <n-select v-model:value="queryForm.busstas" :options="busstasOptions" clearable />
      </n-form-item>
      <n-form-item label="采购类型" path="type">
        <n-select v-model:value="queryForm.type" :options="procurOptions" clearable />
      </n-form-item>
    </template>
    <template #content>
      <!-- 流程详情弹窗 -->
      <ProcessInstanceDetailModal :processInstanceId="currentProcessInstanceId" v-model:show="processDetailVisible" />
      <j-bus-audit-progress ref="auditProgressRef" />
      <j-modal
        v-model:show="showDetail"
        :show-btn="!view"
        width="95%"
        height="95%"
        :content-style="{ height: 'calc(100% - 85px - 56px)' }"
        :btn-disabled="showInvoIdentify"
        :btn-loading="reimLoading"
        @confirm="ocr"
      >
        <template #header>
          <div style="text-align: center">{{ detailTitle }}</div>
        </template>
        <template #header-extra>
          <n-button type="info" @click="printMyObj" v-if="view" style="margin-left: 10px">打印</n-button>
        </template>
        <n-scrollbar style="height: 100%">
          <n-space vertical>
            <n-spin :show="showInvoIdentify">
              <ReimDetail
                v-if="isOldReim"
                ref="reimDetailRef"
                :details="details"
                :view="view"
                :type="curReim"
                :apprFlag="apprFlag"
                :addressData="codeData"
                :show-audit-steps="view"
                :is-audit="false"
                :reimLastNoShow="true"
                :resubmit="resubmit"
                @reimLoading="btnLoadingChange"
                @close="closeDetail"
                @closeInvo="showInvoIdentify = false"
                @openInvo="showInvoIdentify = true"
              />
              <NewReimDetail
                v-else
                ref="reimDetailRef"
                :id="id"
                :details="details"
                :view="view"
                :type="curReim"
                :apprFlag="apprFlag"
                :addressData="codeData"
                :show-audit-steps="view"
                :is-audit="false"
                :reimLastNoShow="true"
                :resubmit="resubmit"
                @reimLoading="btnLoadingChange"
                @close="closeDetail"
                @closeInvo="showInvoIdentify = false"
                @openInvo="showInvoIdentify = true"
              />
            </n-spin>
          </n-space>
        </n-scrollbar>
      </j-modal>

      <!-- 采购详情 -->
      <ProcessInstanceDetailModal
        :processInstanceId="processInstanceCode"
        :other-props="detailOtherProps"
        v-model:show="showPurcDetail"
        :z-index="10"
      />

      <j-modal
        v-model:show="showRcptUpdModal"
        title="上传付款证明文件"
        height="60%"
        width="55%"
        :contentStyle="{ height: '100%' }"
        @confirm="doUpdRcpt"
      >
        <n-form ref="purcPayRef" :model="purcPayForm" label-placement="top" label-width="auto">
          <n-form-item label="支付类型:" path="payMethod">
            <n-select v-model:value="purcPayForm.payMethod" :options="payMethodOptions"></n-select>
          </n-form-item>
        </n-form>
        <n-upload
          :max="100"
          :file-list="rcptFileList"
          multiple
          @update:file-list="rcptFileChange"
          accept=".png,.jpg,.jpeg"
        >
          <n-upload-dragger>
            <div style="margin-bottom: 12px">
              <n-icon size="48" :depth="3">
                <archive-icon />
              </n-icon>
            </div>
            <n-text style="font-size: 16px"> 点击或者拖动文件到该区域来上传 </n-text>
            <n-p depth="3" style="margin: 8px 0 0 0"> 只能上传pdf,png,jpg,jpeg文件格式数据 </n-p>
          </n-upload-dragger>
        </n-upload>
      </j-modal>
    </template>
  </j-crud>
</template>

<script lang="ts" setup>
  import { computed, h, onMounted, ref, VNode } from 'vue'
  import { NPopconfirm, NTag, SelectOption, UploadFileInfo } from 'naive-ui'
  import { queryDictData } from '@/api/hrm/dictManage/treeSelectDict'
  import { ArchiveOutline as ArchiveIcon } from '@vicons/ionicons5'
  import {
    cancelEcsReimDetailNew,
    deleteNoAudit,
    deleteNoAuditNew,
    purcUploadReceipt,
    queryEcsReimDetail,
    queryEcsReimDetailNew,
    queryEcsReimDetailNoPageNew,
    queryItemDetail,
  } from '@/api/ecs/reimMgt/reimDetail'
  import { ContainerValueType, CRUDColumnInterface, JTab, Step } from '@jtypes'
  import JPGlobal from '@jutil'
  import { useRouter } from 'vue-router'
  import ReimDetail from '@/views/modules/ecs/reimMgt/expenseReim/components/reimDetail.vue'
  import NewReimDetail from '@/views/modules/ecs/reimNew/expenseReim/components/reimDetail.vue'
  import { queryEcsReimPurcTask, queryEcsReimPurcTaskDetail } from '@/api/ecs/reimMgt/reimPurcTask'
  import { queryPurcProcessInstanceCode } from '@/api/purms/applyMgt/purcReq'
  import ProcessInstanceDetailModal from '@/views/modules/bpm/processInstance/detail/processDetailModal.vue'
  import { queryEmpPurcPaymentInfo } from '@/api/purms/applyMgt/purcReq'
  import * as TaskApi from '@/api/bpm/task'

  onMounted(() => {
    queryDictData({ codeType: 'REGION' }).then(res => {
      codeData.value = res.data
    })
  })

  const style = JPGlobal.linkStyle

  // refs
  const reimDetailRef = ref()
  const crudRef = ref()
  const auditProgressRef = ref()

  // data
  const router = useRouter()
  let showDetail = ref(false)
  let id = ref() //报销id
  let view = ref(false)
  let showInvoIdentify = ref(false)
  let apprFlag = ref(false)
  let curReim = ref('8')
  let tabType = ref('0') //tab类型
  let reimLoading = ref(false)
  let auditProgressSteps = ref<Step[]>([])
  let detailTitle = ref('采购报销审批表') //modal弹框标题
  let checkRowKeys = ref([]) //选中行
  let paging = ref(false)
  const currentProcessInstanceId = ref()
  const processDetailVisible = ref()
  let resubmit = ref(false) //是否重新编辑
  let showRcptUpdModal = ref(false) //展示上传付款单界面
  let isOldReim = ref(false) //显示报销界面的版本  新/旧
  let rcptFileList = ref<Array<UploadFileInfo>>([]) //付款证明文件
  let purcPayForm = ref({
    payMethod: '0', //付款方式 0：非现金 1：现金 2:复明工程 可自行拓展
  })
  let queryForm = ref({
    type: '8',
    reimFlag: '0',
    itemName: '',
    reimTaskType: '1'
  })
  let details = ref({
    formData: {
      purcDetailIds: [],
      paymentInfo: [],
    },
    itemDetails: [],
    subsItemDetails: [],
    psnDetails: [],
    reimAsstDetails: [],
    fileRecords: [],
  })
  let codeData = ref([])
  let itemNameOptions = computed(() => {
    const uniqueItemName = new Set()
    let options = []
    if (crudRef.value) {
      crudRef.value.originData.forEach(e => {
        if (e.children) {
          e.children.forEach(ec => {
            if (ec['itemName'] && !uniqueItemName.has(ec['itemName'])) {
              uniqueItemName.add(ec['itemName'])
              options.push({
                label: ec['itemName'],
                value: ec['itemName'],
              })
            }
          })
        }
      })
    }
    return options
  })

  // 监听采购类型变更
  watch(() => queryForm.value.type, (newVal) => {
    if (newVal === '8') {
      queryForm.value.reimTaskType = '1' // 零星采购
    } else if (newVal === '10') {
      queryForm.value.reimTaskType = '3' // 物资采购
    }
  }, { immediate: true })

  let queryMethodCpd = computed(() => {
    return tabType.value == '8' ? queryEcsReimDetailNoPageNew : queryEcsReimPurcTask
  })

  // ocr
  const ocr = () => {
    reimDetailRef.value.submit()
  }

  const handleUpdateValue = (value: any, option: SelectOption) => {
    queryForm.value.itemName = value.join('')
  }

  //付款证明文件改变触发
  const rcptFileChange = (files: Array<UploadFileInfo>) => {
    rcptFileList.value = files
  }

  const payMethodOptions = ref([
    {
      label: '现金支付',
      value: '1',
    },
    {
      label: '非现金支付',
      value: '0',
    },
    {
      label: '复明工程',
      value: '2',
    },
  ])

  const showPurcDetail = ref(false)
  // 采购实例id
  const processInstanceCode = ref('')
  //详情其他参数
  const detailOtherProps = {
    isEdit: false,
    isReq: false,
    isExe: false,
    isCfm: true,
    isMmis: false,
  }


  const procurOptions = ref([
    {
      label: '零星采购',
      value: '8',
    },
    {
      label: '物资采购',
      value: '10',
    }
  ])

  const busstasOptions = ref([
    {
      label: '已付款',
      value: '1',
    },
    {
      label: '未提交',
      value: '2',
    },
    {
      label: '审核中',
      value: '3',
    },
    {
      label: '审批驳回',
      value: '4',
    },
    {
      label: '审批取消',
      value: '5',
    },
    {
      label: '审批通过',
      value: '6',
    },
  ])

  // 获取列
  const getColumns = (name: string): CRUDColumnInterface[] => {
    // 待报销
    if (name == '0' || name == '1') {
      return [
        {
          title: '#',
          key: 'selection',
          type: ContainerValueType.SELECTION,
          show: false,
          disabled: (row: any) => row.reimFlag == '1',
        },
        ...columns,
        {
          title: '操作',
          key: 'operation',
          width: 100,
          fixed: 'right',
          align: 'center',
          render: (row: any) => {
            return row.itemNo
              ? h(
                  'span',
                  {
                    style: { marginRight: '10px', ...style },
                    onClick: async () => {
                      queryPurcProcessInstanceCode(row.itemNo).then((res: any) => {
                        processInstanceCode.value = res.data
                        showPurcDetail.value = true
                      })
                    },
                  },
                  '采购详情'
                )
              : ''
          },
        },
      ]
    } else {
      return [
        {
          title: '#',
          key: 'selection',
          type: ContainerValueType.SELECTION,
        },
        { title: '报销标识', key: 'id', width: 100 },
        { title: '报销科室', key: 'appyerDeptName', width: 100 },
        { title: '报销人', key: 'appyerName', width: 100 },
        { title: '报销时间', key: 'appyerTime', width: 160 },
        {
          title: '报销类型',
          key: 'type',
          width: 100,
          render: (row: any) => {
            if (row.type) {
              switch (row.type) {
                case '8':
                  return h(NTag, { type: 'success', size: 'small' }, () => '零星采购')
                case '10':
                  return h(NTag, { type: 'info', size: 'small' }, () => '物资采购')
                default:
                  return ''
              }
            }
            return ''
          },
        },
        { title: '备注信息', key: 'evectionRea', width: 150 },
        { title: '报销金额', key: 'sum', width: 100 },
        {
          title: '业务状态',
          key: 'busstas',
          width: 100,
          render: (row: any) => {
            switch (row.busstas) {
              case '1':
                return h(NTag, { type: 'success', size: 'small' }, () => '已付款')
              case '2':
                return h(NTag, { type: 'info', size: 'small' }, () => '未提交')
              case '3':
                return h(NTag, { type: 'warning', size: 'small' }, () => '审核中')
              case '4':
                return h(NTag, { type: 'error', size: 'small' }, () => '审批驳回')
              case '5':
                return h(NTag, { type: 'info', size: 'small' }, () => '审批取消')
              case '6':
                return h(NTag, { type: 'success', size: 'small' }, () => '审批通过')
              default:
                return ''
            }
          },
        },
        {
          title: '审核状态',
          key: 'auditState',
          width: 100,
          render: (row: any) => {
            if (row.auditState) {
              switch (row.auditState) {
                case '1':
                  return h(NTag, { type: 'info', size: 'small' }, () => '审核中')
                case '2':
                  return h(NTag, { type: 'success', size: 'small' }, () => '审核成功')
                case '3':
                  return h(NTag, { type: 'error', size: 'small' }, () => '审核失败')
                default:
                  break
              }
            }
            return ''
          },
        },
        {
          title: '操作',
          key: 'operation',
          width: 100,
          fixed: 'right',
          align: 'center',
          render: (row: any) => {
            let children = [
              h(
                'span',
                {
                  style: { ...style, marginRight: '10px' },
                  onClick: () => {
                    if (row.processInstanceId) {
                      processDetailVisible.value = true
                      currentProcessInstanceId.value = row.processInstanceId
                    } else {
                      window.$message.warning('当前未处于审核状态')
                      return
                    }
                  },
                },
                '报销进度'
              ),
              h(
                'span',
                {
                  style: { ...style },
                  onClick: () => {
                    // 报销详情
                    //查询报销明细
                    // 查询项目和补助项目
                    id.value = row.id
                    view.value = true
                    resubmit.value = false
                    apprFlag.value = false
                    showDetail.value = true
                    console.log('@@-id', id.value)
                    /*queryItemDetail({ id: row.id, attCode: row.attCode }).then((res: any) => {
                      details.value.formData = row
                      details.value.itemDetails = res.data.itemDetails
                      details.value.subsItemDetails = res.data.subsItemDetails
                      details.value.psnDetails = res.data.psnDetails
                      details.value.reimAsstDetails = res.data.reimAsstDetails
                      details.value.fileRecords = res.data.fileRecords
                      detailTitle.value = '采购报销审批表'
                      view.value = true
                      apprFlag.value = false
                      resubmit.value = false
                      showDetail.value = true
                    })*/
                  },
                },
                '详情'
              ),
              h(
                NPopconfirm,
                {
                  positiveButtonProps: {
                    type: 'error',
                    secondary: true,
                  },
                  onPositiveClick: async (e: Event) => {
                    e.stopPropagation()
                    let reimStatus
                    if (row.busstas == '4' || row.busstas == '5' || row.auditState == '3') {
                      //审批驳回状态,直接删除
                      reimStatus = '4'
                    } else if (row.busstas == '2') {
                      //未提交状态,只删除记录
                      reimStatus = '2'
                    } else {
                      reimStatus = '3'
                      //查询当前流程实例的任务数量是否大于1， 是则说明已经审核了
                      const data = await TaskApi.getTaskListByProcessInstanceId(row.processInstanceId)
                      if (data.length > 1) {
                        window.$message.error('当前申请已经审核过，不能删除')
                        return
                      }
                    }
                    //删除待报销
                    deleteNoAuditNew({
                      id: row.id,
                      processInstanceId: row.processInstanceId,
                      reimStatus: reimStatus,
                    }).then((res: any) => {
                      if (res.code == 200) {
                        window.$message.success('删除成功')
                        crudRef.value.queryData()
                      }
                    })
                  },
                  onNegativeClick: (e: Event) => {
                    e.stopPropagation()
                  },
                },
                {
                  trigger: () =>
                    h(
                      'span',
                      {
                        style: { ...style, marginLeft: '10px' },
                      },
                      '删除'
                    ),
                  default: () => '是否删除?',
                }
              ),
            ]

            if (row.busstas == '2' || row.busstas == '4' || row.busstas == '5' || row.auditState == '3') {
              children.push(
                h(
                  'span',
                  {
                    style: { ...style, marginLeft: '10px' },
                    onClick: () => {
                      //重新编辑
                      id.value = row.id
                      resubmit.value = true
                      view.value = false
                      apprFlag.value = false
                      details.value.formData = row
                      detailTitle.value = '采购报销审批表'
                      showDetail.value = true
                    },
                  },
                  '重新编辑'
                )
              )
              /*children.push(
                h(
                    'span',
                    {
                      style: { ...style, marginLeft: '10px' },
                      onClick: () => {
                        //重新编辑
                        // 查询项目和补助项目
                        queryItemDetail({ id: row.id, attCode: row.attCode }).then((res: any) => {
                          details.value.formData = row
                          details.value.itemDetails = res.data.itemDetails
                          details.value.subsItemDetails = res.data.subsItemDetails
                          details.value.psnDetails = res.data.psnDetails
                          details.value.reimAsstDetails = res.data.reimAsstDetails
                          details.value.fileRecords = res.data.fileRecords
                          detailTitle.value = '采购报销审批表'
                          curReim.value = row.type
                          apprFlag.value = false
                          resubmit.value = true
                          view.value = false
                          showDetail.value = true
                        })
                      },
                    },
                    () => '重新编辑'
                )
            )*/
            }
            //审核中的才能取消
            if (row.busstas == '3' || row.auditState == '1') {
              children.push(
                h(
                  NPopconfirm,
                  {
                    positiveButtonProps: {
                      type: 'error',
                      secondary: true,
                    },
                    onPositiveClick: async (e: Event) => {
                      e.stopPropagation()
                      cancelEcsReimDetailNew({ id: row.id, processInstanceId: row.processInstanceId }).then(res => {
                        if (res.code == 200) {
                          window.$message.success('取消成功')
                          crudRef.value.queryData()
                        }
                      })
                    },
                    onNegativeClick: (e: Event) => {
                      e.stopPropagation()
                    },
                  },
                  {
                    trigger: () =>
                      h(
                        'span',
                        {
                          style: { ...style, marginLeft: '10px' },
                        },
                        '取消'
                      ),
                    default: () => '是否取消?',
                  }
                )
              )
            }
            return h('div', children)
          },
        },
      ]
    }
  }

  const columns: CRUDColumnInterface[] = [
    {
      title: '项目名称',
      key: 'itemName',
      width: 200,
      render: (row: any) => {
        return h('span', { style: { fontWeight: row.children ? 'bold' : 'normal' } }, row.itemName || row.reimDesc)
      },
    },
    {
      title: '采购类型',
      key: 'reimTaskType',
      width: 100,
      render: (row: any) => {
        if (row.reimTaskType) {
          switch (row.reimTaskType) {
            case '1':
              return h(NTag, { type: 'success', size: 'small' }, () => '零星采购')
            case '3':
              return h(NTag, { type: 'info', size: 'small' }, () => '物资采购')
            default:
              return ''
          }
        }
        return ''
      },
    },
    { title: '报销标识', key: 'reimId', width: 100 },
    {
      title: '单位',
      key: 'unt',
      width: 100,
    },
    {
      title: '数量',
      key: 'cnt',
      width: 100,
    },
    {
      title: '单价',
      key: 'price',
      width: 100,
    },
    {
      title: '金额(元)',
      key: 'sumamt',
      width: 100,
    },

    {
      title: '报销标志',
      key: 'reimFlag',
      width: 100,
      render: (row: any) => {
        if (row.reimFlag) {
          return h(NTag, { size: 'small', round: true, type: row.reimFlag == '1' ? 'success' : 'error' }, () =>
            row.reimFlag == '1' ? '已报销' : '待报销'
          )
        }
      },
    },
    {
      title: '执行科室',
      key: 'reimOrgName',
      width: 100,
    },
    {
      title: '执行人',
      key: 'exeEmpName',
      width: 100,
    },
    {
      title: '执行日期',
      key: 'exeDate',
      width: 100,
    },
    {
      title: '执行备注',
      key: 'reimDesc',
      width: 100,
    },
    {
      title: '签收科室',
      key: 'cfmOrgName',
      width: 100,
    },
  ]
  // 默认展开
  const defaultExpandAll = ref<boolean>(true)

  // tab改变
  const tabChange = (tab: JTab) => {
    tabType.value = tab.name
    // queryForm.value.reimFlag = tab.name == '0' ? '0' : '1'
    // 所有tab页签都不分页
    paging.value = false
    queryForm.value.reimFlag = tab.name
    defaultExpandAll.value = tab.name == '0' ? true : false
  }

  const btnLoadingChange = (state: boolean) => {
    reimLoading.value = state
  }

  const tabs = ref<JTab[]>([
    {
      name: '0',
      tab: '待报销任务',
      columns: getColumns('0'),
      tabChange: tabChange,
    },
    {
      name: '1',
      tab: '已报销任务',
      columns: getColumns('1'),
      tabChange: tabChange,
    },
    {
      name: '8',
      tab: '报销记录',
      columns: getColumns('8'),
      tabChange: tabChange,
    },
  ])

  // 关闭
  const closeDetail = () => {
    showDetail.value = false
    crudRef.value.queryData()
  }

  // 提交
  const submit = () => {
    reimDetailRef.value.submit()
  }

  const printMyObj = async () => {
    reimDetailRef.value.printDoc(curReim.value)
  }

  //报销
  const purcReim = async () => {
    let detailIds = checkRowKeys.value.filter(key => /^\d+$/.test(key)).map(Number)
    if (detailIds.length == 0) {
      window.$message.warning('请选择报销项')
      return
    }
    //1.查询对应明细详情
    const res = await queryEcsReimPurcTaskDetail({ ids: detailIds })
    details.value.formData.purcDetailIds = detailIds
    details.value.itemDetails = res.data
    //2.查询报销人付款信息
    const res2 = await queryEmpPurcPaymentInfo(res.data.map(item => item.itemNo))
    details.value.formData.paymentInfo = res2.data
    apprFlag.value = true
    resubmit.value = false
    view.value = false
    showDetail.value = true
  }

  //不足100 金额报销
  const purcReimLow = async () => {
    details.value.formData.purcDetailIds = []
    details.value.itemDetails = []
    details.value.formData.paymentInfo = []
    apprFlag.value = true
    resubmit.value = false
    view.value = false
    showDetail.value = true
  }

  const purcUploadPayReceipt = () => {
    //只能选择已报销的采购项目
    if (tabType.value != '8') {
      window.$message.warning('请选择已报销的项目')
      return
    }

    if (checkRowKeys.value.length == 0) {
      window.$message.warning('请选择报销项目')
      return
    }
    let valid = true
    //只能选择审批通过的采购报销
    crudRef.value.originData
      .filter(item => checkRowKeys.value.includes(item.id))
      .forEach(row => {
        if (row.busstas != '6') {
          valid = false
        }
      })
    if (!valid) {
      window.$message.warning('只能选择审批通过的采购项目')
      return
    }

    showRcptUpdModal.value = true
  }

  const doUpdRcpt = () => {
    let formData = new FormData()

    //添加上传付款单数据
    for (let key in purcPayForm.value) {
      if (purcPayForm.value[key]) {
        formData.append(key, purcPayForm.value[key])
      }
    }

    //添加报销id
    checkRowKeys.value.forEach((item, idx) => {
      formData.append('ids[' + idx + ']', item)
    })

    if (purcPayForm.value.payMethod == '0') {
      if (rcptFileList.value.length == 0) {
        window.$message.warning('当前未选择文件')
        return
      }
      //添加付款文件
      for (let i = 0; i < rcptFileList.value.length; i++) {
        formData.append('payRcptFiles[' + i + ']', rcptFileList.value[i].file)
      }
    }

    for (let [a, b] of formData.entries()) {
      console.log(a, b)
    }

    // return

    purcUploadReceipt(formData).then(res => {
      if (res.code == 200) {
        showRcptUpdModal.value = false
        rcptFileList.value = []
        crudRef.value.queryData()
      }
    })
  }
</script>

<script lang="ts">
  export default {
    name: '采购报销',
  }
</script>
<style scoped></style>
