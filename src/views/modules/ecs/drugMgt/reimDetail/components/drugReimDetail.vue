<template>
  <n-space vertical size="large">
    <n-layout has-sider>
      <n-layout-sider
          bordered
          width="30%"
      >
        <div class="expense-list">
          <div class="header">
            <n-space align="center"></n-space>
          </div>
          <n-list hoverable clickable>
            <n-list-item
                v-for="drugReim in drugReims"
                :key="drugReim.id"
                @click="onSelect(drugReim)"
            >
              <div class="expense-item">
                <div class="expense-title">
                  <n-checkbox v-model:checked="drugReim.checked" v-if="isAudit"/>
                  <n-text strong>{{drugReim.spler}}</n-text>
                </div>
                <div class="expense-meta">
                  <n-text depth="3">申请人: {{ drugReim.crterName}}</n-text>
                  <n-text type="primary">总金额: ¥{{ drugReim.sum }}</n-text>
                </div>
              </div>
            </n-list-item>
          </n-list>
        </div>
      </n-layout-sider>
      <n-layout-content>
        <div class="expense-details">
          <n-space vertical>
<!--            <div class="header">-->
<!--              <n-space align="center">-->
<!--                <n-h3>医疗报销明细</n-h3>-->
<!--              </n-space>-->
<!--            </div>-->
            <template v-if="drugReim.stoins.length > 0">
              <n-space vertical>
<!--                  <div class="header-info">-->
<!--                    <n-text strong>{{ drugReim.reimTitle }}</n-text>-->
<!--                    <n-text type="primary">总金额: ¥{{ drugReim.totalAmt }}</n-text>-->
<!--                  </div>-->
                <j-n-data-table :columns="drugReim.columns" :data="drugReim.stoins" />
              </n-space>
            </template>
            <template v-else>
              <n-card>
                <n-text depth="3">请选择左侧报销单查看详情</n-text>
              </n-card>
            </template>
          </n-space>
        </div>
      </n-layout-content>
    </n-layout>
    <n-space>
      <j-preview v-model:show="showPreview" :oss-path="ossInfo.path!" :oss-path-name="ossInfo.name!" :bucket="bucket" />
      <div v-if="isAudit && ossPayInfo.length > 0" style="font-size: 16px">
        <j-title-line title="付款证明文件" style="margin-top: 10px;" />
        <n-empty description="未上传附件" v-if="ossPayInfo.length == 0" />
        <div style="display: flex; flex-direction: row; flex-wrap: wrap">
          <a
              href="javascript:void(0)"
              style="color: #18a058; margin-right: 20px"
              v-for="(item, idx) in ossPayInfo"
              :key="idx"
              @click="itemClick(item)"
          >
            {{ item.name }}
          </a>
        </div>
      </div>
    </n-space>
  </n-space>
</template>

<script lang="ts" setup>
import {delDrugReimNoChoose, queryEcsDrugReims} from "@/api/ecs/drugMgt/drugReimDetai.ts";
import { h, onMounted, ref, watch } from 'vue'
import {NTag} from "naive-ui";
import {UploadPreview} from "@jcomponents";
import {propTypes} from "@/utils/bpmAdapter/propTypes.ts";

const props = defineProps({
  isAudit: {
    type: Boolean,
    required: true,
    default: true
  },
  // id: propTypes.number.def(undefined),
  //药品报销ids逗号拼接字符串     1,2,3
  id: {
    type: String,
  },
  runningTasks: {
    type: Array,
  },
  tasks: {
    type: Array,
  },
  processInstanceVariables: {
    type: Object,
  },
  setHandleAuditBefoerFn: {
    type: Function,
  },
})

let drugReims = ref([])           //药品报销信息
let ossPayInfo = ref<Array<any>>([]) //付款证明文件
let showPreview = ref(false)  //附件预览
let ossInfo = ref<{ path?:string;name?:string}>({}) //付款证明文件项
let bucket = ref()               //预览默认桶

const columns = [
  { title: '#', key: 'index', width: 50 ,
    render: (_,index) => {
      return `${index + 1}`
    }
  },
  { title: '入库单号', key: 'stoinNum', width: 100 },
  { title: '库房类型',key:'stoinType',width: 80,
    render: (row: any) => {
      switch(row.stoinType) {
        case '1099':
          return h(NTag, { type: 'info',},() => '中药库')
        case '1094':
          return h(NTag, { type: 'success',},() =>'西药库')
        case '1241':
          return h(NTag, { type: 'warning',},() =>'消毒用品库')
        default:
          return ''
      }
    }
  },
  { title: '入库日期', key: 'stoinDate', width: 100 },
  { title: '数量合计', key: 'totlcnt', width: 100 },
  { title: '零售金额', key: 'rtalAmt', width: 100 },
  { title: '进价金额', key: 'purcpricAmt', width: 100 },
  { title: '发票号', key: 'invono', width: 100,
    render: (row: any) => {
      return h(UploadPreview, {
        ossPath: row.att,
        ossName: row.attName,
        bucket: 'ecs',
        showUpload: false,
        isFile: false,
      })
    }
  },
]
let drugReim = ref({
  title: '',
  totalAmt: null,
  columns: columns,
  stoins: []
})              //药品报销对应入库单信息

const onSelect = (item: any) => {
  drugReim.value.title = item.spler
  drugReim.value.totalAmt = item.sum
  drugReim.value.stoins = item.stoins
}

onMounted(()=> {
  if (props.setHandleAuditBefoerFn) {
    props.setHandleAuditBefoerFn(auditConfirmBefore)
  }
})

const itemClick = (item: any) => {
  bucket.value = 'ecs'
  ossInfo.value.name = item.name
  ossInfo.value.path = item.path
  showPreview.value = true
}

const auditConfirmBefore = async (pass: boolean) => {
  if (!pass) {
    return true
  }
  //根据勾选的报销，更新对应报销的状态

  let unpassIds = drugReims.value.filter(item => !item.checked).map(item => item.id)
  //如果通过的报销都没有勾选，且同时为通过审核，则提示应不通过审核
  if (pass && (unpassIds.length == drugReims.value.length)) {
    window.$message.warning('审批通过，请至少勾选一条通过的报销')
    return false
  }

  let flag = false
  if (unpassIds.length == 0) {      //如果都通过
    flag = true
  } else {    //需要进行处理的报销id
    await delDrugReimNoChoose({ids: unpassIds}).then(res => {
      if (res.code == 200) {
        window.$message.success('更新成功');
        flag = true
      }
    })
  }

  // flag = false
  return flag
}

watch(()=> props.id,
    nVal => {
      debugger
      //查询当前审核批次下所有报销的信息
      if (nVal) {
        queryEcsDrugReims({drugIdsStr: nVal}).then((res: any) => {
          if (res.code == 200) {
            console.log('@@-drugReims',res.data)
            //默认全部勾选
            drugReims.value = res.data
            const seen = new Set()
            ossPayInfo.value = []
            drugReims.value.forEach(item => {
              item.checked = true
              if (item.att && item.attName) {
                let splitAtt = item.att.split(',')
                let splitAttName = item.attName.split(',')
                for (let i = 0; i < splitAtt.length; i++) {
                  if (!seen.has(splitAtt[i])) {
                    seen.add(splitAtt[i]);
                    ossPayInfo.value.push({
                      name: splitAttName[i],
                      path: splitAtt[i],
                    })
                  }
                }
              }
            })
          }
        })
      }
    },
    { immediate: true}
)
</script>
<script lang="ts">
export default {
  name: '药品报销明细'
}
</script>

<style scoped>
.expense-list {
  padding: 8px;
}

.header {
  margin-bottom: 16px;
}

.expense-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.expense-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.expense-meta {
  display: flex;
  justify-content: space-between;
  font-size: 0.9em;
  padding-left: 28px;
}

.selected-item {
  background-color: rgba(24, 160, 88, 0.1);
}

.expense-details {
  padding: 16px;
}

.header {
  margin-bottom: 16px;
}

.header-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}
</style>

