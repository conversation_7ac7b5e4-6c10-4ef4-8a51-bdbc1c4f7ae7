<template>
  <j-crud
      ref="crudRef"
      :queryMethod="queryErpVcrDetail"
      :columns="columns"
      :queryForm="queryForm"
      :show-add-button="false"
      :show-operation-button="false"
      name="凭证信息"
      showAddButton
  >
    <template #extendFormItems>
      <n-form-item label="凭证类型" path="type">
        <j-select
            :options="vcrType"
            v-model:value="queryForm.type"
            clearable
        />
      </n-form-item>
      <n-form-item label="凭证号id" path="idpzh">
        <n-input v-model:value="queryForm.idpzh" type="text" placeholder="请输入" clearable/>
      </n-form-item>
      <n-form-item label="会计期间" path="kjqj">
        <n-date-picker
            v-model:formatted-value="queryForm.kjqj"
            value-format="yyyyMM"
            type="month"
            clearable
        />
      </n-form-item>
    </template>
    <template #content>
      <j-modal
          v-model:show="showVcrMsg"
          :show-btn="false"
          width="90%"
          height="90%"
          :content-style="{ height: 'calc(100% - 85px - 56px)' }"
          title="凭证信息详情"
      >
        <n-scrollbar style="height: 100%">
          <VcrDetail
              ref="vcrDetailRef"
              gen-type="2"
              :sup-type="supType"
              :only-read="true"
              :details="vcrInfo"
          >
          </VcrDetail>
        </n-scrollbar>
      </j-modal>

      <!-- 修改凭证相关信息 -->
      <j-modal
          v-model:show="showVcrUpdModal"
          width="30%"
          height="30%"
          :content-style="{height: 'calc(100% - 85px - 56px)'}"
          title="修改凭证信息"
          @confirm="updVcrMsg"
      >
        <n-form :model="vcrMsgForm" label-placement="left" label-width="80" ref="formRef">
          <n-form-item label="id凭证号" path="idpzh">
            <n-input type="text" v-model:value="vcrMsgForm.idpzh" clearable disabled/>
          </n-form-item>
          <n-form-item label="凭证号" path="pzh">
            <n-input type="text" v-model:value="vcrMsgForm.pzh" clearable/>
          </n-form-item>
        </n-form>
      </j-modal>
    </template>
  </j-crud>
</template>
<script lang="ts" setup>
import {CRUDColumnInterface} from '@/types/comps/crud'
import {h, onMounted, ref, watch} from 'vue'
import {
  deleteErpVcrDetail,
  queryErpVcrDetail,
  queryVcrDetail,
  updErpVcrDetailPzh,
} from '@/api/erp/vcr/vcrBox/vcrDetail'
import {NButton, NPopconfirm, NPopover, NTag} from "naive-ui";
import {Icon as JIcon} from "@/types/common/jcomponents";
import VcrDetail from "@/views/modules/erp/vcrGen/travelVcr/components/vcrDetail.vue";
import {useRoute} from "vue-router";

// data
let queryForm = ref({
  type: '',
  idpzh: '',
  kjqj: null
})
let crudRef = ref()
let showVcrMsg = ref(false)
let vcrDetailRef = ref()
let vcrInfo = ref()
let route = useRoute()
let supType = ref()           //凭证类型    1：费用  2：药品  3：工资
let showVcrUpdModal = ref(false)
let vcrMsgForm = ref({
  idpzh: '',
  pzh: '',
})

onMounted(() => {
  watch(
      () => route.query,
      query => {
        if (query && Object.keys(query).length > 0) {
          Object.assign(queryForm.value,query)

          crudRef.value.queryData()
        }
      },{
        immediate: true
      }
  )
})

const vcrType = [
  {
    label: '差旅费凭证',
    value: '1',
  },
  {
    label: '培训费凭证',
    value: '2',
  },
  {
    label: '其他费用凭证',
    value: '3',
  },
  {
    label: '分摊费用凭证',
    value: '4',
  },
  {
    label: '工资凭证',
    value: '5',
  },
  {
    label: '合同费用凭证',
    value: '6',
  },
  {
    label: '折旧凭证',
    value: '7',
  },
  {
    label: '零星采购费用凭证',
    value: '8',
  },
  {
    label: '物资采购凭证',
    value: '10',
  },
  {
    label: '其他费用(无发票)',
    value: '11',
  },
  {
    label: '往来支付',
    value: '12',
  },
  {
    label: '借款',
    value: '13',
  },
  {
    label: '临床试验经费',
    value: '14',
  }
]
const columns = ref<CRUDColumnInterface[]>([
  { title: '#', key: 'index',width:50 },
  { title: '凭证号id', key: 'idpzh',width:120 },
  { title: '会计期间', key: 'kjqj',width:100 },
  { title: '凭证号', key: 'pzh',width:50 },
  { title: '财务凭证金额', key: 'pzje',width:100 },
  { title: '预算凭证金额', key: 'yspzje',width:100 },
  { title: '凭证类型',
    key: 'type',
    width: 80,
    render: (row: any) => {
      switch (row.supType) {
        case '1': return h(NTag, { type: 'success', size: 'small' }, () => vcrType.find(it => it.value == row.type)?.label)
        case '2': return h(NTag, { type: 'success', size: 'small' }, () => '药品凭证')
        case '3': return h(NTag, { type: 'success', size: 'small' }, () => '工资凭证')
        case '4': return h(NTag, { type: 'success', size: 'small' }, () => '折旧凭证')
        default: return ''
      }
    }
  },
  { title: '凭证状态', key: 'status',width:100 },
  { title: '操作',
    key:'operate',
    width:120,
    render: (row: any) => {
      let children = [
          h(NPopover,
              { style: 'padding: 5px',
                showArrow: false
              },{
            trigger: () =>
                h(NPopconfirm,
                    {
                      positiveButtonProps: {
                        type: 'error',
                        secondary: true,
                      },
                      onPositiveClick: (e: Event) => {
                        e.stopPropagation()
                        deleteVcr(row.id)
                      },
                      onNegativeClick: (e: Event) => {
                        e.stopPropagation()
                      }
                    },
                    {
                      trigger: () =>
                        h(JIcon,{
                          name: 'delete',
                          width: 20,
                          height: 20,
                          style: { cursor: 'pointer',marginRight: '10px'},
                          onclick: (e: Event) => {
                            e.stopPropagation()
                          }
                        }),
                      default: () => '是否删除当前凭证信息'
                    }
                ),
                default: () => '删除',
          }),
          h(NPopover, { style: 'padding: 5px',
            showArrow: false
          },{
            trigger: () =>
                h(JIcon,{
                  name: 'preview',
                  width: 20,
                  height: 20,
                  style: { cursor: 'pointer', marginRight: '10px'},
                  onClick: (e: Event) => {
                    queryVcrDetail({idpzh: row.idpzh,supType: row.supType,type: row.type}).then((res: any) => {
                      supType.value = row.supType
                      vcrInfo.value = res.data
                      showVcrMsg.value = true
                    })
                  }
                }),
            default: () => '查看凭证信息'
          }),
        h(NButton,
            {
              type: 'info',
              strong: true,
              secondary: true,
              onClick: () => {
                Object.assign(vcrMsgForm.value,row)
                showVcrUpdModal.value = true
              }
            },
            () => '修改')
      ]
      return h('div',children)
    }
  }
])

const deleteVcr = (id: any) => {
  deleteErpVcrDetail({ ids: [id]}).then((res: any) => {
    if (res.code == 200){
      window.$message.success('删除成功')
      crudRef.value.queryData()
    }
  })
}


const updVcrMsg = ()=> {
  updErpVcrDetailPzh(vcrMsgForm.value).then(res => {
    if (res.code == 200) {
      window.$message.success('修改成功')
      showVcrUpdModal.value = false
      crudRef.value.queryData()
    }
  })
}

</script>

<script lang="ts">
export default {
  name: '凭证箱',
}
</script>
