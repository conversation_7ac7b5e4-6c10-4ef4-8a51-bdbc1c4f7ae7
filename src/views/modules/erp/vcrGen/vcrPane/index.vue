<template>
  <!-- 凭证生成门户 -->
  <div class="med-container">
    <div class="gd-container">
      <n-grid x-gap="30" :cols="gridCols" responsive="screen">
        <n-gi
          v-for="(item, index) in reimLists"
          :key="index"
          @click="vcrGridPane(item)"
          class="gd-item"
          :style="{ background: item.color }"
        >
          <div class="icon" v-html="item.icon"></div>
          <span class="class-font" style="margin-top: 10px; font-family: alimama">{{ item.name }}</span>
        </n-gi>
      </n-grid>
    </div>
  </div>

  <j-modal
    v-model:show="drugModalShow"
    :show-btn="false"
    width="85%"
    height="85%"
    title="报销凭证"
    :content-style="{ height: 'calc(100% - 85px - 56px)' }"
  >
    <ReimVcr v-if="supType == '1'" :type="type" :supType="supType" />
    <DrugVcr v-if="supType == '2'" :type="type" :supType="supType" />
    <SalaryVcr v-if="supType == '3'" :type="type" :supType="supType" />
    <DeprVcr v-if="supType == '4'" :type="type" :supType="supType" />
  </j-modal>
</template>
<script lang="ts" setup>
  import { computed, onMounted, onUnmounted, ref } from 'vue'
  import DrugVcr from '@/views/modules/erp/vcrGen/vcrPane/components/drugVcr.vue'
  import ReimVcr from '@/views/modules/erp/vcrGen/vcrPane/components/reimVcr.vue'
  import SalaryVcr from '@/views/modules/erp/vcrGen/vcrPane/components/salaryVcr.vue'
  import DeprVcr from '@/views/modules/erp/vcrGen/vcrPane/components/deprVcr.vue'

  const colors = [
    'linear-gradient(to bottom, #4887fb, #57bdfe)', // 蓝
    'linear-gradient(to bottom, #09c974, #26dea8)', // 绿
    'linear-gradient(to bottom, #fdb002, #ffc403)', // 黄
    'linear-gradient(to bottom, #fd503f, #fe8336)', // 红
  ]

  // 窗口宽度响应式变量
  const windowWidth = ref(window.innerWidth)

  // 监听窗口大小变化
  const handleResize = () => {
    windowWidth.value = window.innerWidth
  }

  // 根据窗口宽度计算网格列数
  const gridCols = computed(() => {
    const width = windowWidth.value
    if (width < 576) return 3 // 手机屏幕
    if (width < 768) return 3 // 小平板
    if (width < 992) return 4 // 平板
    if (width < 1200) return 4 // 小桌面
    return 5 // 大桌面
  })

  // SVG图标
  const icons = {
    car: '<svg viewBox="64 64 896 896" fill="currentColor"><path d="M380 704h264c4.4 0 8-3.6 8-8v-84c0-4.4-3.6-8-8-8h-40c-4.4 0-8 3.6-8 8v36h-168v-36c0-4.4-3.6-8-8-8h-40c-4.4 0-8 3.6-8 8v84c0 4.4 3.6 8 8 8zm340-123a40 40 0 1080 0 40 40 0 10-80 0zm239-167.6L935.3 372a8 8 0 00-10.9-2.9l-50.7 29.6-78.3-216.2a63.9 63.9 0 00-60.9-44.4H301.2c-34.7 0-65.5 22.4-76.2 55.5l-74.6 205.2-50.8-29.6a8 8 0 00-10.9 2.9L65 413.4c-2.2 3.8-.9 8.6 2.9 10.8l60.4 35.2-14.5 40c-1.2 3.2-1.8 6.6-1.8 10v348.2c0 15.7 11.8 28.4 26.3 28.4h67.6c12.3 0 23-9.3 25.6-22.3l7.7-37.7h545.6l7.7 37.7c2.7 13 13.3 22.3 25.6 22.3h67.6c14.5 0 26.3-12.7 26.3-28.4V509.4c0-3.4-.6-6.8-1.8-10l-14.5-40 60.3-35.2a8 8 0 003-10.8zM840 517v237H184V517l15.6-43h624.8l15.6 43zM292.7 218.1l.5-1.3.4-1.3c1.1-3.3 4.1-5.5 7.6-5.5h427.6l75.4 208H220l72.7-199.9zM224 581a40 40 0 1080 0 40 40 0 10-80 0z"/></svg>',
    book: '<svg viewBox="64 64 896 896" fill="currentColor"><path d="M832 64H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zm-260 72h96v209.9L621.5 312 572 347.4V136zm220 752H232V136h280v296.9c0 3.3 1 6.6 3 9.3a15.9 15.9 0 0022 4.7l83.9-59.2 81.1 57.2c2.7 1.9 5.9 2.9 9.1 2.9 3.2 0 6.3-1 9-2.9 5.6-3.9 8.9-10.3 8.9-17.2V136h72v752z"/></svg>',
    account:
      '<svg viewBox="64 64 896 896" fill="currentColor"><path d="M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z"/></svg>',
    share:
      '<svg viewBox="64 64 896 896" fill="currentColor"><path d="M752 664c-28.5 0-54.8 10-75.4 26.7L469.4 540.8a160.68 160.68 0 000-57.6l207.2-149.9C697.2 350 723.5 360 752 360c66.2 0 120-53.8 120-120s-53.8-120-120-120-120 53.8-120 120c0 11.6 1.6 22.7 4.7 33.3L439.9 415.8C410.7 377.1 364.3 352 312 352c-88.4 0-160 71.6-160 160s71.6 160 160 160c52.3 0 98.7-25.1 127.9-63.8l196.8 142.5c-3.1 10.6-4.7 21.8-4.7 33.3 0 66.2 53.8 120 120 120s120-53.8 120-120-53.8-120-120-120zm0-476c28.7 0 52 23.3 52 52s-23.3 52-52 52-52-23.3-52-52 23.3-52 52-52zM312 600c-48.5 0-88-39.5-88-88s39.5-88 88-88 88 39.5 88 88-39.5 88-88 88zm440 236c-28.7 0-52-23.3-52-52s23.3-52 52-52 52 23.3 52 52-23.3 52-52 52z"/></svg>',
    dollar:
      '<svg viewBox="64 64 896 896" fill="currentColor"><path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372zm47.7-395.2l-25.4-5.9V348.6c38 5.2 61.5 29 65.5 58.2.5 4 3.9 6.9 7.9 6.9h44.9c4.7 0 8.4-4.1 8-8.8-6.1-62.3-57.4-102.3-125.9-109.2V263c0-4.4-3.6-8-8-8h-28.1c-4.4 0-8 3.6-8 8v33c-70.8 6.9-126.2 46-126.2 119 0 67.6 49.8 100.2 102.1 112.7l24.7 6.3v142.7c-44.2-5.9-69-29.5-74.1-61.3-.6-3.8-4-6.6-7.9-6.6H363c-4.7 0-8.4 4-8 8.7 4.5 55 46.2 105.6 135.2 112.1V761c0 4.4 3.6 8 8 8h28.4c4.4 0 8-3.6 8-8.1l-.2-31.7c78.3-6.9 134.3-48.8 134.3-124-.1-69.4-44.2-100.4-109-116.4zm-78.1-146.5c0-36.3 27.5-57.1 61.3-61.6v125.6c-32.4-7.7-61.3-29.3-61.3-64zm61.3 167.8v128.4c-38.5-5.2-64.7-25.4-64.7-65.1 0-36.1 26-56.2 64.7-63.3z"/></svg>',
    file: '<svg viewBox="64 64 896 896" fill="currentColor"><path d="M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494z"/></svg>',
    calculator:
      '<svg viewBox="64 64 896 896" fill="currentColor"><path d="M251.2 387H320v68.8c0 1.8 1.8 3.2 4 3.2h48c2.2 0 4-1.4 4-3.2V387h68.8c1.8 0 3.2-1.8 3.2-4v-48c0-2.2-1.4-4-3.2-4H376v-68.8c0-1.8-1.8-3.2-4-3.2h-48c-2.2 0-4 1.4-4 3.2V331h-68.8c-1.8 0-3.2 1.8-3.2 4v48c0 2.2 1.4 4 3.2 4zm328 0h193.6c1.8 0 3.2-1.8 3.2-4v-48c0-2.2-1.4-4-3.2-4H579.2c-1.8 0-3.2 1.8-3.2 4v48c0 2.2 1.4 4 3.2 4zm0 265h193.6c1.8 0 3.2-1.8 3.2-4v-48c0-2.2-1.4-4-3.2-4H579.2c-1.8 0-3.2 1.8-3.2 4v48c0 2.2 1.4 4 3.2 4zm0 104h193.6c1.8 0 3.2-1.8 3.2-4v-48c0-2.2-1.4-4-3.2-4H579.2c-1.8 0-3.2 1.8-3.2 4v48c0 2.2 1.4 4 3.2 4zm-195.2-81l61.6-61.6c1.8-1.8 1.8-4.8 0-6.4l-34-34c-1.8-1.8-4.8-1.8-6.4 0l-61.6 61.6-61.6-61.6c-1.8-1.8-4.8-1.8-6.4 0l-34 34c-1.8 1.8-1.8 4.8 0 6.4l61.6 61.6-61.6 61.6c-1.8 1.8-1.8 4.8 0 6.4l34 34c1.8 1.8 4.8 1.8 6.4 0l61.6-61.6 61.6 61.6c1.8 1.8 4.8 1.8 6.4 0l34-34c1.8-1.8 1.8-4.8 0-6.4l-61.6-61.6zM880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-36 732H180V180h664v664z"/></svg>',
    cart: '<svg viewBox="64 64 896 896" fill="currentColor"><path d="M922.9 701.9H327.4l29.9-60.9 496.8-.9c16.8 0 31.2-12 34.2-28.6l68.8-385.1c1.8-10.1-.9-20.5-7.5-28.4a34.99 34.99 0 00-26.6-12.5l-632-2.1-5.4-25.4c-3.4-16.2-18-28-34.6-28H96.5a35.3 35.3 0 100 70.6h125.9L246 312.8l58.1 281.3-74.8 122.1a34.96 34.96 0 00-3 36.8c6 11.9 18.1 19.4 31.5 19.4h62.8a102.43 102.43 0 00-20.6 61.7c0 56.6 46 102.6 102.6 102.6s102.6-46 102.6-102.6c0-22.3-7.4-44-20.6-61.7h161.1a102.43 102.43 0 00-20.6 61.7c0 56.6 46 102.6 102.6 102.6s102.6-46 102.6-102.6c0-22.3-7.4-44-20.6-61.7H923c19.4 0 35.3-15.8 35.3-35.3a35.42 35.42 0 00-35.4-35.2zM305.7 253l575.8 1.9-56.4 315.8-452.3.8L305.7 253zm96.9 612.7c-17.4 0-31.6-14.2-31.6-31.6 0-17.4 14.2-31.6 31.6-31.6s31.6 14.2 31.6 31.6a31.6 31.6 0 01-31.6 31.6zm325.1 0c-17.4 0-31.6-14.2-31.6-31.6 0-17.4 14.2-31.6 31.6-31.6s31.6 14.2 31.6 31.6a31.6 31.6 0 01-31.6 31.6z"/></svg>',
    experiment:
      '<svg viewBox="64 64 896 896" fill="currentColor"><path d="M512 472a40 40 0 1080 0 40 40 0 10-80 0zm367 352.9L696.3 352V178H768v-68H256v68h71.7v174L145 824.9c-2.8 7.4-4.3 15.2-4.3 23.1 0 35.3 28.7 64 64 64h614.6c7.9 0 15.7-1.5 23.1-4.3 33-12.7 49.4-49.8 36.6-82.8zM395.7 364.7V180h232.6v184.7L719.2 600c-20.7-5.3-42.1-8-63.9-8-61.2 0-119.2 21.5-165.3 60a188.78 188.78 0 00-121.3 60.1l-75.2-225.1L395.7 364.7zM220.2 848l83.9-251.7c18.2-54.5 72.3-90.4 130.9-90.4 27.7 0 54.1 8.4 76.3 24.3 22.2-15.9 48.6-24.3 76.3-24.3 58.6 0 112.7 35.9 130.9 90.4L803 848H220.2z"/></svg>',
    medicine:
      '<svg viewBox="64 64 896 896" fill="currentColor"><path d="M839.2 278.1a32 32 0 00-30.4-22.1H736V144c0-17.7-14.3-32-32-32H320c-17.7 0-32 14.3-32 32v112h-72.8a31.9 31.9 0 00-30.4 22.1L112 502v378c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V502l-72.8-223.9zM360 184h304v72H360v-72zm480 656H184V513.4L244.3 328h535.4L840 513.4V840zM652 572H544V464c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v108H372c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h108v108c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V636h108c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8z"/></svg>',
    shopping:
      '<svg viewBox="64 64 896 896" fill="currentColor"><path d="M832 312H696v-16c0-101.6-82.4-184-184-184s-184 82.4-184 184v16H192c-17.7 0-32 14.3-32 32v536c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V344c0-17.7-14.3-32-32-32zm-432-16c0-61.9 50.1-112 112-112s112 50.1 112 112v16H400v-16zm392 544H232V384h96v88c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-88h224v88c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-88h96v456z"/></svg>',
  }

  // 响应式数据
  const drugModalShow = ref(false)
  const supType = ref()
  const type = ref()

  let reimLists = ref<Array<any>>([
    {
      color: colors[0 % 4],
      name: '差旅凭证',
      supType: '1',
      type: '1',
      icon: icons.car,
    },
    {
      color: colors[1 % 4],
      name: '培训凭证',
      supType: '1',
      type: '2',
      icon: icons.book,
    },
    {
      color: colors[2 % 4],
      name: '费用报销凭证',
      supType: '1',
      type: '3',
      icon: icons.account,
    },
    {
      color: colors[3 % 4],
      name: '分摊报销凭证',
      supType: '1',
      type: '4',
      icon: icons.share,
    },
    {
      color: colors[4 % 4],
      name: '工资报销凭证',
      supType: '3',
      type: '5',
      icon: icons.dollar,
    },
    {
      color: colors[5 % 4],
      name: '合同报销凭证',
      supType: '1',
      type: '6',
      icon: icons.file,
    },
    {
      color: colors[6 % 4],
      name: '折旧报销凭证',
      supType: '4',
      type: '7',
      icon: icons.calculator,
    },
    {
      color: colors[7 % 4],
      name: '零星采购报销凭证',
      supType: '1',
      type: '8',
      icon: icons.cart,
    },
    {
      color: colors[8 % 4],
      name: '科研经费报销凭证',
      supType: '1',
      type: '9',
      icon: icons.experiment,
    },
    {
      color: colors[9 % 4],
      name: '药品报销凭证',
      supType: '2',
      type: '1',
      icon: icons.medicine,
    },
    {
      color: colors[10 % 4],
      name: '物资采购凭证',
      supType: '1',
      type: '10',
      icon: icons.shopping,
    },
    {
      color: colors[11 % 4],
      name: '其他费用报销(无发票)',
      supType: '1',
      type: '11',
      icon: icons.account,
    },
    {
      color: colors[12 % 4],
      name: '往来支付',
      supType: '1',
      type: '12',
      icon: icons.account,
    },
    {
      color: colors[13 % 4],
      name: '借款',
      supType: '1',
      type: '13',
      icon: icons.account,
    },
    {
      color: colors[14 % 4],
      name: '临床试验经费',
      supType: '1',
      type: '14',
      icon: icons.account,
    }
  ])

  //展示
  const vcrGridPane = (item: any) => {
    supType.value = item.supType
    type.value = item.type
    drugModalShow.value = true
  }

  // 生命周期钩子
  onMounted(() => {
    window.addEventListener('resize', handleResize)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
  })
</script>
<style lang="less" scoped>
  .gd-container {
    box-sizing: border-box;
    overflow-y: auto;
    height: 100%;
    background-color: rgb(237, 247, 255);
    width: 100%;
    padding: var(--container-padding, 100px 200px 0 200px);
    //background-image: url(@/assets/images/bj.jpg);
  }
  .gd-container::-webkit-scrollbar {
    display: none;
  }
  .gd-item {
    height: 175px;
    cursor: pointer;
    margin-bottom: 1rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: white;
    border-radius: 10px;
    justify-content: center;
    background: linear-gradient(to bottom, #4887fb, #57bdfe);
    transition: all 0.5s ease;
  }
  .gd-item:hover {
    transform: scale(1.05);
  }

  .icon {
    width: 48px;
    height: 48px;
    margin-bottom: 8px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .icon svg {
    width: 100%;
    height: 100%;
    fill: currentColor;
  }

  /* 响应式样式 */
  @media screen and (max-width: 1200px) {
    .gd-container {
      --container-padding: 80px 100px 0 100px;
    }
  }

  @media screen and (max-width: 992px) {
    .gd-container {
      --container-padding: 60px 50px 0 50px;
    }
    .gd-item {
      height: 150px;
    }
    .icon {
      width: 40px;
      height: 40px;
    }
  }

  @media screen and (max-width: 768px) {
    .gd-container {
      --container-padding: 40px 30px 0 30px;
    }
    .gd-item {
      height: 130px;
    }
    .icon {
      width: 36px;
      height: 36px;
    }
    .class-font {
      font-size: 14px !important;
    }
  }

  @media screen and (max-width: 576px) {
    .gd-container {
      --container-padding: 30px 20px 0 20px;
    }
    .gd-item {
      height: 105px;
    }
    .icon {
      width: 28px;
      height: 28px;
      margin-bottom: 6px;
    }
    .class-font {
      font-size: 12px !important;
      text-shadow: 0.04px -0.1px 0 #000, 0.1px -0.1px 0 #000;
    }
  }
  .class-font {
    font-size: 16px;
    font-weight: 500;
    text-shadow: 0.1px -0.1px 0 #000, 0.1px -0.1px 0 #000;
  }
</style>
