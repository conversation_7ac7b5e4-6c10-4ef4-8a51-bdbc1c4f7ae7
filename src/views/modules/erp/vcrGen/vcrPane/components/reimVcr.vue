<template>
  <j-crud
      :queryMethod="queryErpReimDetail"
      :tabs="tabs"
      default-check-tab="0"
      :queryForm="queryForm"
      :show-operation-button="false"
      v-model:checked-row-keys="checkRowKeys"
      ref="crudRef"
  >
    <template #extendFormItems>
      <n-select v-if="false" v-model:value="fundType" :options="fundTypeOption"/>
    </template>
    <template #extendButtons>
      <n-button type="primary" @click="genVcr">生成凭证信息</n-button>
    </template>

    <template #content>
      <!-- 凭证生成填写界面 -->
      <j-modal
          v-model:show="showVcrFill"
          width="95%"
          height="95%"
          :title="vcrGenTitle"
          :content-style="{ height: 'calc(100% - 85px - 56px)'}"
          @confirm="confirm"
          @update:show="resetVcrForm"
      >
        <template #header-extra>
          <n-button type="info" @click="showApprInfo" v-if="showApprFlag">{{ msgBtnPre }}申请审批表</n-button>
          <n-button type="primary" style="margin-left: 10px" @click="showReimInfo" v-if="showReimFlag">{{
              msgBtnPre
            }}报销审批表
          </n-button>
        </template>
        <n-scrollbar style="height: 100%">
          <n-space vertical>
            <VcrDetail
                ref="vcrDetailRef"
                gen-type="1"
                :onlyRead="false"
                sup-type="1"
                :type="reimType"
                :vpzh="vpzh"
                :details="vcrGenInfo"
                :payMethod="payMethod"
                :checked-row-keys="checkRowKeys"
                @close="close"
            ></VcrDetail>
          </n-space>
        </n-scrollbar>
      </j-modal>

      <!-- 报销信息展示 -->
      <j-modal
          v-model:show="showDetail"
          :show-btn="!view"
          width="95%"
          height="95%"
          :title="reimPaneTitle"
          :content-style="{ height: 'calc(100% - 85px - 56px)'}"
      >
        <template #header>
          <div style="text-align: center">{{ apprTitle }}</div>
        </template>
        <n-scrollbar style="height: 100%">
          <n-space vertical>
            <ReimDetail
                ref="reimDetailRef"
                :details="details"
                :view="view"
                :type="reimType"
                :appr-flag="false"
                :address-data="codeData"
                :show-audit-step="view"
                :is-audit="false"
                :reimLastNoShow="reimLastNoShow"
                :show-audit-steps="true"
                @close="showDetail = false"/>
          </n-space>
        </n-scrollbar>
      </j-modal>

      <!-- 流程详情弹窗 -->
      <ProcessInstanceDetailModal :processInstanceId="currentProcessInstanceId" v-model:show="processDetailVisible"/>

      <!-- 差旅、培训申请展示界面 -->
      <j-modal
          v-model:show="showAppr"
          width="95%"
          height="95%"
          :content-style="{ height: '85%'}"
          :show-btn="false"
      >
        <template #header>
          <div style="text-align: center">{{ reimType == '1' ? '差旅费申请审批表' : '培训费申请审批表' }}</div>
        </template>
        <n-scrollbar style="height: 100%">
          <TravelApprDetail
              :type="reimType"
              :view="true"
              :show-audit-steps="true"
              :is-audit="false"
              :details="apprDetails"
              :address-data="codeData"
              @close="showAppr = false"
              ref="travelApprDetailRef"/>
        </n-scrollbar>
      </j-modal>

      <!-- 可生成凭证的报销项目界面 -->
      <j-modal
          v-model:show="showVcrPane"
          :show-btn="false"
          width="100%"
          height="100%"
          :title="vcrPaneTitle"
          :content-style="{ height: 'calc(100% - 85px -56px)'}"
          @update:show="resetVcrPane"
      >

      </j-modal>

      <!-- 已选生成凭证报销项对应报销信息或申请信息 -->
      <j-modal
          v-model:show="showChoosedExtraMsg"
          :show-btn="false"
          width="95%"
          height="95%"
          :title="showChoosedExtraTitle"
          :content-style="{ height: 'calc(100% - 85px - 56px)'}"
      >
        <j-n-data-table :columns="chooseExtra" :data="chooseExtraData"/>
      </j-modal>

    </template>
  </j-crud>
</template>
<script lang="ts" setup>

import {
  delDrugVpzhMsg,
  queryApprInfo,
  queryDrugVpzh,
  queryErpReimDetail,
  queryReimInfo,
  queryVcrReimAsst
} from "@/api/erp/vcr/vcrGen/vcrGen.ts";
import ReimDetail from "@/views/modules/ecs/reimMgt/expenseReim/components/reimDetail.vue";
import ProcessInstanceDetailModal from "@/views/modules/bpm/processInstance/detail/processDetailModal.vue";
import VcrDetail from "@/views/modules/erp/vcrGen/travelVcr/components/vcrDetail.vue";
import TravelApprDetail from "@/views/modules/ecs/reimMgt/travelAppr/components/travelApprDetail.vue";
import {h, ref} from "vue";
import {CRUDColumnInterface, JTab} from "@/types/comps/crud.ts";
import {ContainerValueType} from "@/types/enums/enums.ts";
import {NTag} from "naive-ui";
import JPGlobal from "@jutil";
import {useRouter} from "vue-router";
import {queryItemDetail, queryPsnDetails} from "@/api/ecs/reimMgt/reimDetail.ts";
import {computed, onMounted} from "vue";
import {queryDictData} from "@/api/hrm/dictManage/treeSelectDict.ts";
import {queryContractType} from "@/api/cms/contractType.ts";

//refs
const crudRef = ref()
const processDetailVisible = ref()
const currentProcessInstanceId = ref()
const vcrDetailRef = ref()


const router = useRouter()
let chooseExtraData = ref([])
let checkRowKeys = ref([])    //选中的行
let fundType = ref('1')       //资金类型  默认为1 自有类型
let showApprFlag = ref(false)      //是否展示申请审批表btn     默认不展示
let vcrGenInfo = ref<any>({}) //凭证详情界面信息
let reimType = ref('')        //凭证类型
let vcrGenTitle = ref('')     //凭证生成面板标题
let msgBtnPre = ref('')     //申请或报销详情按钮前缀
let showVcrFill = ref(false)  //显示凭证生成面板
let reimLastNoShow = ref(false)//报销详情不展示最后出纳审核
let apprTitle = ref('')       //modal标题
let view = ref(false)         //是否仅展示
let showDetail = ref(false)   //显示报销面板
let showVcrPane = ref(false)  //显示凭证面板
let msgBtnType = ref('1')        //展示申请、报销按钮类型    0：申请 1：报销
let showChoosedExtraMsg = ref(false) //展示已选生成凭证报销项信息
let codeData = ref([])        //地区code
let showReimFlag = ref(true)           //是否展示报销审批表btn  默认展示
let reimPaneTitle = ref('')   //报销面板标题
let vcrPaneTitle = ref('')    //凭证面板标题
let showChoosedExtraTitle = ref('')  //展示已选生成凭证报销项modal标题
let showAppr = ref(false)     //申请展示界面
let vpzh = ref()            //查询vpzh
let payMethod = ref()

let contractTypeNoTree = ref<any[]>([])
let paymentTypeDict = ref<any[]>([]) // 付款方式字典

const props = defineProps({
  type: {
    type: String,
    required: true,
  }
})

let apprDetails = ref({
  formData: {},
  psnDetails: [],
})

let queryForm = ref({
  auditFlag: '0',
  genFlag: '0',
  type: '',
  ids: [],
  supType: '1'
})

let details = ref({           //报销信息
  formData: {},
  itemDetails: [],
  subsItemDetails: [],
  psnDetails: [],
  reimAsstDetails: [],
  fileRecords: []
})

let queryExtraForm = ref({
  audit: true,
  auditFlag: '0',
  type: '',
  ids: []
})

//资金类型option
let fundTypeOption = ref([{
  label: '自有资金',
  value: '1'
}, {
  label: '财政资金',
  value: '2'
}])

//展示申请审批表
const showApprInfo = () => {
  if (checkRowKeys.value.length == 0) {
    window.$message.warning('当前未选择报销项')
    return
  }

  msgBtnType.value = '0'
  queryExtraForm.value.type = reimType.value
  let shareItem = crudRef.value.originData.filter((item: any) => checkRowKeys.value.includes(item.id))
  queryExtraForm.value.ids = shareItem.map((item: any) => item.travelApprId)
  queryApprInfo(queryExtraForm.value).then((res: any) => {
    if (res.code == 200) {
      showChoosedExtraMsg.value = true
      chooseExtraData.value = res.data
    }
  })
}

const genVcr = async () => {

  if (queryForm.value.genFlag != '0') {
    window.$message.error("请选择未生成报销项目")
    return
  }
  if (!(checkRowKeys.value.length > 0)) {
    window.$message.error('请至少选择一条报销项')
    return
  }

  //分摊
  if (reimType.value == '4') {
    if (checkRowKeys.value.length > 1) {
      window.$message.error('只能选择一条报销项')
      return
    }

    let shareItem = crudRef.value.originData.filter((item: any) => checkRowKeys.value.includes(item.id))
    if (shareItem.length > 1) {
      window.$message.warning('当前选择了多个报销项')
      return
    }

    vcrGenInfo.value.shareType = shareItem[0]['shareType']
    fundType.value = shareItem[0]['fundType']
  }
  //工资
  if (reimType.value == '5') {
    if (checkRowKeys.value.length > 1) {
      window.$message.error('只能选择一条工资报销项目')
      return
    }
    let salaryItem = crudRef.value.originData.filter((item: any) => checkRowKeys.value.includes(item.id))
    vcrGenInfo.value.salaryType = salaryItem[0]['salaryType']
  }
  if (reimType.value == '11') {
    if (checkRowKeys.value.length > 1) {
      window.$message.error('只能选择一条报销项')
      return
    }
  }
  vcrGenInfo.value.fundType = fundType.value
  //零星采购或者物资采购
  if (reimType.value == '8' || reimType.value == '10') {
    //判断报销的支付类型，不同支付方式不能同时选择
    let payMethods = Array.from(new Set(crudRef.value.originData.filter(e => checkRowKeys.value.includes(e.id))?.map(e => e.payMethod)))
    if (payMethods.length > 1) {
      window.$message.error('不能同时选择现金和非现金的支付方式')
      return
    }
    payMethod.value = payMethods[0]
    //先查询所选报销对应的vpzh
    await queryDrugVpzh({supType: '1', type: reimType.value, ids: checkRowKeys.value}).then(res => {
      if (res.code == 200) {
        let msg = res.data
        if (msg == '-1' || msg == '-2') {        //所选报销对应多个vpzh，需要重新选择或者删除已存在的vpzh
          //弹出提示框
          window.$dialog.warning({
            title: msg == '-1' ? '当前报销对应预览凭证不一致' : '当前报销对应预览凭证未选择完整',
            content: '是否删除当前报销关联的预览凭证',
            maskClosable: false,
            positiveText: '确定',
            negativeText: '取消',
            onPositiveClick: () => {
              delDrugVpzhMsg({supType: '1', type: reimType.value, ids: checkRowKeys.value}).then(res => {
                if (res.code == 200) {
                  vpzh.value = null
                  queryAsst()
                }
              })
            }
          })
        } else if (msg == '0') {  //所选报销都未生成vpzh,可直接进行生成
          vpzh.value = null
          queryAsst()
        } else {        //只对应一个vpzh，查询已生成的辅助项目
          vpzh.value = msg
          queryAsst()
        }
      }
    })
  } else {
    queryAsst()
  }
}

const queryAsst = () => {
  //查询所选报销的辅助项目
  queryVcrReimAsst({supType: '1', type: reimType.value, ids: checkRowKeys.value}).then((res: any) => {
    if (res.code == 200) {
      vcrGenInfo.value.reimDetailList = crudRef.value.originData.filter((item: any) => checkRowKeys.value.includes(item.id))
      vcrGenInfo.value.vcrAsstList = res.data.vcrAsstList
      vcrGenInfo.value.vcrItemList = res.data.vcrItemList
      vcrGenInfo.value.vcrSubItemList = res.data.vcrSubItemList
      vcrGenInfo.value.fileRecords = res.data.fileRecords
      vcrGenInfo.value.drugStoins = res.data.drugStoins
      // vcrGenTitle.value = reimLists.value.find((item: any) => reimType.value == item.type).name + '生成'
      vcrGenTitle.value = '凭证生成'
      msgBtnPre.value = reimOptions.find((ro: any) => ro.value == reimType.value)?.label
      showApprFlag.value = (reimType.value == '1' || reimType.value == '2')
      showVcrFill.value = true
    }
  })
}

//报销展示界面   tabType: 申请/报销   curType: 报销类型
const getChooseExtraColumns = (btnType: string, curType?: string): CRUDColumnInterface[] => {
  let btnColumns
  if (btnType == '0') {
    btnColumns = [
      {title: '#', key: 'index', width: 50},
      {title: '申请人', key: 'appyer', width: 100, render: (row: any) => row.appyerName},
      {title: '申请时间', key: 'appyerTime', width: 150},
      {title: '出差事由', key: 'evectionRea', width: 300},
      {title: '出差开始时间', key: 'evectionBegnTime', width: 130},
      {title: '出差结束时间', key: 'evectionEndTime', width: 130},
      {
        title: '出差地点',
        key: 'evectionAddr',
        width: 160,
        ellipsis: {
          tooltip: true,
        },
        render: (row: any) => {
          return JPGlobal.findPath(row.evectionAddr, codeData.value)
        },
      },
      {title: '是否绕道', key: 'detourOrNot', width: 100, dictType: 'YES_OR_NO', type: ContainerValueType.SELECT},
      {title: '公里数', key: 'kil', width: 100},
      {title: '交通方式', key: 'trnp', width: 100, type: ContainerValueType.SELECT, dictType: 'REIM_TRNP'},
      {title: '是否安排伙食', key: 'food', width: 130, dictType: 'YES_OR_NO', type: ContainerValueType.SELECT},
      {title: '是否安排住宿', key: 'stay', width: 130, dictType: 'YES_OR_NO', type: ContainerValueType.SELECT},
      {title: '预计出差金额', key: 'planAmt', width: 130},
      {
        title: '是否报销',
        key: 'reimFlag',
        width: 130,
        render: (row: any) => {
          if (row.reimFlag) {
            return
          }
          return ''
        },
      },
      {
        title: '类型',
        key: 'type',
        width: 150,
        fixed: 'right',
        align: 'center',
        render: (row: any) => {
          return h(NTag, {type: 'success', size: 'small'}, () => {
            return row.type == '1' ? '差旅费' : '培训费'
          })
        },
      }
    ]
  } else {
    btnColumns = getMidColumns(curType)
  }
  let option = {
    title: '操作',
    key: 'operate',
    width: 120,
    fixed: 'right',
    align: 'center',
    render: (row: any) => {
      let renderItem = [
        h(
            'span',
            {
              style: JPGlobal.linkStyle,
              onclick: () => {
                if (row.processInstanceId) {
                  processDetailVisible.value = true
                  currentProcessInstanceId.value = row.processInstanceId
                } else {
                  if (btnType == '0') {
                    queryPsnDetails({id: row.id, type: '1'}).then((res: any) => {
                      apprDetails.value.formData = row
                      apprDetails.value.psnDetails = res.data
                      showAppr.value = true
                    })
                  } else {
                    queryItemDetail({id: row.id, attCode: row.attCode}).then((res: any) => {
                      details.value.formData = row
                      details.value.itemDetails = res.data.itemDetails
                      details.value.subsItemDetails = res.data.subsItemDetails
                      details.value.psnDetails = res.data.psnDetails
                      details.value.reimAsstDetails = res.data.reimAsstDetails
                      details.value.fileRecords = res.data.fileRecords
                      reimLastNoShow.value = true
                      apprTitle.value = reimOptions.find((item: any) => item.value == row.type)?.label + '报销审批表'
                      view.value = true
                      showDetail.value = true
                    })
                  }
                }
              }
            },
            btnType == '0' ? '查看申请' : '查看报销'
        ),
      ]
      return renderItem
    }
  }
  return [...btnColumns, option]
}

let chooseExtra = computed(() => {
  return msgBtnType.value == '0' ? getChooseExtraColumns('0', reimType.value) : getChooseExtraColumns('1', reimType.value)
})

const busstasRender = (row: any) => {
  switch (row.busstas) {
    case '1':
      return h(NTag, {type: 'success', size: 'small'}, () => '已付款')
    case '2':
      return h(NTag, {type: 'info', size: 'small'}, () => '未提交')
    case '3':
      return h(NTag, {type: 'warning', size: 'small'}, () => '审核中')
    case '4':
      return h(NTag, {type: 'error', size: 'small'}, () => '审批驳回')
    default:
      return ''
  }
}

const getMidColumns = (reimType: string) => {
  if (reimType == '1') {      //差旅
    return [
      {
        title: '#',
        key: 'key',
        width: 50,
        render: (_, index) => {
          return `${index + 1}`
        }
      },
      {
        title: '报销标识',
        key: 'id',
        width: 100,
      },
      {
        title: '申请人',
        key: 'appyer',
        width: 100,
        render: (row: any) => row.appyerName
      },
      {title: '申请时间', key: 'appyerTime', width: 150},
      {title: '付款时间', key: 'paidTime', width: 150},
      /*{
        title: '出差开始时间', key: 'evectionBegnTime', width: 120,
      },
      { title: '出差结束时间', key: 'evectionEndTime', width: 120},*/

      {
        title: '出差地点',
        key: 'evectionAddr',
        width: 100,
        render: (row: any) => {
          return JPGlobal.findPath(row.evectionAddr, codeData.value)
        }
      },
      {title: '出差事由', key: 'evectionRea', width: 300},
      {title: '合计金额小写(元)', key: 'sum', width: 140},
      {
        title: '业务状态', key: 'busstas', width: 100, show: false,
        render: (row: any) => busstasRender(row)
      },
      {
        title: '支付类型',
        key: 'payMethod',
        width: 100,
        render: (row: any) => {
          switch (row.payMethod) {
            case '1':
              return h(NTag, {type: 'info', size: 'small'}, () => '现金支付')
            case '2':
              return h(NTag, {type: 'warning', size: 'small'}, () => '复明工程')
            default:
              return h(NTag, {type: 'success', size: 'small'}, () => '非现金支付')
          }
        }
      },
      {
        title: '类型',
        key: 'type',
        width: 150,
        fixed: 'right',
        align: 'center',
        render: (row: any) => {
          return h(NTag, {
            type: 'success',
            size: 'small'
          }, () => reimOptions.find((item: any) => item.value == row.type)?.label)
        }
      },

    ]
  } else if (reimType == '2') {   //培训
    return [
      {
        title: '#',
        key: 'key',
        width: 50,
        render: (_, index) => {
          return `${index + 1}`
        }
      },
      {
        title: '报销标识',
        key: 'id',
        width: 100,
      },
      {
        title: '申请人',
        key: 'appyer',
        width: 100,
        render: (row: any) => row.appyerName
      },
      {title: '申请时间', key: 'appyerTime', width: 150},
      {title: '付款时间', key: 'paidTime', width: 150},
      /*{
        title: '出差开始时间', key: 'evectionBegnTime', width: 120,
      },
      { title: '出差结束时间', key: 'evectionEndTime', width: 120},*/

      {
        title: '出差地点',
        key: 'evectionAddr',
        width: 100,
        render: (row: any) => {
          return JPGlobal.findPath(row.evectionAddr, codeData.value)
        }
      },
      {title: '出差事由', key: 'evectionRea', width: 300},
      {title: '合计金额小写(元)', key: 'sum', width: 140},
      {
        title: '业务状态', key: 'busstas', width: 100, show: false,
        render: (row: any) => busstasRender(row)
      },
      {
        title: '支付类型',
        key: 'payMethod',
        width: 100,
        render: (row: any) => {
          switch (row.payMethod) {
            case '1':
              return h(NTag, {type: 'info', size: 'small'}, () => '现金支付')
            case '2':
              return h(NTag, {type: 'warning', size: 'small'}, () => '复明工程')
            default:
              return h(NTag, {type: 'success', size: 'small'}, () => '非现金支付')
          }
        }
      },
      {
        title: '类型',
        key: 'type',
        width: 150,
        fixed: 'right',
        align: 'center',
        render: (row: any) => {
          return h(NTag, {
            type: 'success',
            size: 'small'
          }, () => reimOptions.find((item: any) => item.value == row.type)?.label)
        }
      },]
  } else if (reimType == '3' || reimType == '11' || reimType == '12' || reimType == '13' || reimType == '14') {    //费用
    return [
      {
        title: '#',
        key: 'key',
        width: 50,
        render: (_, index) => {
          return `${index + 1}`
        }
      },
      {
        title: '报销标识',
        key: 'id',
        width: 100,
      },
      {
        title: '申请人',
        key: 'appyer',
        width: 100,
        render: (row: any) => row.appyerName
      },
      {title: '申请时间', key: 'appyerTime', width: 150},
      {title: '付款时间', key: 'paidTime', width: 150},
      {title: '合计金额小写(元)', key: 'sum', width: 140},
      {
        title: '业务状态', key: 'busstas', width: 100, show: false,
        render: (row: any) => busstasRender(row)
      },
      {
        title: '支付类型',
        key: 'payMethod',
        width: 100,
        render: (row: any) => {
          switch (row.payMethod) {
            case '1':
              return h(NTag, {type: 'info', size: 'small'}, () => '现金支付')
            case '2':
              return h(NTag, {type: 'warning', size: 'small'}, () => '复明工程')
            default:
              return h(NTag, {type: 'success', size: 'small'}, () => '非现金支付')
          }
        }
      },
      {
        title: '类型',
        key: 'type',
        width: 150,
        fixed: 'right',
        align: 'center',
        render: (row: any) => {
          return h(NTag, {
            type: 'success',
            size: 'small'
          }, () => reimOptions.find((item: any) => item.value == row.type)?.label)
        }
      },

    ]
  } else if (reimType == '4') {   //分摊
    return [
      {
        title: '#',
        key: 'key',
        width: 50,
        render: (_, index) => {
          return `${index + 1}`
        }
      },
      {
        title: '报销标识',
        key: 'id',
        width: 100,
      },
      {
        title: '申请人',
        key: 'appyer',
        width: 100,
        render: (row: any) => row.appyerName
      },
      {title: '申请时间', key: 'appyerTime', width: 150},
      {title: '付款时间', key: 'paidTime', width: 150},
      {title: '合计金额小写(元)', key: 'sum', width: 140},
      {
        title: '业务状态', key: 'busstas', width: 100, show: false,
        render: (row: any) => busstasRender(row)
      },
      {
        title: '类型',
        key: 'type',
        width: 150,
        fixed: 'right',
        align: 'center',
        render: (row: any) => {
          return h(NTag, {
            type: 'success',
            size: 'small'
          }, () => reimOptions.find((item: any) => item.value == row.type)?.label)
        }
      },
      {
        title: '分摊类型',
        key: 'shareType',
        width: 100,
        render: (row: any) => {
          return JPGlobal.getDictByType('ACTIG_SHARE_TYPE').find((item: any) => item.value == row.shareType)?.label
        },
      },
      {
        title: '分摊总额',
        key: 'shareAmt',
        width: 100,
      },
      {
        title: '分摊月份',
        key: 'shareDate',
        width: 100,
      },
    ]
  } else if (reimType == '5') {  //工资
    return [
      {
        title: '#',
        key: 'key',
        width: 50,
        render: (_, index) => {
          return `${index + 1}`
        }
      },
      {
        title: '报销标识',
        key: 'id',
        width: 100,
      },
      {
        title: '工资发放月份',
        key: 'ffMth',
        width: 100,
      },
      {
        title: '工资条数',
        key: 'num',
        width: 100,
      }, {
        title: '应发合计汇总',
        key: 'shouldPay',
        width: 100,
      }, {
        title: '扣款合计汇总',
        key: 'reducePay',
        width: 100,
      }, {
        title: '实发合计汇总',
        key: 'realPay',
        width: 100,
      }, {
        title: '备注',
        key: 'remark',
        width: 100,
      }
    ]
  } else if (reimType == '6') {    //合同
    return [
      {
        title: '#',
        key: 'key',
        width: 50,
        render: (_, index) => {
          return `${index + 1}`
        }
      },
      {
        title: '报销标识',
        key: 'id',
        width: 100,
      },
      {
        title: '合同统一编码',
        key: 'ctUnifiedCode',
        width: 100,
      },
      {
        title: '合同编码',
        key: 'ctCode',
        width: 100,
      },
      {
        title: '合同名称',
        key: 'ctName',
        width: 220,
        render: (row: any) => {
          return h(
              'span',
              {
                style: {
                  fontSize: '16px',
                  fontWeight: 'bold',
                  color: '#333',
                },
              },
              row.ctName
          )
        },
      },
      {
        title: '合同分类',
        key: 'typeCode',
        width: 300,
        render: (row: any) => {
          const item = contractTypeNoTree.value?.find(item => item.code === row.typeCode)
          let type: 'default' | 'error' | 'success' | 'warning' | 'primary' | 'info' = 'success'
          // 采购类
          if (item?.name?.includes('采购')) {
            type = 'warning'
          }
          // 通用类
          else if (item?.name?.includes('通用')) {
            type = 'info'
          }
          return h(NTag, {type}, {default: () => (item ? item.name : row.typeCode)})
        },
      },
      {
        title: '合同相对方',
        key: 'oppositeName',
        width: 180,
        render: (row: any) => {
          return h(
              'span',
              {
                style: {
                  fontSize: '16px',
                  color: '#333',
                },
              },
              row.oppositeName
          )
        },
      },
      {
        title: '合同附件',
        key: 'contractAtt',
        width: 100,
      }, {
        title: '合同总额',
        key: 'totalAmt',
        width: 100,
      }, {
        title: '当前阶段',
        key: 'stage',
        width: 100,
      }, {
        title: '所占比例',
        key: 'proportion',
        width: 100,
      }, {
        title: '计划付款时间',
        key: 'paymentTime',
        width: 100,
      },
      {
        title: '支付方式',
        key: 'payMethod',
        width: 100,
        render: (row: any) => {
          switch (row.payMethod) {
            case '1':
              return h(NTag, {type: 'info', size: 'small'}, () => '现金支付')
            case '2':
              return h(NTag, {type: 'warning', size: 'small'}, () => '复明工程')
            default:
              return h(NTag, {type: 'success', size: 'small'}, () => '非现金支付')
          }
        }
      },
      {
        title: '备注',
        key: 'abst',
        width: 200,
        render: (row: any) => {
          return (
              row.appyerDeptName +
              '：' +
              row.appyerName +
              ',申请：' +
              row.ctCode +
              ',' +
              row.stage +
              '阶段：' +
              '执行比例：' +
              row.proportion +
              '%' +
              ',总额：' +
              row.totalAmt
          )
        },
      },
    ]
  } else if (reimType == '7') {    //折旧
    return []
  } else if (reimType == '8') {     //零星采购
    return [
      {
        title: '#',
        key: 'key',
        width: 50,
        render: (_, index) => {
          return `${index + 1}`
        }
      },
      {
        title: '报销标识',
        key: 'id',
        width: 100,
      },
      {
        title: '申请人',
        key: 'appyer',
        width: 100,
        render: (row: any) => row.appyerName
      },
      {title: '申请时间', key: 'appyerTime', width: 150},
      {title: '付款时间', key: 'paidTime', width: 150},
      {title: '合计金额小写(元)', key: 'sum', width: 140},
      {
        title: '业务状态', key: 'busstas', width: 100, show: false,
        render: (row: any) => busstasRender(row)
      },
    ]
  } else if (reimType == '9') {
    // 科研
    return [
      {
        title: '#',
        key: 'key',
        width: 50,
        render: (_, index) => {
          return `${index + 1}`
        }
      },
      {
        title: '报销标识',
        key: 'id',
        width: 100,
      },
      {
        title: '项目名称',
        key: 'projectName',
        width: 100,
      },
      {
        title: '项目负责人',
        key: 'projectLeader',
        width: 100,
      }, {
        title: '项目申报级别',
        key: 'projectLevel',
        width: 100,
      }, {
        title: '课题类别',
        key: 'topicCategory',
        width: 100,
      }, {
        title: '计划付款时间',
        key: 'schedulePayTime',
        width: 100,
      },
    ]
  } else if (reimType == '10') {
    //物资采购
    return [
      {
        title: '#',
        key: 'key',
        width: 50,
        render: (_, index) => {
          return `${index + 1}`
        }
      },
      {
        title: '报销标识',
        key: 'id',
        width: 100,
      },
      {
        title: '申请人',
        key: 'appyer',
        width: 100,
        render: (row: any) => row.appyerName
      },
      {title: '申请时间', key: 'appyerTime', width: 150},
      {title: '付款时间', key: 'paidTime', width: 150},
      {title: '合计金额小写(元)', key: 'sum', width: 140},
      {
        title: '业务状态', key: 'busstas', width: 100, show: false,
        render: (row: any) => busstasRender(row)
      },
    ]
  }
  return []
}

//获取待生成凭证表格列
const getColumns = (tabType: string, curType?: string): CRUDColumnInterface[] => {
  let selection = {title: '#', key: 'selection', type: ContainerValueType.SELECTION, show: false}
  let midColumns = getMidColumns(curType)
  let option = {
    title: '操作',
    key: 'operate',
    width: 120,
    fixed: 'right',
    align: 'center',
    render: (row: any) => {
      let renderItem = [h(
          'span',
          {
            style: JPGlobal.linkStyle,
            onclick: () => {
              console.log('@@-查询合同详情')
              console.log('===== 点击报销按钮 =====')
              console.log('当前报销记录完整数据:', row)
              console.log('合同编码:', row.ctCode)
              console.log('合同名称:', row.ctName)
              console.log('合同总额:', row.totalAmt)
              console.log('合同相对方:', row.oppositeName)
              console.log('付款方式:', row.paymentType)
              console.log('当前阶段:', row.stage)
              console.log('所占比例:', row.proportion)
              console.log('============================')
              //是新报销还是旧报销
              if (row.processInstanceId) {
                processDetailVisible.value = true
                currentProcessInstanceId.value = row.processInstanceId
              } else {
                // 查询项目和补助项目
                queryItemDetail({id: row.id, attCode: row.attCode}).then((res: any) => {
                  details.value.formData = row
                  details.value.itemDetails = res.data.itemDetails
                  details.value.subsItemDetails = res.data.subsItemDetails
                  details.value.psnDetails = res.data.psnDetails
                  details.value.reimAsstDetails = res.data.reimAsstDetails
                  details.value.fileRecords = res.data.fileRecords
                  reimLastNoShow.value = true
                  apprTitle.value = reimOptions.find((item: any) => item.value == row.type)?.label + '报销审批表'
                  view.value = true
                  showDetail.value = true
                })
              }
            }
          },
          '查看报销'
      ),]
      if (tabType == '1') {
        renderItem.push(
            h('span',
                {
                  style: {textDecoration: 'underline #18a058', color: '#18a058', cursor: 'pointer', marginLeft: '10px'},
                  show: tabType == '1',
                  onclick: () => {
                    //展示凭证
                    showVcrPane.value = false
                    router.push({path: '/erp/vcrBox/vcrBox', query: {idpzh: row.idpzh}})
                  }
                },
                '查看凭证'
            ))
      }
      return renderItem
    }
  }
  if (tabType == '0') {
    return [selection, ...midColumns, option]
  }
  let pzColumns = [{
    title: '凭证号',
    key: 'pzh',
    width: 100,
  }, {
    title: '凭证id',
    key: 'idpzh',
    width: 100,
  }, {
    title: '会计期间',
    key: 'kjqj',
    width: 100,
  }]
  return [...pzColumns, ...midColumns, option]
}

const tabChange = (tab: JTab) => {
  queryForm.value.genFlag = tab.name
}

//清除vcrPane数据
const resetVcrPane = () => {
  checkRowKeys.value = []
}

const confirm = () => {
  vcrDetailRef.value.vcrGen()
}

//费用类型
const reimOptions = [
  {
    value: '1',
    label: '差旅费'
  },
  {
    value: '2',
    label: '培训费',
  },
  {
    value: '3',
    label: '其他费用'
  },
  {
    value: '4',
    label: '分摊费用'
  },
  {
    label: '工资',
    value: '5',
  },
  {
    label: '合同',
    value: '6',
  },
  {
    label: '零星采购',
    value: '8'
  },
  {
    label: '科研',
    value: '9'
  },
  {
    label: '物资采购',
    value: '10'
  },
  {
    label: '其他费用(无发票)',
    value: '11',
  },
  {
    label: '往来支付',
    value: '12',
  },
  {
    label: '借款',
    value: '13',
  },
  {
    label: '临床试验经费',
    value: '14',
  }
]

const close = () => {
  showVcrFill.value = false
  crudRef.value.queryData()
}

const tabs = ref<JTab[]>([
  {
    name: '0',
    tab: '未生成',
    columns: getColumns('0'),
    tabChange: tabChange,
  },
  {
    name: '1',
    tab: '已生成',
    columns: getColumns('1'),
    tabChange: tabChange,
  }
])

//清除vcrform数据
const resetVcrForm = () => {
}

//展示报销审批表
const showReimInfo = () => {
  if (checkRowKeys.value.length == 0) {
    window.$message.warning('当前未选择报销项')
    return
  }

  msgBtnType.value = '1'
  queryExtraForm.value.type = reimType.value
  queryExtraForm.value.ids = checkRowKeys.value
  queryReimInfo(queryExtraForm.value).then((res: any) => {
    if (res.code == 200) {
      showChoosedExtraMsg.value = true
      chooseExtraData.value = res.data
    }
  })
}

onMounted(() => {
  queryForm.value.type = props.type
  reimType.value = props.type
  vcrPaneTitle.value = reimOptions.find((item: any) => item.value == props.type)?.label + '报销审批表'
  tabs.value[0].columns = getColumns('0', reimType.value)
  tabs.value[1].columns = getColumns('1', reimType.value)
  queryDictData({codeType: 'REGION'}).then(res => {
    codeData.value = res.data
  })
  queryContractType({})
      .then((res: any) => {
        if (res.code === 200 && Array.isArray(res.data)) {
          contractTypeNoTree.value = res.data
        } else {
          console.warn('获取合同类型数据异常:', res)
          contractTypeNoTree.value = [] // 确保是数组
        }
      })
      .catch(err => {
        console.error('获取合同类型失败:', err)
        contractTypeNoTree.value = [] // 确保是数组
      })
  // 获取付款方式字典
  const dictData = JPGlobal.getDictByType('PAYMENT_TYPE')
  if (Array.isArray(dictData)) {
    paymentTypeDict.value = dictData
  } else {
    console.warn('获取付款方式字典异常:', dictData)
    paymentTypeDict.value = [] // 确保是数组
  }
  console.log('付款方式字典数据:', paymentTypeDict.value)
})
</script>
<script lang="ts">
export default {
  name: '费用报销凭证生成'
}
</script>