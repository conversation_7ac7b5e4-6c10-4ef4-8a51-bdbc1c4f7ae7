<template>
  <n-modal :style="{ width: props.width, marginTop: '4vh', height: '94vh' }" v-model:show="show" :z-index="10">
    <div style="overflow-y: auto">
      <ProcessInstanceCreate
        :processDefinitionKey="props.processDefinitionKey"
        :processInstanceId="props.processInstanceId"
        :otherProps="props.otherProps"
        @close="handleClose"
      />
    </div>
  </n-modal>
</template>
<script setup lang="ts">
  import ProcessInstanceCreate from '@/views/modules/bpm/processInstance/create/index.vue'

  import { ref, watch } from 'vue'

  const props = defineProps({
    processDefinitionKey: {
      type: String,
      default: '',
    },
    processInstanceId: {
      type: String,
      default: '',
    },
    
    otherProps: {
      type: Object,
      default: () => {},
    },
    width: {
      type: String,
      default: '90%',
    },
  })
  const emits = defineEmits(['close'])

  const show = defineModel('show', { type: Boolean, default: false })
  const handleClose = () => {
    show.value = false
    emits('close')
  }
</script>
<script lang="ts">
  import { defineComponent } from 'vue'

  export default defineComponent({
    name: 'ProcessInstanceCreateModal',
  })
</script>
