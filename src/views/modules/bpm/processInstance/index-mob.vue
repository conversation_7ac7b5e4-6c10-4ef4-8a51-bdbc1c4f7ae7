<template>
  <BpmListTemplate
    :list="list"
    :total="total"
    :loading="loading"
    :queryParams="queryParams"
    @query="handleQuery"
    @reset="resetQuery"
    @load-more="loadMore"
    @quick-search="handleQuickSearch"
    ref="listTemplateRef"
  >
    <!-- 筛选条件插槽 -->
    <template #filters="{ queryParams }">
      <n-form-item label="流程分类">
        <n-select 
          v-model:value="queryParams.category" 
          placeholder="请选择流程分类" 
          clearable
          size="medium"
        >
          <n-option
            v-for="category in categoryList"
            :key="category.code"
            :label="category.name"
            :value="category.code"
          />
        </n-select>
      </n-form-item>
      
      <n-form-item label="流程状态">
        <n-select 
          v-model:value="queryParams.status" 
          placeholder="请选择流程状态" 
          clearable
          size="medium"
        >
          <n-option
            v-for="dict in getIntDictOptions(DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </n-select>
      </n-form-item>
      
      <n-form-item label="发起时间">
        <RangePicker
          v-model:value="queryParams.createTime"
          :placeholder="['开始日期', '结束日期']"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          class="w-full"
          :allowClear="true"
          show-time
        />
      </n-form-item>
    </template>

    <!-- 顶部操作按钮插槽 -->
    <template #actions>
      <n-button 
        type="primary" 
        @click="handleCreate(undefined)"
        v-hasPermi="['bpm:process-instance:query']"
        class="create-btn"
        size="medium"
      >
        <template #icon>
          <n-icon><add-outline /></n-icon>
        </template>
        发起流程
      </n-button>
    </template>

    <!-- 卡片内容插槽 -->
    <template #card="{ item }">
      <div class="process-card" @click="handleDetail(item)">
        <!-- 卡片头部 -->
        <div class="card-header">
          <div class="process-info">
            <h3 class="process-name">{{ item.name }}</h3>
            <div class="process-meta">
              <span class="business-key" v-if="item.businessKey">
                {{ item.businessKey }}
              </span>
              <span class="category-name">{{ item.categoryName }}</span>
            </div>
          </div>
          <div class="status-badge">
            <dict-tag 
              :type="DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS" 
              :value="item.status" 
            />
          </div>
        </div>
        
        <!-- 卡片内容 -->
        <div class="card-content">
          <div class="time-info">
            <div class="time-item">
              <span class="time-label">发起时间</span>
              <span class="time-value">{{ formatTime(item.startTime) }}</span>
            </div>
            <div class="time-item" v-if="item.endTime">
              <span class="time-label">结束时间</span>
              <span class="time-value">{{ formatTime(item.endTime) }}</span>
            </div>
            <div class="time-item" v-if="item.durationInMillis > 0">
              <span class="time-label">耗时</span>
              <span class="time-value">{{ formatPast2(item.durationInMillis) }}</span>
            </div>
          </div>
          
          <!-- 当前审批任务 -->
          <div class="current-tasks" v-if="item.tasks && item.tasks.length > 0">
            <div class="tasks-label">当前审批任务</div>
            <div class="task-tags">
              <n-tag 
                v-for="task in item.tasks" 
                :key="task.id" 
                type="info" 
                size="small"
                class="task-tag"
              >
                {{ task.name }}
              </n-tag>
            </div>
          </div>
        </div>
        
        <!-- 卡片操作 -->
        <div class="card-actions" @click.stop>
          <n-button 
            text 
            type="primary" 
            size="small"
            @click="handleDetail(item)"
          >
            详情
          </n-button>
          <n-button
            text
            type="primary"
            size="small"
            v-if="item.status === 1"
            v-hasPermi="['bpm:process-instance:query']"
            @click="handleCancel(item)"
          >
            取消
          </n-button>
          <n-button 
            text 
            type="primary" 
            size="small"
            v-else 
            @click="handleCreate(item)"
          > 
            重新发起 
          </n-button>
        </div>
      </div>
    </template>
  </BpmListTemplate>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onActivated } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import { AddOutline } from '@vicons/ionicons5'
import { DatePicker } from 'ant-design-vue'
import { getIntDictOptions, DICT_TYPE } from '@/utils/bpmAdapter/bpmDictAdapter'
import { dateFormatter, formatPast2 } from '@/utils/bpmAdapter/formatTime'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import { CategoryApi } from '@/api/bpm/category'
import { ProcessInstanceVO } from '@/api/bpm/processInstance'
import * as DefinitionApi from '@/api/bpm/definition'
import { useMessage } from '@/components/common/bpm/bpmAdapter/useMessage'
import BpmListTemplate from '@/components/mobile/BpmListTemplate.vue'
const { RangePicker } = DatePicker

defineOptions({
  name: 'BpmProcessInstanceMyMobile'
})

const router = useRouter()
const message = useMessage()
const listTemplateRef = ref()

// 响应式数据
const loading = ref(true)
const list = ref([])
const total = ref(0)
const categoryList = ref<any[]>([])

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: '',
  processDefinitionId: undefined,
  category: undefined,
  status: undefined,
  createTime: [] as any,
})

// 方法
const formatTime = (time: string) => {
  if (!time) return '-'
  return dateFormatter(null, null, time)
}

/** 查询列表 */
const getList = async (isLoadMore = false) => {
  if (isLoadMore) {
    // 加载更多时不显示loading
  } else {
    loading.value = true
    queryParams.pageNo = 1
    list.value = []
  }

  try {
    const createTime = Array.isArray(queryParams.createTime) ? queryParams.createTime.join(',') : ''
    const data = await ProcessInstanceApi.getProcessInstanceMyPage({
      ...queryParams,
      createTime
    })

    if (isLoadMore) {
      list.value.push(...data.list)
    } else {
      list.value = data.list
    }
    total.value = data.total
  } finally {
    loading.value = false
    listTemplateRef.value?.stopLoadingMore()
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 快速搜索 */
const handleQuickSearch = (keyword: string) => {
  queryParams.name = keyword
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  Object.assign(queryParams, {
    pageNo: 1,
    pageSize: 10,
    name: '',
    processDefinitionId: undefined,
    category: undefined,
    status: undefined,
    createTime: [],
  })
  handleQuery()
}

/** 加载更多 */
const loadMore = () => {
  queryParams.pageNo += 1
  getList(true)
}

/** 发起流程操作 **/
const handleCreate = async (row?: ProcessInstanceVO) => {
  // 如果是【业务表单】，不支持重新发起
  if (row?.id) {
    const processDefinitionDetail = await DefinitionApi.getProcessDefinition(row.processDefinitionId)
    if (processDefinitionDetail.formType === 20) {
      message.error('重新发起流程失败，原因：该流程使用业务表单，不支持重新发起')
      return
    }
  }
  // 跳转发起流程界面
  await router.push({
    path: '/bpm/processInstance/create/index',
    query: { processInstanceId: row?.id },
  })
}

/** 查看详情 */
const handleDetail = (row: any) => {
  // 移动端使用页面跳转而不是弹窗
  router.push({
    path: '/bpm/processInstance/detail/index',
    query: {
      id: row.id,
    },
  })
}

/** 取消按钮操作 */
const handleCancel = async (row: any) => {
  // 二次确认
  const { value } = await ElMessageBox.prompt('请输入取消原因', '取消流程', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
    inputErrorMessage: '取消原因不能为空',
  })
  // 发起取消
  await ProcessInstanceApi.cancelProcessInstanceByStartUser(row.id, value)
  message.success('取消成功')
  // 刷新列表
  await getList()
}

/** 激活时 **/
onActivated(() => {
  getList()
})

/** 初始化 **/
onMounted(async () => {
  await getList()
  categoryList.value = await CategoryApi.getCategorySimpleList()
})
</script>

<style scoped>
@reference "tailwindcss";

/* 流程卡片样式 */
.process-card {
  @apply bg-white rounded-lg border border-gray-200 p-4 shadow-sm active:bg-gray-50 transition-colors;
}

.card-header {
  @apply flex items-start justify-between mb-3;
}

.process-info {
  @apply flex-1 mr-3;
}

.process-name {
  @apply text-base font-semibold text-gray-900 mb-1 line-clamp-2;
}

.process-meta {
  @apply flex items-center gap-2 text-sm text-gray-500;
}

.business-key {
  @apply bg-blue-50 text-blue-600 px-2 py-1 rounded text-xs;
}

.category-name {
  @apply text-gray-600;
}

.status-badge {
  @apply flex-shrink-0;
}

/* 卡片内容 */
.card-content {
  @apply space-y-3;
}

.time-info {
  @apply grid grid-cols-1 gap-2 text-sm;
}

.time-item {
  @apply flex justify-between items-center;
}

.time-label {
  @apply text-gray-500;
}

.time-value {
  @apply text-gray-900 font-medium;
}

/* 当前任务 */
.current-tasks {
  @apply space-y-2;
}

.tasks-label {
  @apply text-sm font-medium text-gray-700;
}

.task-tags {
  @apply flex flex-wrap gap-1;
}

.task-tag {
  @apply text-xs;
}

/* 卡片操作 */
.card-actions {
  @apply flex items-center justify-end gap-3 mt-4 pt-3 border-t border-gray-100;
}

.create-btn {
  @apply px-4 py-2 rounded-lg;
}

/* 响应式优化 */
@media (max-width: 480px) {
  .process-card {
    @apply p-3;
  }
  
  .process-name {
    @apply text-sm;
  }
  
  .time-info {
    @apply text-xs;
  }
}
</style>
