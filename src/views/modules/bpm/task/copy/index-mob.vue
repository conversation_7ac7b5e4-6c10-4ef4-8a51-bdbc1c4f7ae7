<template>
  <BpmListTemplate
    :list="list"
    :total="total"
    :loading="loading"
    :queryParams="queryParams"
    @query="handleQuery"
    @reset="resetQuery"
    @load-more="loadMore"
    @quick-search="handleQuickSearch"
    ref="listTemplateRef"
  >
    <!-- 筛选条件插槽 -->
    <template #filters="{ queryParams }">
      <n-form-item label="抄送时间">
        <RangePicker
          v-model:value="queryParams.createTime"
          :placeholder="['开始日期', '结束日期']"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          class="w-full"
          :allowClear="true"
          show-time
        />
      </n-form-item>
    </template>

    <!-- 卡片内容插槽 -->
    <template #card="{ item }">
      <div class="copy-task-card" @click="handleAudit(item)">
        <!-- 卡片头部 -->
        <div class="card-header">
          <div class="task-info">
            <h3 class="task-name">{{ item.processInstanceName }}</h3>
            <div class="task-meta">
              <span class="copy-tag">抄送</span>
              <span class="task-name-tag">{{ item.taskName }}</span>
            </div>
          </div>
          <div class="time-badge">
            <n-tag type="info" size="small">
              {{ formatTime(item.createTime) }}
            </n-tag>
          </div>
        </div>
        
        <!-- 卡片内容 -->
        <div class="card-content">
          <div class="info-grid">
            <div class="info-item">
              <span class="info-label">流程发起人</span>
              <span class="info-value">{{ item.startUserName || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">流程发起时间</span>
              <span class="info-value">{{ formatTime(item.processInstanceStartTime) }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">抄送人</span>
              <span class="info-value">{{ item.creatorName || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">抄送时间</span>
              <span class="info-value">{{ formatTime(item.createTime) }}</span>
            </div>
          </div>
        </div>
        
        <!-- 卡片操作 -->
        <div class="card-actions" @click.stop>
          <n-button 
            type="primary" 
            size="small"
            @click="handleAudit(item)"
            block
          >
            查看详情
          </n-button>
        </div>
      </div>
    </template>
  </BpmListTemplate>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { DatePicker } from 'ant-design-vue'
import { dateFormatter } from '@/utils/bpmAdapter/formatTime'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import BpmListTemplate from '@/components/mobile/BpmListTemplate.vue'

const { RangePicker } = DatePicker

defineOptions({
  name: 'BpmProcessInstanceCopyMobile'
})

const router = useRouter()
const listTemplateRef = ref()

// 响应式数据
const loading = ref(false)
const list = ref([])
const total = ref(0)

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  processInstanceId: '',
  processInstanceName: '',
  createTime: [] as any,
})

// 方法
const formatTime = (time: string) => {
  if (!time) return '-'
  return dateFormatter(null, null, time)
}

/** 查询任务列表 */
const getList = async (isLoadMore = false) => {
  if (isLoadMore) {
    // 加载更多时不显示loading
  } else {
    loading.value = true
    queryParams.pageNo = 1
    list.value = []
  }

  try {
    const createTime = Array.isArray(queryParams.createTime) ? queryParams.createTime.join(',') : ''
    const data = await ProcessInstanceApi.getProcessInstanceCopyPage({ ...queryParams, createTime })
    
    if (isLoadMore) {
      list.value.push(...data.list)
    } else {
      list.value = data.list
    }
    total.value = data.total
  } finally {
    loading.value = false
    listTemplateRef.value?.stopLoadingMore()
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 快速搜索 */
const handleQuickSearch = (keyword: string) => {
  queryParams.processInstanceName = keyword
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  Object.assign(queryParams, {
    pageNo: 1,
    pageSize: 10,
    processInstanceId: '',
    processInstanceName: '',
    createTime: [],
  })
  handleQuery()
}

/** 加载更多 */
const loadMore = () => {
  queryParams.pageNo += 1
  getList(true)
}

/** 处理审批按钮 */
const handleAudit = (row: any) => {
  router.push({
    path: '/bpm/processInstance/detail/index',
    query: {
      id: row.processInstanceId,
    },
  })
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>

<style scoped>
@reference "tailwindcss";

/* 抄送任务卡片样式 */
.copy-task-card {
  @apply bg-white rounded-lg border border-gray-200 p-4 shadow-sm active:bg-gray-50 transition-colors;
}

.card-header {
  @apply flex items-start justify-between mb-3;
}

.task-info {
  @apply flex-1 mr-3;
}

.task-name {
  @apply text-base font-semibold text-gray-900 mb-1 line-clamp-2;
}

.task-meta {
  @apply flex items-center gap-2 text-sm text-gray-500;
}

.copy-tag {
  @apply bg-purple-50 text-purple-600 px-2 py-1 rounded text-xs;
}

.task-name-tag {
  @apply bg-gray-50 text-gray-600 px-2 py-1 rounded text-xs;
}

.time-badge {
  @apply flex-shrink-0;
}

/* 卡片内容 */
.card-content {
  @apply space-y-3;
}

.info-grid {
  @apply grid grid-cols-1 gap-2 text-sm;
}

.info-item {
  @apply flex justify-between items-center;
}

.info-label {
  @apply text-gray-500;
}

.info-value {
  @apply text-gray-900 font-medium;
}

/* 卡片操作 */
.card-actions {
  @apply mt-4 pt-3 border-t border-gray-100;
}

/* 响应式优化 */
@media (max-width: 480px) {
  .copy-task-card {
    @apply p-3;
  }
  
  .task-name {
    @apply text-sm;
  }
  
  .info-grid {
    @apply text-xs;
  }
}
</style>
