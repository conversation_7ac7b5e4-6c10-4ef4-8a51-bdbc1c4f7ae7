<template>
  <BpmListTemplate
    :list="list"
    :total="total"
    :loading="loading"
    :queryParams="queryParams"
    @query="handleQuery"
    @reset="resetQuery"
    @load-more="loadMore"
    @quick-search="handleQuickSearch"
    ref="listTemplateRef"
  >
    <!-- 筛选条件插槽 -->
    <template #filters="{ queryParams }">
      <n-form-item label="流程分类">
        <n-select 
          v-model:value="queryParams.processCategory" 
          placeholder="请选择流程分类" 
          clearable
          size="medium"
        >
          <n-option
            v-for="category in categoryList"
            :key="category.code"
            :label="category.name"
            :value="category.code"
          />
        </n-select>
      </n-form-item>
      
      <n-form-item label="业务标识">
        <n-input
          v-model:value="queryParams.businessKey"
          placeholder="请输入业务标识"
          clearable
          size="medium"
        />
      </n-form-item>
      
      <n-form-item label="发起人">
        <n-select 
          v-model:value="queryParams.empCode" 
          placeholder="请选择发起人" 
          filterable 
          clearable
          size="medium"
        >
          <n-option 
            v-for="user in userList" 
            :key="user.id" 
            :label="user.empName" 
            :value="user.empCode" 
          />
        </n-select>
      </n-form-item>
      
      <n-form-item label="创建时间">
        <RangePicker
          v-model:value="queryParams.createTime"
          :placeholder="['开始日期', '结束日期']"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          class="w-full"
          :allowClear="true"
          show-time
        />
      </n-form-item>
    </template>

    <!-- 卡片内容插槽 -->
    <template #card="{ item }">
      <div class="task-card" @click="handleAudit(item)">
        <!-- 卡片头部 -->
        <div class="card-header">
          <div class="task-info">
            <h3 class="task-name">{{ item.processInstance.name }}</h3>
            <div class="task-meta">
              <span class="business-key" v-if="item.processInstance.businessKey">
                {{ item.processInstance.businessKey }}
              </span>
              <span class="current-task">{{ item.name }}</span>
            </div>
          </div>
          <div class="priority-badge">
            <n-tag type="warning" size="small">
              待办
            </n-tag>
          </div>
        </div>
        
        <!-- 卡片内容 -->
        <div class="card-content">
          <div class="info-grid">
            <div class="info-item">
              <span class="info-label">发起人</span>
              <span class="info-value">{{ item.processInstance.startUser?.empName || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">发起时间</span>
              <span class="info-value">{{ formatTime(item.processInstance.startTime) }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">任务时间</span>
              <span class="info-value">{{ formatTime(item.createTime) }}</span>
            </div>
          </div>
          
          <!-- 发起原因 -->
          <div class="reason-section" v-if="item.createReason">
            <div class="reason-label">发起原因</div>
            <div class="reason-content">{{ item.createReason }}</div>
          </div>
        </div>
        
        <!-- 卡片操作 -->
        <div class="card-actions" @click.stop>
          <n-button 
            type="primary" 
            size="small"
            @click="handleAudit(item)"
            block
          >
            立即办理
          </n-button>
        </div>
      </div>
    </template>
  </BpmListTemplate>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, inject, type Ref } from 'vue'
import { useRouter } from 'vue-router'
import { DatePicker } from 'ant-design-vue'
import { dateFormatter } from '@/utils/bpmAdapter/formatTime'
import * as TaskApi from '@/api/bpm/task'
import { CategoryApi } from '@/api/bpm/category'
import * as UserApi from '@/api/bpm/bpmAdapter/user.ts'
import { IPageRes } from '@jtypes'
import { querySelection } from '@/api/hrm/hrmEmp.ts'
import BpmListTemplate from '@/components/mobile/BpmListTemplate.vue'

const { RangePicker } = DatePicker

defineOptions({
  name: 'BpmTodoTaskMobile'
})

const router = useRouter()
const listTemplateRef = ref()
const isMobileDevice = inject<Ref<Boolean>>('isMobileDevice')

// 响应式数据
const loading = ref(true)
const list = ref([])
const total = ref(0)
const categoryList = ref<any[]>([])
const userList = ref<any[]>([])

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: '',
  businessKey: '',
  processCategory: '',
  createTime: [] as any,
  empCode: '',
})

// 方法
const formatTime = (time: string) => {
  if (!time) return '-'
  return dateFormatter(null, null, time)
}

/** 查询任务列表 */
const getList = async (isLoadMore = false) => {
  if (isLoadMore) {
    // 加载更多时不显示loading
  } else {
    loading.value = true
    queryParams.pageNo = 1
    list.value = []
  }

  try {
    const createTime = Array.isArray(queryParams.createTime) ? queryParams.createTime.join(',') : ''
    const data = await TaskApi.getTaskTodoPage({ ...queryParams, createTime })
    
    if (isLoadMore) {
      list.value.push(...data.list)
    } else {
      list.value = data.list
    }
    total.value = data.total
  } finally {
    loading.value = false
    listTemplateRef.value?.stopLoadingMore()
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 快速搜索 */
const handleQuickSearch = (keyword: string) => {
  queryParams.name = keyword
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  Object.assign(queryParams, {
    pageNo: 1,
    pageSize: 10,
    name: '',
    businessKey: '',
    processCategory: '',
    createTime: [],
    empCode: '',
  })
  handleQuery()
}

/** 加载更多 */
const loadMore = () => {
  queryParams.pageNo += 1
  getList(true)
}

/** 处理审批按钮 */
const handleAudit = (row: any) => {
  router.push({
    path: '/bpm/processInstance/detail/index',
    query: {
      id: row.processInstance.id,
    },
  })
}

/** 初始化 **/
onMounted(async () => {
  await getList()
  categoryList.value = await CategoryApi.getCategorySimpleList()
  
  // 加载用户列表
  try {
    const res: IPageRes = await querySelection({
      pageSize: 2000,
      pageNum: 1,
      empCodeOrEmpName: '',
      orgId: '',
      hideLoadingBar: true,
      restrictedEmpType: null,
      restrictedEmps: null,
    })
    userList.value = res.data.records
  } catch (error) {
    console.error('加载用户列表失败:', error)
  }
})
</script>

<style scoped>
@reference "tailwindcss";

/* 任务卡片样式 */
.task-card {
  @apply bg-white rounded-lg border border-gray-200 p-4 shadow-sm active:bg-gray-50 transition-colors;
}

.card-header {
  @apply flex items-start justify-between mb-3;
}

.task-info {
  @apply flex-1 mr-3;
}

.task-name {
  @apply text-base font-semibold text-gray-900 mb-1 line-clamp-2;
}

.task-meta {
  @apply flex items-center gap-2 text-sm text-gray-500;
}

.business-key {
  @apply bg-blue-50 text-blue-600 px-2 py-1 rounded text-xs;
}

.current-task {
  @apply bg-orange-50 text-orange-600 px-2 py-1 rounded text-xs;
}

.priority-badge {
  @apply flex-shrink-0;
}

/* 卡片内容 */
.card-content {
  @apply space-y-3;
}

.info-grid {
  @apply grid grid-cols-1 gap-2 text-sm;
}

.info-item {
  @apply flex justify-between items-center;
}

.info-label {
  @apply text-gray-500;
}

.info-value {
  @apply text-gray-900 font-medium;
}

/* 发起原因 */
.reason-section {
  @apply space-y-1;
}

.reason-label {
  @apply text-sm font-medium text-gray-700;
}

.reason-content {
  @apply text-sm text-gray-600 bg-gray-50 p-2 rounded;
}

/* 卡片操作 */
.card-actions {
  @apply mt-4 pt-3 border-t border-gray-100;
}

/* 响应式优化 */
@media (max-width: 480px) {
  .task-card {
    @apply p-3;
  }
  
  .task-name {
    @apply text-sm;
  }
  
  .info-grid {
    @apply text-xs;
  }
}
</style>
