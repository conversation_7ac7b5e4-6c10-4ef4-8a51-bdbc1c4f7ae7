<template>
  <div class="funding-apply-container">
    <div class="form-container">
      <h1 class="form-title">学术著作出版申请备案

        <n-tooltip trigger="hover" v-if="formData.id">
          <template #trigger>
            <n-icon size="18" class="preview-icon" @click="handlePreview(formData.id)">
              <EyeOutline />
            </n-icon>
          </template>
          预览
        </n-tooltip>
      </h1>

      <n-h4 prefix="bar">
            <n-flex justify="space-between" align="center">
              <strong
                >基本信息
              </strong>
            </n-flex>
      </n-h4>
      <n-form ref="formRef" :model="formData" :rules="rules" label-placement="left" label-width="120"
        require-mark-placement="right-hanging" :disabled="formDisabled">
        <!-- 基本信息部分 -->
        <n-grid :cols="3" :x-gap="24">
          <n-grid-item>
            <n-form-item label="专业科室" path="department">
              <j-bus-hos-org v-model:value="formData.department" :disabled="formDisabled" :clearable="true" />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="申请人" path="mainResearcher">
              <j-bus-emp-search v-model:value="formData.mainResearcher" :disabled="formDisabled" :multiple="true"
                :auto-select="false" />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="学历" path="education">
              <n-input v-model:value="formData.education" placeholder="请输入学历" />
            </n-form-item>
          </n-grid-item>
        </n-grid>


        <!-- 编者排位 -->
        <n-grid :cols="3" :x-gap="20">
          <n-grid-item>
            <!-- 暂时用空格占位进行对齐 -->
            <span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
            <span>编者：第 </span>
            <n-input v-model:value="formData.editorRankZhu" placeholder="请输入" :disabled="formDisabled"
              style="width: 60px; display: inline-block;" />
            <span>主编</span>
          </n-grid-item>
          <n-grid-item>
            <span>第 </span>
            <n-input v-model:value="formData.editorRankFu" placeholder="请输入" :disabled="formDisabled"
              style="width: 60px; display: inline-block;" />
            <span>副主编</span>
          </n-grid-item>
          <n-grid-item>
            <span>第 </span>
            <n-input v-model:value="formData.editorRankCan" placeholder="请输入" :disabled="formDisabled"
              style="width: 60px; display: inline-block;" />
            <span>参编</span>
          </n-grid-item>
        </n-grid>

        
        <n-grid :cols="2" :x-gap="24">
          <n-grid-item>
            <n-form-item label="著作名称" path="thesisName" style="margin-top: 20px;">
              <n-input v-model:value="formData.thesisName" placeholder="请输入著作名称" />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="著作类型" path="publicationType" style="margin-top: 20px;">
              <n-space vertical>
                <n-select v-model:value="formData.publicationType" filterable placeholder="请选择著作类型"
                  :options="publicationTypeOptions" />
              </n-space>
            </n-form-item>
          </n-grid-item>
        </n-grid>


        <n-grid :cols="2" :x-gap="24">
          <n-grid-item>
            <n-form-item label="主要章节" path="mainChapter" style="margin-top: 20px;">
              <n-input v-model:value="formData.mainChapter" placeholder="请输入著作主要章节" />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="字数" path="wordCount" style="margin-top: 20px;">
              <n-input v-model:value="formData.wordCount" placeholder="请输入总字数" />
            </n-form-item>
          </n-grid-item>
        </n-grid>

        <n-grid :cols="2" :x-gap="24">
          <n-grid-item>
            <n-form-item label="出版社名称" path="publisherName" style="margin-top: 20px;">
              <n-input v-model:value="formData.publisherName" placeholder="请输入" />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="出版社类型" path="publisherType" style="margin-top: 20px;">
              <n-space vertical>
                <n-select v-model:value="formData.publisherType" filterable placeholder="请选择著作类型"
                  :options="publisherTypeOptions" />
              </n-space>
            </n-form-item>
          </n-grid-item>
        </n-grid>

        <n-h4 prefix="bar">
            <n-flex justify="space-between" align="center">
              <strong
                >学术著作内容简介
              </strong>
            </n-flex>
      </n-h4>
      <i>(写明目前国内外的研究现状、选题意义、结构体系、主要观点和创新之处，须闻明该专著与本人学科相关性)</i>
      <n-form-item label="内容简介" path="contentIntroduction">
        <n-input
          v-model:value="formData.contentIntroduction"
          type="textarea"
          placeholder="请输入内容简介"
          :autosize="{ minRows: 3, maxRows: 5 }"
        />
      </n-form-item>

      <!-- 附件上传 -->
      <n-h4 prefix="bar"><strong>附件上传</strong></n-h4>
      <div class="performance-section-header">
        <span class="section-title">附件</span>
        <div
          class="upload-wrapper"
          :style="{ backgroundColor: formData?.attFiles?.length > 0 ? '#8dd46c' : '#f5f7fa' }"
        >
          <span class="upload-text">相关附件</span>
          <!-- 文件上传和预览组件 -->
          <component :is="fileUploadComp" />
        </div>
      </div>
      </n-form>
      

      <!-- 提交按钮 -->
      <div class="form-actions" v-if="!formDisabled">
        <n-button type="primary" @click="handleSubmit" :loading="submitLoding" :disabled="submitLoding">提交申请</n-button>
        <n-button @click="handleReset" :disabled="submitLoding">重置表单</n-button>
      </div>
    </div>

    <!-- 签名 -->
    <j-modal title="签名" v-model:show="showSign" width="20%" :show-btn="false">
      <template #content>
        <j-sign @done="signDone" placeholder="请输入个人签章密码" />
      </template>
    </j-modal>

        <!-- 预览文件 -->
    <Preview v-model:show="showPreview" :oss-path="previewOssPath" :oss-path-name="previewOssPathName"
      :bucket="bucket" />


      
  </div>
</template>

<script lang="ts" setup>
  import { useForm } from './useForm'
  // import { useTable } from './useTable'
  import { EyeOutline } from '@vicons/ionicons5'
  import { ref } from 'vue' // 导入 ref
  import './styles.scss'


  // 定义假数据
  const publicationTypeOptions = ref([
      { label: '专著', value: '专著' },
      { label: '编著', value: '编著' },
      { label: '译著', value: '译著' }
    ]);

const publisherTypeOptions = ref([
      { label: '国家级出版社', value: '国家级出版社' },
      { label: '省级出版社', value: '省级出版社' }
    ]);


 

  // 在组件中定义 props
  const props = defineProps({
    // 流程标识(新增)
    processDefinitionKey: {
      type: String,
      required: false,
    },
    // 流程实例ID（重新提交）
    processInstanceId: {
      type: String,
      required: false,
    },
    // 业务表单ID（详情）
    id: {
      type: [Number, String],
      required: false,
    },
  })

  // 定义 emits
  const emits = defineEmits(['close'])

  // 将 props 传入 useForm
  const {
    formRef,
    formDisabled,
    submitLoding,
    formData,
    rules,
    handleSubmit,
    handleReset,
    showNumberButton,
    fileUploadComp,
    showSign,
    signDone,
    showPreview,
    previewOssPath,
    previewOssPathName,
    bucket,
    handlePreview,
  } = useForm(props, emits)

  watch(
    () => props,
    newVal => {
      console.log(newVal, 'props')
    },
    {
      immediate: true,
      deep: true,
    }
  )



</script>
