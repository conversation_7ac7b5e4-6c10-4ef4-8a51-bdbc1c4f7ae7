// 分配明细数据结构
export interface Distribution {
  /** 部门 */
  orgName: string
  /** 工号 */
  empCode: string
  /** 姓名 */
  empName: string
  /** 分配金额 */
  amount: number | null
}

// 表单数据结构
export interface FormState {
  /** 流程id */
  id?: string | null
  /** 流程定义key */
  processDefinitionKey?: string
  /** 论文名称 */
  thesisName?: string
  /** 所属部门 */
  department?: string
  /** 主要研究者 */
  mainResearcher?: string[]
  /** 学历 */
  education?: string
  /** 编者排位 */
  editorRankZhu?: string
  editorRankFu?: string
  editorRankCan?: string
  /** 著作类型 */
  publicationType?: string
  /** 主要构成章节 */
  mainChapter?: string
  /** 字数 */
  wordCount?: string
  /** 出版社名称 */
  publisherName?: string
  /** 出版社类型 */
  publisherType?: string
  /** 内容简介 */
  contentIntroduction?: string
  /** 保存的文件名 */
  att?: string | null
  /** 原文件名 */
  attName?: string | null
  /** 附件 */
  attFiles?: File[]
}
