<template>
  <div class="funding-apply-container">
    <div class="form-container">
      <h1 class="form-title">学术著作奖励申请

        <n-tooltip trigger="hover" v-if="formData.id">
          <template #trigger>
            <n-icon size="18" class="preview-icon" @click="handlePreview(formData.id)">
              <EyeOutline />
            </n-icon>
          </template>
          预览
        </n-tooltip>
      </h1>

      <n-form ref="formRef" :model="formData" :rules="rules" label-placement="left" label-width="120"
        require-mark-placement="right-hanging" :disabled="formDisabled">

        <n-row :gutter="20">
        <n-col :span="24">
          <j-title-line :title="reimTable.title">
            <template #default>
              <n-button  type="info" style="margin-right: 10px" @click="showClinicalTrialChoose"
                :disabled="formDisabled"
              >学术著作申请备案</n-button>
            </template>
          </j-title-line>
          <j-n-data-table
              :columns="reimTable.columns"
              :data="reimTable.data"
              :max-height="350"
              virtual-scroll
          />
        </n-col>
      </n-row>

      <j-modal
          v-model:show="showClinicalTrialChoosePane"
          width="80%"
          height="80%"
          title="专著奖励申请备案"
          :content-style="{ height: 'calc(100% - 85px -56px)' }"
          @close="showClinicalTrialChoosePane = false"
          @confirm="doChooseClinicalTrial"
        >
      
          <academic-writing ref="clinicalTrialChooseRef" @rtClinicalTrials="handleRtClinicalTrials" />
        </j-modal>

        <!-- 基本信息部分 -->
         <n-h4 prefix="bar">
          <n-flex justify="space-between" align="center">
            <strong>基本信息</strong>
          </n-flex>
        </n-h4>
        <n-grid :cols="3" :x-gap="24">
          <n-grid-item>
            <n-form-item label="专业科室" path="department">
              <j-bus-hos-org v-model:value="formData.department" :disabled="formDisabled" :clearable="true" />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="申请人" path="mainResearcher">
              <j-bus-emp-search v-model:value="formData.mainResearcher" :disabled="formDisabled" :multiple="true"
                :auto-select="false" />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="职称" path="title">
              <n-input v-model:value="formData.title" placeholder="请输入职称" />
            </n-form-item>
          </n-grid-item>
        </n-grid>


        <n-grid :cols="2" :x-gap="24">
          <n-grid-item>
            <n-form-item label="联系电话" path="phone">
              <n-input v-model:value="formData.phone" placeholder="请输入联系电话" />
            </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="在版编目(CIP)" path="catalogData">
              <n-input v-model:value="formData.catalogData" placeholder="请输入再版编目数据" />
            </n-form-item>
          </n-grid-item>
        </n-grid>

        <n-form-item label="著作名称" path="thesisName">
          <n-input v-model:value="formData.thesisName" placeholder="请输入著作名称" />
        </n-form-item>

        <!-- 编者排位 -->
        <n-grid :cols="3" :x-gap="20">
          <n-grid-item>
            <span>编者信息：第 </span>
            <n-input v-model:value="formData.editorRankZhu" placeholder="" 
              style="width: 60px; display: inline-block;" />
            <span>主编撰写&nbsp;&nbsp;</span>
            <n-input v-model:value="formData.editorTaskZhu" placeholder="" 
              style="width: 60px; display: inline-block;" />
            <span>字</span>
          </n-grid-item>
          <n-grid-item>
            <span>第 </span>
            <n-input v-model:value="formData.editorRankFu" placeholder=""
              style="width: 60px; display: inline-block;" />
            <span>副主编撰写&nbsp;&nbsp;</span>
            <n-input v-model:value="formData.editorTaskFu" placeholder="" 
              style="width: 60px; display: inline-block;" />
            <span>字</span>
          </n-grid-item>
          <n-grid-item>
            <span>第 </span>
            <n-input v-model:value="formData.editorRankCan" placeholder=""
              style="width: 60px; display: inline-block;" />
            <span>参编撰写&nbsp;&nbsp;</span>
            <n-input v-model:value="formData.editorTaskCan" placeholder="" 
              style="width: 60px; display: inline-block;" />
            <span>字</span>
          </n-grid-item>
        </n-grid>

        <n-grid :cols="2" :x-gap="20" style="margin-top: 20px;">
          <n-grid-item>
            <n-form-item label="出版社" path="publisher">
          <n-input v-model:value="formData.publisher" placeholder="请输入出版社名称" />
        </n-form-item>
          </n-grid-item>
          <n-grid-item>
            <n-form-item label="标准书号ISBN" path="standardBookNumber">
              <n-input v-model:value="formData.standardBookNumber" placeholder="请输入标准书号" />
            </n-form-item>
          </n-grid-item>
        </n-grid>

        <n-h4 prefix="bar">
          <n-flex justify="space-between" align="center">
            <strong>奖励申请</strong>
          </n-flex>
        </n-h4>
        <n-form-item label="奖励额度" path="rewardAmount">
          <n-input v-model:value="formData.rewardAmount" placeholder="请输入奖励额度" style="width: 200px;">
            <template #suffix>元</template>
            <template #prefix>￥</template>
          </n-input>
        </n-form-item>

        <!-- 附件上传 -->
      <n-h4 prefix="bar"><strong>附件上传</strong></n-h4>
      <div class="performance-section-header">
        <span class="section-title">附件</span>
        <div
          class="upload-wrapper"
          :style="{ backgroundColor: formData?.attFiles?.length > 0 ? '#8dd46c' : '#f5f7fa' }"
        >
          <span class="upload-text">
           相关附件 
          </span>
          <component :is="fileUploadComp" />
        </div>
      </div>

      <n-h4 prefix="bar" style="color: red;"><strong>备注</strong></n-h4>
          1.请上传正式出版的专著原件1部及PDF版;<br>
          2.请上传新闻总署专著检索CIP数据页面下载证明;"<br>
          3.请上传出版社出具的字数证明电子版（PDF）;"<br>
          4.请上传专著复印件1份（包括封面、扉页、图书在版编目CIP数据页、目录、能够显示申报人完成撰写工作量的页面、封底）;"<br>
          5.请上传权威检测机构的著作检索凭证PDF1份（含CIP数据核字号结果和文本复制检测报告）;"<br>
          6.请上传出版合同+著作权归属书面合同
      </n-form>


      <!-- 提交按钮 -->
      <div class="form-actions" v-if="!formDisabled">
        <n-button type="primary" @click="prepareSubmitData" :loading="submitLoding" :disabled="submitLoding">提交申请</n-button>
        <n-button @click="handleReset" :disabled="submitLoding">重置表单</n-button>
      </div>
    </div>

    <!-- 签名 -->
    <j-modal title="签名" v-model:show="showSign" width="20%" :show-btn="false">
      <template #content>
        <j-sign @done="signDone" placeholder="请输入个人签章密码" />
      </template>
    </j-modal>

    <!-- 预览签名和文件 -->
    <j-preview v-model:show="showPreview" :oss-path="previewOssPath" :oss-path-name="previewOssPathName"
      :bucket="bucket" />

    <!-- 学术专著有效认定详情 -->
      <ProcessInstanceDetailModal
      :processInstanceId="processInstanceCode"
      v-model:show="showPaperSubmissionDetail"
      :z-index="10"
    />
  </div>
</template>

<script lang="ts" setup>
  import { useForm } from './useForm'
  import { EyeOutline } from '@vicons/ionicons5'
  import './styles.scss'
  import { ref } from 'vue' 
  import {
    NButton,
    NInput,
    NInputNumber,
    NPopconfirm,
    NPopover,
    NSelect,
    NTag,
    NTreeSelect,
    TreeSelectOption,
    UploadFileInfo,
  } from 'naive-ui'
  import { Addr, HosOrg, Icon as JIcon, UploadPreview } from '@jcomponents'
  import JPGlobal from '@jutil'
  import Decimal from 'decimal.js'
  import { h } from 'vue'
  import { queryOrg } from '@/api/hrm/hrmOrg' // 导入科室查询API
  import AcademicWriting from './academicWriting.vue'
  import { getBpmDetailById } from '@/api/hrm/thesisCondition/paperSubmission'
  import ProcessInstanceDetailModal from '@/views/modules/bpm/processInstance/detail/processDetailModal.vue'

  let deptInfos = ref<any[]>([]) //科室类型数据
  let deptInfoOptions = ref<TreeSelectOption[]>([])
  let showClinicalTrialChoosePane = ref(false) //是否显示论文投稿备案
  let costs = ref<Costs[]>([])
  let sumVal1 = ref(0)
  let sumVal2 = ref(0)

  // 定义费用对象
  type Costs = {
    key: string
    val: number | null
  }

 

  // 定义假数据
  const publicationTypeOptions = ref([
      { label: '专著', value: '专著' },
      { label: '编著', value: '编著' },
      { label: '译著', value: '译著' }
    ]);

const publisherTypeOptions = ref([
      { label: '国家级出版社', value: '国家级出版社' },
      { label: '省级出版社', value: '省级出版社' }
    ]);


 

  // 在组件中定义 props
  const props = defineProps({
    // 流程标识(新增)
    processDefinitionKey: {
      type: String,
      required: false,
    },
    // 流程实例ID（重新提交）
    processInstanceId: {
      type: String,
      required: false,
    },
    // 业务表单ID（详情）
    id: {
      type: [Number, String],
      required: false,
    },
  })

  // 定义 emits
  const emits = defineEmits(['close'])

  // 将 props 传入 useForm
  const {
    formRef,
    formDisabled,
    submitLoding,
    formData,
    rules,
    handleSubmit,
    handleReset,
    showNumberButton,
    fileUploadComp,
    showSign,
    signDone,
    showPreview,
    previewOssPath,
    previewOssPathName,
    bucket,
    handlePreview,
  } = useForm(props, emits)

  watch(
    () => props,
    newVal => {
      console.log(newVal, 'props')
    },
    {
      immediate: true,
      deep: true,
    }
  )

const handleRtClinicalTrials = (clinicalTrials: Array<any>) => {
    showClinicalTrialChoosePane.value = false
    
    // 清空现有数据
    reimTable.value.data = []
    
    // 只添加选中的第一条记录
    if (clinicalTrials.length > 0) {
      const clinicalTrial = clinicalTrials[0]
      
      // 添加到表格
      reimTable.value.data.push({
        key: JPGlobal.guid(),
        index: 1,
        reimAbst: clinicalTrial.thesisName,
        deptCode: clinicalTrial.department,
        deptName: clinicalTrial.departmentName || '',
        processInstanceCode: clinicalTrial.processInstanceCode,
        mainResearcherName: clinicalTrial.mainResearcherName,
        type: '',
        typeName: '',
        budgetCode: null,
        amt: clinicalTrial.applyCost,
        researcherFundingApplyId: clinicalTrial.id
      })
      
      // 同步更新表单数据
      formData.value.thesisName = clinicalTrial.thesisName // 论文题目
      formData.value.department = clinicalTrial.department // 专业科室
      formData.value.mainResearcher = clinicalTrial.mainResearcher // 申请人
      
      // 如果没有科室名称，则需要获取科室名称
      if (clinicalTrial.department && !clinicalTrial.departmentName) {
        // 获取科室名称
        queryOrg({ orgId: clinicalTrial.department }).then(res => {
          if (res && res.data && res.data.length > 0) {
            const deptName = res.data[0].orgName;
            // 更新表格中的科室名称
            if (reimTable.value.data.length > 0) {
              reimTable.value.data[0].deptName = deptName;
            }
          }
        });
      }
    }
    
    // 重新计算costs数据(报销时需要)
    let newCosts: any = []
    reimTable.value.data.forEach((item, num) => {
      let rk = 'j' + 'amt' + num
      newCosts.push({
        key: rk,
        val: item.amt,
      })
    })
    costs.value = newCosts
    changeSummary()
  }

   // 表格汇总数据
  let changeSummary = () => {
    sumVal1.value = 0
    sumVal2.value = 0
    let dcVal1 = new Decimal(0)
    let dcVal2 = new Decimal(0)
    costs.value.forEach(t => {
      if (t.key.startsWith('j')) {
        dcVal1 = dcVal1.plus(new Decimal(t.val ?? 0))
      } else {
        dcVal2 = dcVal2.plus(new Decimal(t.val ?? 0))
      }
    })
    sumVal1.value = dcVal1.toNumber()
    sumVal2.value = dcVal2.toNumber()
  }

   const reimTable = ref({
    title: '学术专著备案详情',
    columns: [
      {
        type: 'selection',
      },
      {
        title: '序号',
        key: 'index',
        align: 'center',
        width: 70,
      },
      {
        title: '学术专著',
        key: 'reimAbst',
        render: (row: any) => {
          return h(NInput, {
            value: row.reimAbst,
            type: 'textarea',
            autosize: true,
            disabled: true,
            onInput: (val: string) => {
              row.reimAbst = val
            },
          })
        },
      },
      {
        title: '申请人',
        key: 'mainResearcherName',
        width: 220,
        render: (row: any) => {
          return h(NInput, {
            value: row.mainResearcherName,
            disabled: true,
            onInput: (val: string) => {
              row.mainResearcherName = val
            },
          })
        },
      },
      {
        title: '申请科室',
        key: 'deptCode',
        width: 260,
        render: (row: any) => {
          // 如果已有科室名称，直接显示名称，否则使用HosOrg组件
          if (row.deptName) {
            return h(NInput, {
              value: row.deptName,
              disabled: true,
              onInput: (val: string) => {
                row.deptName = val
              },
            })
          } else {
            return h(HosOrg, {
              value: row.deptCode,
              disabled: true,
              clearable: false,
              pinyinSearch: true,
              customRecords: deptInfos.value as any,
              customOptions: deptInfoOptions.value as any,
              onChangeValue: (d: any) => {
                row.deptCode = d.orgId
                row.deptName = d.orgName
              },
            })
          }
        },
      },
      {
        title: '操作',
        key: 'action',
        width: 120,
        render: (row: any) => {
          return h(
            NButton,
            {
              size: 'small',
              type: 'primary',
              onClick: () => {
                processInstanceCode.value = row.processInstanceCode
                showPaperSubmissionDetail.value = true
              }
            },
            { default: () => '专著备案详情' }
          )
        }
      },
    ],
    data: <any[]>[],
  })

  const clinicalTrialChooseRef = ref()
  const doChooseClinicalTrial = () => {
    clinicalTrialChooseRef.value.doChooseClinicalTrial()
  }

  const showClinicalTrialChoose = () => {
    showClinicalTrialChoosePane.value = true
  }

  const processInstanceCode = ref('')
  const showPaperSubmissionDetail = ref(false)

  // 在handleSubmit调用前准备数据
  const prepareSubmitData = () => {
    // 使用类型断言解决类型检查问题
    (formData.value as any).paperRecords = reimTable.value.data.map(item => ({
      researcherFundingApplyId: item.researcherFundingApplyId,
      thesisName: item.reimAbst,
      department: item.deptCode,
      departmentName: item.deptName,
      processInstanceCode: item.processInstanceCode,
      mainResearcherName: item.mainResearcherName,
      amt: item.amt
    }))
    
    handleSubmit(new MouseEvent('click'))
  }

 
  //回显后端的数据
  watch(
    () => formData.value,
    (newVal) => {
      if(newVal && newVal.paperRecords && newVal.paperRecords.length > 0) {
        // 清空现有数据
        reimTable.value.data = [];
        // 填充回显数据
        newVal.paperRecords.forEach((record, index) => {
          reimTable.value.data.push({
            key: JPGlobal.guid(),
            index: index + 1,
            reimAbst: record.thesisName,
            deptCode: record.department,
            deptName: record.departmentName || '', 
            processInstanceCode: record.processInstanceCode,
            mainResearcherName: record.mainResearcherName,
            type: '',
            typeName: '',
            budgetCode: null,
            amt: record.amt || 0,
            researcherFundingApplyId: record.id || null  
          });
          
          // 如果有科室代码但没有科室名称，尝试获取科室名称
          if (record.department && !record.departmentName) {
            queryOrg({ orgId: record.department }).then(res => {
              if (res && res.data && res.data.length > 0) {
                const deptName = res.data[0].orgName;
                // 更新表格中对应行的科室名称
                const foundRow = reimTable.value.data.find(item => item.deptCode === record.department && item.index === index + 1);
                if (foundRow) {
                  foundRow.deptName = deptName;
                }
              }
            });
          }
        });
        // 重新计算costs
        let newCosts = [];
        reimTable.value.data.forEach((item, num) => {
          newCosts.push({
            key: 'jamt' + num,
            val: item.amt || 0,
          });
        });
        costs.value = newCosts;
        changeSummary();
      } else {
        console.log('paperRecords 不存在或为空数组');
      }
    },
    { deep: true, immediate: true }
  );


  // 将formData中的paperRecords数据更新到reimTable中
  const updateReimTable = () => {
    if (formData.value?.paperRecords?.length > 0) {
      reimTable.value.data = formData.value.paperRecords.map((record, index) => ({
        key: JPGlobal.guid(),
        index: index + 1,
        reimAbst: record.thesisName,
        deptCode: record.department,
        deptName: record.departmentName || '',
        processInstanceCode: record.processInstanceCode,
        mainResearcherName: record.mainResearcherName,
        type: '',
        typeName: '',
        budgetCode: null,
        amt: record.amt || 0,
        researcherFundingApplyId: record.researcherFundingApplyId || null
      }));
      
      // 处理没有科室名称的情况
      reimTable.value.data.forEach((row, index) => {
        if (row.deptCode && !row.deptName) {
          queryOrg({ orgId: row.deptCode }).then(res => {
            if (res && res.data && res.data.length > 0) {
              row.deptName = res.data[0].orgName;
            }
          });
        }
      });
    }
  }


</script>
