<template>
  <j-crud
    :queryMethod="queryMethod"
    :queryForm="queryForm"
    :tabs="tabs"
    default-check-tab="0"
    :ext-table-buttons="extTableButtons"
    name="学术著作奖励申请"
    ref="crudRef"
  >
    <template #extendButtons>
      <n-button type="info" @click="handleApply">申请</n-button>
      <n-button type="primary" @click="handleTravelApply">差旅报销申请</n-button>
      <n-button type="primary" @click="handleTravelPay">差旅报销</n-button>
    </template>

    

    <template #extendFormItems>
      <n-form-item label="项目名称" path="thesisName">
        <n-input v-model:value="queryForm.thesisName" />
      </n-form-item>
      <n-form-item label="专业科室" path="department">
        <j-bus-hos-org v-model:value="queryForm.department" :clearable="true" />
      </n-form-item>
      <n-form-item label="申请部门" path="applyDepartment">
        <j-bus-hos-org v-model:value="queryForm.applyDepartment" :clearable="true" />
      </n-form-item>
    </template>

    <template #content>
      <!-- 经费申报弹窗 -->
      <ProcessInstanceCreateModal
        v-model:show="showAdd"
        :processDefinitionKey="processDefinitionKey"
        :processInstanceId="processInstanceCode"
        @close="close"
      />
      <!-- 经费申报详情 -->
      <ProcessInstanceDetailModal
        :processInstanceId="processInstanceCode"
        v-model:show="showDetail"
        :z-index="10"
        @close="close"
      />
      <!-- 预览 -->
      <j-preview
        v-model:show="fileData.showPreview.value"
        :oss-path="fileData.previewOssPath.value"
        :oss-path-name="fileData.previewOssPathName.value"
        bucket="hrm"
      />
    </template>
  </j-crud>
</template>

<script lang="ts" setup>
  import ProcessInstanceCreateModal from '@/views/modules/bpm/processInstance/detail/processCreateModel.vue'
  import ProcessInstanceDetailModal from '@/views/modules/bpm/processInstance/detail/processDetailModal.vue'
import { useThesisCondition } from './useThesisCondition'

  const {
    processDefinitionKey,
    crudRef,
    queryForm,
    showAdd,
    showDetail,
    fileData,
    processInstanceCode,
    handleApply,
    handleTravelApply,
    handleTravelPay,
    close,
    extTableButtons,
    tabs,
    queryMethod,
  } = useThesisCondition()
</script>
