import { computed, h, ref, VNode } from 'vue'
import { NButton, NTag } from 'naive-ui'
import { ContainerValueType } from '@/types/common/jtypes'
import { fileShowRender } from '@/types/modules/hrm/emp/emp'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import { useUserStore } from '@/store'
import { ElMessageBox } from 'element-plus'
import { JTab } from '@jtypes'
import { pageQueryOutStudy} from '@/api/hrm/thesisCondition/outStudy'
import { useRouter } from 'vue-router'

export function useThesisCondition() {
  // 常量定义
  const processDefinitionKey = 'RMS_OUT_STUDY' // 流程定义key
  const userStore = useUserStore() // 用户数据存储
  const router = useRouter() // 路由实例

  // refs定义
  const crudRef = ref<any>() // CRUD操作引用
  const showAdd = ref(false) // 控制添加表单的显示
  const showDetail = ref(false) // 控制详情页面的显示
  const processInstanceCode = ref<string>('') // 流程实例编码

  // 查询表单条件
  const queryForm = ref({
    chkState: '0', // 状态：0-审核中，1-已通过，2-未通过，3-已取消
    thesisName: '', // 论文名称
    department: '', // 专业科室
    applyDepartment: '', // 申请部门
  })

  // 文件预览相关配置
  const fileData = {
    bucket: 'hrm', // 存储桶名称
    showPreview: ref(false), // 控制预览窗口显示
    previewOssPath: ref(''), // 预览文件路径
    previewOssPathName: ref(''), // 预览文件名称
  }

  // 表格列定义
  const originColumns: any = [
    
    {
      columns1: true,
      columns2: true,
      title: '序号',
      key: 'index',
      width: 70,
      align: 'center',
    },
    {
      columns1: true,
      columns2: true,
      title: '申请人',
      key: 'mainResearcherName',
      width: 150,
      ellipsis: {
        showTitle: false,
      },
    },
    {
      columns1: true,
      columns2: true,
      title: '专业科室',
      key: 'departmentName',
      width: 120,
    },
    {
      columns1: true,
      columns2: true,
      title: '学习城市',
      key: 'studyCity',
      width: 120,
    },
    {
      columns1: true,
      columns2: true,
      title: '学习单位',
      key: 'studyUnit',
      width: 120,
    },
    {
      columns1: true,
      columns2: true,
      title: '学习专业',
      key: 'studyType',
      width: 120,
    },
  ]

  // 方法定义
  const handleApply = () => {
    // 处理申请操作
    processInstanceCode.value = null
    showAdd.value = true
  }

  // 差旅报销申请
  const handleTravelApply = () => {
    router.push('/ecs/reimNew/travelAppr')
  }

  // 差旅报销
  const handleTravelPay = () => {
    router.push('/ecs/reimNew/travelReim')
  }

  const close = () => {
    // 关闭表单并刷新数据
    showAdd.value = false
    crudRef.value.queryData()
  }

  const backApply = async (row: any) => {
    // 取消申请操作
    const { value } = await ElMessageBox.prompt('请输入取消原因', '取消流程', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /^[\s\S]*.*\S[\s\S]*$/,
      inputErrorMessage: '取消原因不能为空',
    })
    await ProcessInstanceApi.cancelProcessInstanceByStartUser(row.processInstanceCode, value)
    window.$message.success('取消成功')
    await crudRef.value.queryData()
  }

  // 扩展表格操作按钮
  const extTableButtons = computed((): VNode[] => {
    let result = [
      h(
        NTag,
        {
          //控制按钮颜色
          type: 'success',
          callback: (row: any) => {
            processInstanceCode.value = row.processInstanceCode
            if (processInstanceCode.value == null) {
              window.$message.warning('获取流程实例失败')
              return
            }
            showDetail.value = true
          },
          style: {
            cursor: 'pointer',
          },
        },
        () => '详情'
      ),
    ]
    let r2 = h(
      NTag,
      {
        type: 'error',
        callback: (row: any) => {
          if (row.crter == userStore.getUserInfo?.hrmUser?.empCode) {
            backApply(row)
          } else {
            window.$message.warning('只能取消自己提交的申请')
          }
        },
        style: {
          cursor: 'pointer',
          marginLeft: '10px',
        },
      },
      () => '取消申请'
    )
    let r3 = h(
      NTag,
      {
        type: 'default',
        callback: (row: any) => {
          processInstanceCode.value = row.processInstanceCode
          if (processInstanceCode.value == null) {
            window.$message.warning('获取流程实例失败')
            return
          }
          showAdd.value = true
        },
        style: {
          cursor: 'pointer',
          marginLeft: '10px',
        },
      },
      () => '重新发起'
    )

    // let r4 = h(
    //   NTag,
    //   {
    //     type: 'error',
    //     callback: async (row: any) => {
    //       try {
    //         // 向后端发送删除请求
    //         await deleteHrmAcademicWorkAward(row.id)
    //         window.$message.success('删除成功')
    //         await crudRef.value.queryData() // 刷新表格数据
    //       } catch (error) {
    //         console.error('删除失败:', error)
    //         window.$message.error('删除失败，请稍后重试')
    //       }
    //     },
    //     style: {
    //       cursor: 'pointer',
    //       marginLeft: '10px',
    //     },
    //   },
    //   () => '删除'
    // )
      

    switch (queryForm.value.chkState) {
      case '0':
        result.push(r2)
        break
      case '1':
        break
      case '2':
      case '3':
        result.push(r3)
        // TODO: 在已取消标签下添加一个删除按钮
        // result.push(r4)
        break
    }
    return result
  })

  // 标签页相关配置
  const tabChange = (tab: JTab) => {
    // 标签页切换处理
    queryForm.value.chkState = tab.name
  }

  // 标签页定义
  const tabs = ref<JTab[]>([
    {
      tab: '审核中',
      name: '0',
      columns: originColumns,
      useBadge: true,
      tabChange: tabChange,
    },
    {
      tab: '已通过',
      name: '1',
      columns: originColumns,
      tabChange: tabChange,
    },
    {
      tab: '未通过',
      name: '2',
      columns: originColumns,
      tabChange: tabChange,
    },
    {
      tab: '已取消',
      name: '3',
      columns: originColumns,
      tabChange: tabChange,
    },
  ])

  // 数据查询方法
  const queryMethod = pageQueryOutStudy 

  // 返回组合式API的暴露接口
  return {
    processDefinitionKey,
    crudRef,
    queryForm,
    showAdd,
    showDetail,
    fileData,
    processInstanceCode,
    handleApply,
    close,
    extTableButtons,
    tabs,
    queryMethod,
    handleTravelApply,
    handleTravelPay,
  }
}
