<template>
  <div class="precision-adjustment">
    <div class="page-header">
      <n-space align="center" justify="space-between">
        <n-space vertical>
          <div class="page-title">物资精度调整</div>
          <n-text depth="3">将物资价格和金额从6位小数精度调整为指定小数精度</n-text>
        </n-space>
        <n-space>
          <n-button @click="goBack">返回库存表</n-button>
          <n-button type="info" @click="handleAnalysisQuery">刷新数据</n-button>
        </n-space>
      </n-space>
    </div>

    <!-- 精度损失测算 -->
    <n-card class="analysis-card" :loading="analysisLoading">
      <n-space vertical size="large">
        <n-space align="center" justify="space-between">
          <n-text strong style="font-size: 16px">精度损失测算</n-text>
          <n-space align="center">
            <n-space vertical size="small" style="width: 250px">
              <n-text>物资搜索：</n-text>
              <n-input
                v-model:value="searchName"
                placeholder="输入物资名称"
                clearable
                @keydown.enter="handleAnalysisQuery"
              />
            </n-space>
            <n-space vertical size="small" style="width: 150px">
              <n-text>物资类型：</n-text>
              <n-select
                v-model:value="searchAssetType"
                placeholder="选择物资类型"
                clearable
                :options="assetTypeOptions"
                @update:value="handleAnalysisQuery"
              />
            </n-space>
            <n-text>目标精度：</n-text>
            <n-slider
              v-model:value="targetPrecision"
              :min="0"
              :max="6"
              :step="1"
              :marks="{
                0: '0位',
                1: '1位',
                2: '2位',
                3: '3位',
                4: '4位',
                5: '5位',
                6: '6位',
              }"
              style="width: 300px"
              @update:value="handleAnalysisQuery"
            />
            <n-button type="primary" @click="handleAnalysisQuery">
              <template #icon>
                <n-icon><i class="fas fa-sync-alt"></i></n-icon>
              </template>
              手动查询
            </n-button>
          </n-space>
        </n-space>

        <n-divider />

        <!-- 汇总统计 -->
        <div class="summary-section">
          <div class="summary-box">
            <div class="summary-item">
              <div class="item-label">总物资数量</div>
              <div class="item-value">{{ analysisSummary.totalMaterialCount }}</div>
            </div>
            <div class="summary-item">
              <div class="item-label">原始金额总计</div>
              <div class="item-value">{{ formatNumberWithCommas(analysisSummary.totalOriginalAmount) }}</div>
            </div>
            <div class="summary-item">
              <div class="item-label">精度损失物资数</div>
              <div class="item-value">{{ analysisSummary.affectedMaterialCount }}</div>
            </div>
            <div class="summary-item">
              <div class="item-label">金额精度损失总计</div>
              <div class="item-value loss">{{ formatNumberWithCommas(analysisSummary.totalAmountDiff) }}</div>
            </div>
          </div>

          <div class="summary-footer">
            <n-space align="center" justify="center">
              <n-button type="primary" @click="showOffsetInfo">
                <template #icon>
                  <n-icon><i class="fas fa-list-alt"></i></n-icon>
                </template>
                查看冲账物资信息
              </n-button>
              <n-button type="info" @click="showRiskAnalysisModal = true">
                <template #icon>
                  <n-icon><i class="fas fa-chart-pie"></i></n-icon>
                </template>
                风险等级分析
              </n-button>
              <n-button type="warning" @click="showProgressModal = true">
                <template #icon>
                  <n-icon><i class="fas fa-tasks"></i></n-icon>
                </template>
                进度监控
              </n-button>
            </n-space>
          </div>

          <div v-if="targetPrecision === 6 && analysisSummary.totalAmountDiff === 0" class="no-loss-tip">
            <n-alert type="success" :bordered="false">
              <template #icon>
                <n-icon><i class="fas fa-check-circle"></i></n-icon>
              </template>
              当前精度为6位，无精度损失
            </n-alert>
          </div>
        </div>

        <div class="action-bar">
          <n-space align="center" justify="center">
            <n-button
              type="warning"
              size="large"
              @click="showConfirmDialog"
              :disabled="targetPrecision === 6 || analysisSummary.affectedMaterialCount === 0"
            >
              批量调整精度(6位→{{ targetPrecision }}位)
            </n-button>
          </n-space>
        </div>

        <j-n-data-table
          :columns="analysisColumns"
          :data="analysisDetails"
          :bordered="false"
          :loading="analysisLoading"
          :row-key="row => row.id"
          :default-sort-order="'descend'"
          :default-sort="{ columnKey: 'amountDiff', order: 'descend' }"
          :pagination="pagination"
          @update:page="onPageChange"
          @update:page-size="onPageSizeChange"
        />
      </n-space>
    </n-card>

    <n-modal
      v-model:show="showConfirm"
      preset="dialog"
      title="确认精度调整"
      positive-text="确认调整"
      negative-text="取消"
      @positive-click="handlePrecisionAdjustment"
      @negative-click="showConfirm = false"
    >
      <n-space vertical>
        <n-alert title="警告" type="warning">
          <div>您正在执行精度调整操作，该操作将物资数据从6位小数精度调整为{{ targetPrecision }}位小数精度。</div>
        </n-alert>
        <n-text
          >即将调整 <n-text strong>{{ analysisSummary.affectedMaterialCount }}</n-text> 个物资的精度，此操作将造成约
          <n-text strong type="error">{{ formatNumberWithCommas(analysisSummary.totalAmountDiff) }}元</n-text>
          的精度损失。
        </n-text>
        <n-text>请确认是否继续？</n-text>

        <n-form-item label="操作备注" required>
          <n-input v-model:value="operationRemark" type="textarea" placeholder="请输入操作备注（必填）" :rows="3" />
        </n-form-item>
      </n-space>
    </n-modal>

    <!-- 冲账物资信息弹窗 -->
    <n-modal
      v-model:show="showOffsetModal"
      preset="card"
      title="冲账物资信息"
      style="width: 600px"
      :bordered="false"
      :segmented="{ content: true, footer: 'soft' }"
    >
      <n-space vertical>
        <n-alert type="info" :bordered="false">
          <template #icon>
            <n-icon><i class="fas fa-info-circle"></i></n-icon>
          </template>
          请使用以下信息录入一笔冲账，用于抵消精度损失金额
        </n-alert>

        <n-descriptions bordered>
          <n-descriptions-item label="冲账金额" :span="3">
            <n-text type="error" style="font-size: 16px; font-weight: bold">
              ¥{{ formatNumberWithCommas(analysisSummary.totalAmountDiff) }}
            </n-text>
          </n-descriptions-item>
          <n-descriptions-item label="物资名称" :span="3"> 精度损失冲账 </n-descriptions-item>
          <n-descriptions-item label="备注说明" :span="3">
            {{ targetPrecision }}位精度调整损失冲账
          </n-descriptions-item>
        </n-descriptions>

        <n-alert type="warning" :bordered="false">
          <template #icon>
            <n-icon><i class="fas fa-exclamation-circle"></i></n-icon>
          </template>
          请复制以上信息，在物资入库时使用此金额进行冲账录入
        </n-alert>
      </n-space>

      <template #footer>
        <n-space justify="end">
          <n-button @click="showOffsetModal = false">关闭</n-button>
          <n-button type="primary" @click="handleCopyOffset">
            <template #icon>
              <n-icon><i class="fas fa-copy"></i></n-icon>
            </template>
            复制信息
          </n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 处理结果弹窗 -->
    <n-modal
      v-model:show="showResultModal"
      preset="card"
      title="操作结果"
      style="width: 500px"
      :bordered="false"
      :segmented="{ footer: 'soft' }"
    >
      <n-result
        :status="operationSuccess ? 'success' : 'error'"
        :title="operationSuccess ? '精度调整成功' : '精度调整失败'"
        :description="operationResultMsg"
      />
      <template #footer>
        <n-space justify="center">
          <n-button @click="closeResultModal">关闭</n-button>
          <n-button type="primary" @click="handleAnalysisQuery">刷新数据</n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 风险分析弹窗 -->
    <n-modal
      v-model:show="showRiskAnalysisModal"
      preset="card"
      title="风险等级分析"
      style="width: 800px"
      :bordered="false"
      :segmented="{ content: true, footer: 'soft' }"
    >
      <n-space vertical>
        <n-alert type="info" :bordered="false">
          <template #icon>
            <n-icon><i class="fas fa-chart-pie"></i></n-icon>
          </template>
          风险等级基于金额影响进行分类：高风险(>1000元)、中风险(100-1000元)、低风险(<100元)
        </n-alert>

        <n-data-table
          :columns="riskAnalysisColumns"
          :data="riskAnalysisData"
          :bordered="false"
          :loading="riskAnalysisLoading"
          :row-key="row => row.riskLevel"
        />
      </n-space>

      <template #footer>
        <n-space justify="end">
          <n-button @click="showRiskAnalysisModal = false">关闭</n-button>
          <n-button type="primary" @click="refreshRiskAnalysis">
            <template #icon>
              <n-icon><i class="fas fa-sync-alt"></i></n-icon>
            </template>
            刷新分析
          </n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 进度监控弹窗 -->
    <n-modal
      v-model:show="showProgressModal"
      preset="card"
      title="精度转换进度"
      style="width: 600px"
      :bordered="false"
      :segmented="{ content: true, footer: 'soft' }"
    >
      <n-space vertical size="large">
        <n-alert v-if="conversionProgress" :type="getProgressAlertType()" :bordered="false">
          <template #icon>
            <n-icon><i class="fas fa-info-circle"></i></n-icon>
          </template>
          批次号: {{ conversionProgress.batchNo }} | 状态: {{ getProgressStatusText() }}
        </n-alert>

        <div v-if="conversionProgress">
          <n-progress
            type="line"
            :percentage="conversionProgress.percentage || 0"
            :status="getProgressStatus()"
            :show-indicator="true"
          />
          
          <n-descriptions bordered style="margin-top: 16px">
            <n-descriptions-item label="总物资数量" :span="2">
              {{ conversionProgress.totalCount || 0 }}
            </n-descriptions-item>
            <n-descriptions-item label="已处理数量" :span="1">
              {{ conversionProgress.processedCount || 0 }}
            </n-descriptions-item>
            <n-descriptions-item label="成功数量" :span="1">
              {{ conversionProgress.successCount || 0 }}
            </n-descriptions-item>
            <n-descriptions-item label="失败数量" :span="1">
              {{ conversionProgress.errorCount || 0 }}
            </n-descriptions-item>
            <n-descriptions-item label="当前步骤" :span="3">
              {{ conversionProgress.currentStep || '等待中...' }}
            </n-descriptions-item>
            <n-descriptions-item v-if="conversionProgress.errorMessage" label="错误信息" :span="3">
              <n-text type="error">{{ conversionProgress.errorMessage }}</n-text>
            </n-descriptions-item>
          </n-descriptions>
        </div>

        <div v-else>
          <n-empty description="暂无进度信息" />
        </div>
      </n-space>

      <template #footer>
        <n-space justify="space-between">
          <n-space>
            <n-button 
              v-if="conversionProgress && conversionProgress.batchNo" 
              type="error" 
              @click="handleQuickRollback"
              :disabled="conversionProgress.status === 'RUNNING'"
            >
              <template #icon>
                <n-icon><i class="fas fa-undo"></i></n-icon>
              </template>
              快速回滚
            </n-button>
          </n-space>
          <n-space>
            <n-button @click="showProgressModal = false">关闭</n-button>
            <n-button type="primary" @click="refreshProgress">
              <template #icon>
                <n-icon><i class="fas fa-sync-alt"></i></n-icon>
              </template>
              刷新进度
            </n-button>
          </n-space>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
  import { h, ref, onMounted } from 'vue'
  import { useRouter } from 'vue-router'
  import {
    NPopover,
    NAlert,
    NNumberAnimation,
    NModal,
    NSpace,
    NButton,
    NIcon,
    NDataTable,
    NDescriptions,
    NDescriptionsItem,
    NResult,
    NInput,
    NFormItem,
    NSelect,
  } from 'naive-ui'
  import {
    queryPrecisionAnalysisSummary,
    queryPrecisionAnalysisDetail,
    convertPrecision,
    preConversionCheck,
    enhancedBatchConvert,
    queryProgress,
    queryRiskLevelAnalysis,
    quickRollback,
  } from '@/api/mmis/matSum/MaterialSumWeb'

  interface AnalysisDetailItem {
    id: number
    code: string
    name: string
    originalPrice: number
    roundedPrice: number
    priceDiff: number
    stockQuantity: number
    originalAmount: number
    roundedAmount: number
    amountDiff: number
  }

  interface AnalysisSummaryData {
    totalMaterialCount: number
    totalOriginalAmount: number
    totalAmountDiff: number
    affectedMaterialCount: number
    hospitalId?: string
  }

  const router = useRouter()
  const showConfirm = ref(false)
  const operationRemark = ref('')

  // 精度损失测算
  const targetPrecision = ref(2)
  const analysisLoading = ref(false)
  const analysisDetails = ref<AnalysisDetailItem[]>([])
  const analysisSummary = ref<AnalysisSummaryData>({
    totalMaterialCount: 0,
    totalOriginalAmount: 0,
    totalAmountDiff: 0,
    affectedMaterialCount: 0,
  })

  // 搜索参数
  const searchName = ref('')
  const searchAssetType = ref<string | null>(null)
  const assetTypeOptions = ref([
    { label: '低值易耗品', value: '0101' },
    { label: '医疗设备', value: '0201' },
    { label: '办公设备', value: '0301' },
    { label: '药品', value: '0401' },
  ])

  // 分页
  const pagination = ref({
    page: 1,
    pageSize: 10,
    itemCount: 0,
    pageSizes: [10, 20, 50, 100],
    showSizePicker: true,
    prefix({ itemCount }: { itemCount: number }) {
      return `共 ${itemCount} 项`
    },
  })

  // 冲账物资信息相关
  const showOffsetModal = ref(false)

  // 操作结果相关
  const showResultModal = ref(false)
  const operationSuccess = ref(false)
  const operationResultMsg = ref('')

  // 风险分析相关
  const showRiskAnalysisModal = ref(false)
  const riskAnalysisColumns = ref([
    { title: '风险等级', key: 'riskLevel', sorter: true },
    { title: '物资数量', key: 'materialCount', sorter: true },
    { title: '金额影响', key: 'amountDiff', sorter: true },
  ])
  const riskAnalysisData = ref([])
  const riskAnalysisLoading = ref(false)

  // 进度监控相关
  const showProgressModal = ref(false)
  const conversionProgress = ref(null)

  // 分析查询
  const handleAnalysisQuery = async () => {
    analysisLoading.value = true
    try {
      // 汇总参数
      const summaryParams = {
        precision: targetPrecision.value,
      }

      // 详情参数
      const detailParams = {
        precision: targetPrecision.value,
        name: searchName.value || undefined,
        asetType: searchAssetType.value || undefined,
        pageNum: pagination.value.page,
        pageSize: pagination.value.pageSize,
      }

      // 并行调用两个接口
      const [summaryRes, detailRes] = await Promise.all([
        queryPrecisionAnalysisSummary(summaryParams),
        queryPrecisionAnalysisDetail(detailParams),
      ])

      if (summaryRes.code === 200) {
        analysisSummary.value = summaryRes.data
      } else {
        window.$message.error(`获取汇总数据失败：${summaryRes.message}`)
      }

      if (detailRes.code === 200 && detailRes.data) {
        if (Array.isArray(detailRes.data)) {
          // 直接返回了数组
          analysisDetails.value = detailRes.data
          pagination.value.itemCount = detailRes.data.length
        } else if (detailRes.data.list && Array.isArray(detailRes.data.list)) {
          // 返回了带分页的对象
          analysisDetails.value = detailRes.data.list
          pagination.value.itemCount = detailRes.data.total || detailRes.data.list.length
        } else {
          analysisDetails.value = []
          pagination.value.itemCount = 0
          console.warn('返回的数据格式不符合预期', detailRes.data)
        }
      } else {
        window.$message.error(`获取详情数据失败：${detailRes.message}`)
        analysisDetails.value = []
        pagination.value.itemCount = 0
      }
    } catch (error) {
      console.error('查询精度分析失败:', error)
      window.$message.error('查询精度分析失败，请检查网络连接')
    } finally {
      analysisLoading.value = false
    }
  }

  // 格式化数字（带千分位）
  const formatNumberWithCommas = (value: number | undefined | null) => {
    if (value === undefined || value === null) return '0'
    return value
      .toFixed(6)
      .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      .replace(/\.?0+$/, '')
      .replace(/(\.\d*[1-9])0+$/, '$1')
  }

  // 返回上一页
  const goBack = () => {
    router.back()
  }

  // 显示确认对话框
  const showConfirmDialog = () => {
    operationRemark.value = `将物资价格精度从6位小数调整为${targetPrecision.value}位小数`
    showConfirm.value = true
  }

  // 处理精度调整
  const handlePrecisionAdjustment = async () => {
    if (!operationRemark.value) {
      window.$message.warning('请输入操作备注')
      return false // 阻止对话框关闭
    }

    try {
      // 1. 预检查
      window.$message.loading('正在执行预检查...', { duration: 0 })

      const preCheckParams = {
        precision: targetPrecision.value,
        hospitalId: analysisSummary.value.hospitalId || '0000',
        operator: '系统管理员', // 这里应该从用户信息获取
        remark: operationRemark.value,
      }

      const preCheckRes = await preConversionCheck(preCheckParams)
      window.$message.destroyAll()

      if (preCheckRes.code === 200 && preCheckRes.data) {
        const checkResult = preCheckRes.data
        
        // 如果预检查失败，显示错误信息
        if (!checkResult.success) {
          window.$message.error(`预检查失败: ${checkResult.errorMessages}`)
          return false
        }

        // 如果有警告，询问用户是否继续
        if (checkResult.warningMessages && checkResult.warningMessages.length > 0) {
          const warningMsg = checkResult.warningMessages.join('; ')
          const confirmed = await new Promise((resolve) => {
            window.$dialog.warning({
              title: '警告',
              content: `检测到以下警告信息，是否继续？\n\n${warningMsg}`,
              positiveText: '继续执行',
              negativeText: '取消',
              onPositiveClick: () => resolve(true),
              onNegativeClick: () => resolve(false),
            })
          })
          
          if (!confirmed) {
            return false
          }
        }
      }

      // 2. 执行增强版精度转换
      window.$message.loading('正在执行精度调整，请稍候...', { duration: 0 })

      const params = {
        precision: targetPrecision.value,
        hospitalId: analysisSummary.value.hospitalId || '0000',
        operator: '系统管理员', // 这里应该从用户信息获取
        remark: operationRemark.value,
      }

      const res = await enhancedBatchConvert(params)

      window.$message.destroyAll()

      if (res.code === 200 && res.data) {
        const result = res.data
        operationSuccess.value = result.success
        
        if (result.success) {
          operationResultMsg.value = `精度调整成功！批次号: ${result.batchNo}\n处理物资: ${result.totalProcessed}个\n成功: ${result.successCount}个\n失败: ${result.failureCount}个`
          
          // 设置进度信息用于后续监控
          conversionProgress.value = {
            batchNo: result.batchNo,
            status: 'COMPLETED',
            totalCount: result.totalProcessed,
            processedCount: result.totalProcessed,
            successCount: result.successCount,
            errorCount: result.failureCount,
            percentage: 100,
            currentStep: '转换完成'
          }
          
          // 如果有失败项，显示详细信息
          if (result.hasFailures && result.failureDetails) {
            let failureMsg = '\n失败详情:\n'
            result.failureDetails.slice(0, 5).forEach(detail => {
              failureMsg += `- ${detail.itemIdentifier}: ${detail.errorMessage}\n`
            })
            if (result.failureDetails.length > 5) {
              failureMsg += `... 还有 ${result.failureDetails.length - 5} 个失败项`
            }
            operationResultMsg.value += failureMsg
          }
        } else {
          operationResultMsg.value = `精度调整失败: ${result.errorMessage || '未知错误'}`
        }
      } else {
        operationSuccess.value = false
        operationResultMsg.value = res.message || '调整失败，请联系管理员'
      }

      showResultModal.value = true
      showConfirm.value = false

      // 刷新数据
      await handleAnalysisQuery()

      return true
    } catch (error) {
      window.$message.destroyAll()
      window.$message.error('调整失败，发生异常')
      console.error('精度调整执行失败:', error)

      operationSuccess.value = false
      operationResultMsg.value = '系统异常，请稍后重试或联系管理员'
      showResultModal.value = true
      showConfirm.value = false

      return true
    }
  }

  // 关闭结果弹窗
  const closeResultModal = () => {
    showResultModal.value = false
  }

  // 分页事件处理
  const onPageChange = (page: number) => {
    pagination.value.page = page
    handleAnalysisQuery()
  }

  const onPageSizeChange = (pageSize: number) => {
    pagination.value.pageSize = pageSize
    pagination.value.page = 1
    handleAnalysisQuery()
  }

  // 分析表格列定义
  const analysisColumns = [
    { title: '物资名称', key: 'name', sorter: true },
    { title: '物资代码', key: 'code', sorter: true },
    { title: '库存数量', key: 'stockQuantity', sorter: true },
    {
      title: '原始单价(6位)',
      key: 'originalPrice',
      render: (row: AnalysisDetailItem) => h('span', {}, formatNumberWithCommas(row.originalPrice)),
      sorter: (row1: AnalysisDetailItem, row2: AnalysisDetailItem) => row1.originalPrice - row2.originalPrice,
    },
    {
      title: '调整后单价',
      key: 'roundedPrice',
      render: (row: AnalysisDetailItem) => h('span', {}, formatNumberWithCommas(row.roundedPrice)),
      sorter: (row1: AnalysisDetailItem, row2: AnalysisDetailItem) => row1.roundedPrice - row2.roundedPrice,
    },
    {
      title: '单价精度损失',
      key: 'priceDiff',
      render: (row: AnalysisDetailItem) => h('span', {}, formatNumberWithCommas(row.priceDiff)),
      sorter: (row1: AnalysisDetailItem, row2: AnalysisDetailItem) => row1.priceDiff - row2.priceDiff,
    },
    {
      title: '原始金额',
      key: 'originalAmount',
      render: (row: AnalysisDetailItem) => h('span', {}, formatNumberWithCommas(row.originalAmount)),
      sorter: (row1: AnalysisDetailItem, row2: AnalysisDetailItem) => row1.originalAmount - row2.originalAmount,
    },
    {
      title: '调整后金额',
      key: 'roundedAmount',
      render: (row: AnalysisDetailItem) => h('span', {}, formatNumberWithCommas(row.roundedAmount)),
      sorter: (row1: AnalysisDetailItem, row2: AnalysisDetailItem) => row1.roundedAmount - row2.roundedAmount,
    },
    {
      title: '金额精度损失',
      key: 'amountDiff',
      render: (row: AnalysisDetailItem) => {
        return h(
          'div',
          {
            style: 'cursor: help; color: #d03050; font-weight: bold;',
          },
          [
            h(
              NPopover,
              {
                trigger: 'hover',
                placement: 'top',
                width: 320,
                showArrow: true,
              },
              {
                trigger: () => h('span', {}, formatNumberWithCommas(row.amountDiff)),
                default: () => {
                  return h('div', { class: 'loss-analysis' }, [
                    h('div', { class: 'title' }, '精度损失详情'),
                    h('div', { class: 'item' }, [
                      h('span', { class: 'label' }, '单价精度损失：'),
                      h('span', { class: 'value' }, `¥${formatNumberWithCommas(row.priceDiff)}`),
                    ]),
                    h('div', { class: 'item' }, [
                      h('span', { class: 'label' }, '金额精度损失：'),
                      h('span', { class: 'value' }, `¥${formatNumberWithCommas(row.amountDiff)}`),
                    ]),
                  ])
                },
              }
            ),
          ]
        )
      },
      sorter: (row1: AnalysisDetailItem, row2: AnalysisDetailItem) => row1.amountDiff - row2.amountDiff,
    },
  ]

  // 显示冲账信息
  const showOffsetInfo = () => {
    showOffsetModal.value = true
  }

  // 复制冲账信息
  const handleCopyOffset = () => {
    const offsetInfo =
      `冲账金额：¥${formatNumberWithCommas(analysisSummary.value.totalAmountDiff)}\n` +
      `物资名称：精度损失冲账\n` +
      `备注说明：${targetPrecision.value}位精度调整损失冲账`

    // 复制到剪贴板
    navigator.clipboard
      .writeText(offsetInfo)
      .then(() => {
        window.$message.success('信息已复制到剪贴板')
        showOffsetModal.value = false
      })
      .catch(() => {
        window.$message.error('复制失败，请手动复制')
      })
  }

  // 风险分析相关
  const refreshRiskAnalysis = async () => {
    riskAnalysisLoading.value = true
    try {
      const params = {
        precision: targetPrecision.value,
        hospitalId: analysisSummary.value.hospitalId || '0000',
      }
      const res = await queryRiskLevelAnalysis(params)
      if (res.code === 200 && res.data) {
        riskAnalysisData.value = res.data
      } else {
        window.$message.error(`获取风险分析数据失败：${res.message}`)
        riskAnalysisData.value = []
      }
    } catch (error) {
      console.error('查询风险分析失败:', error)
      window.$message.error('查询风险分析失败，请检查网络连接')
      riskAnalysisData.value = []
    } finally {
      riskAnalysisLoading.value = false
    }
  }

  // 进度监控相关
  const refreshProgress = async () => {
    if (!conversionProgress.value?.batchNo) {
      window.$message.warning('暂无批次号信息')
      return
    }
    const res = await queryProgress(conversionProgress.value.batchNo)
    if (res.code === 200 && res.data) {
      conversionProgress.value = res.data
    } else {
      window.$message.error(`获取进度数据失败：${res.message}`)
      conversionProgress.value = null
    }
  }

  // 获取进度状态文本
  const getProgressStatusText = () => {
    if (!conversionProgress.value) return '等待中...'
    switch (conversionProgress.value.status) {
      case 'RUNNING':
        return '进行中'
      case 'COMPLETED':
        return '已完成'
      case 'FAILED':
        return '失败'
      default:
        return '未知状态'
    }
  }

  // 获取进度状态
  const getProgressStatus = () => {
    if (!conversionProgress.value) return undefined
    switch (conversionProgress.value.status) {
      case 'RUNNING':
        return 'info'
      case 'COMPLETED':
        return 'success'
      case 'FAILED':
        return 'error'
      default:
        return 'info'
    }
  }

  // 获取进度状态类型
  const getProgressAlertType = () => {
    if (!conversionProgress.value) return 'info'
    switch (conversionProgress.value.status) {
      case 'RUNNING':
        return 'info'
      case 'COMPLETED':
        return 'success'
      case 'FAILED':
        return 'error'
      default:
        return 'info'
    }
  }

  // 处理快速回滚
  const handleQuickRollback = async () => {
    if (!conversionProgress.value?.batchNo) {
      window.$message.warning('暂无批次号信息')
      return
    }
    try {
      const res = await quickRollback(conversionProgress.value.batchNo)
      if (res.code === 200 && res.data) {
        window.$message.success('快速回滚成功')
        await handleAnalysisQuery()
        showProgressModal.value = false
      } else {
        window.$message.error(`快速回滚失败：${res.message}`)
      }
    } catch (error) {
      console.error('快速回滚失败:', error)
      window.$message.error('快速回滚失败，请检查网络连接')
    }
  }

  onMounted(() => {
    handleAnalysisQuery()
  })
</script>

<style lang="scss" scoped>
  .precision-adjustment {
    padding: 16px;

    .page-header {
      margin-bottom: 16px;

      .page-title {
        font-size: 24px;
        font-weight: 600;
        color: #303133;
      }
    }

    .analysis-card {
      margin-bottom: 16px;

      .percent {
        font-size: 14px;
        color: #606266;
        margin-left: 4px;
      }

      .no-loss-tip {
        margin-top: 16px;
        text-align: center;
      }

      .action-bar {
        margin: 20px 0;
        padding: 16px;
        background-color: #f9f9f9;
        border-radius: 8px;
        text-align: center;
      }
    }

    .summary-section {
      margin: 16px 0;

      .summary-box {
        display: flex;
        border: 1px solid #f0f0f0;
        border-radius: 4px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        margin-bottom: 16px; // 添加底部间距

        .summary-item {
          flex: 1;
          padding: 24px 16px;
          text-align: center;
          border-right: 1px solid #f0f0f0;

          &:last-child {
            border-right: none;
          }

          .item-label {
            font-size: 14px;
            color: #606266;
            margin-bottom: 8px;
          }

          .item-value {
            font-size: 24px;
            font-weight: 500;
            color: #303133;

            &.loss {
              color: #d03050;
            }
          }
        }
      }

      .summary-footer {
        text-align: center;
        margin: 16px 0;
      }
    }
  }

  :deep(.loss-analysis) {
    padding: 8px;

    .title {
      font-weight: bold;
      margin-bottom: 12px;
      text-align: center;
      color: #303133;
    }

    .item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 6px;

      .label {
        color: #606266;
      }

      .value {
        font-weight: 500;
        font-family: 'Monaco', 'Menlo', monospace;
      }
    }
  }
</style>
