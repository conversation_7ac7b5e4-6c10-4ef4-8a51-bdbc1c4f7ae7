<template>
  <!-- <div> -->
  <!-- nj jhn -->
  <!-- 根据draftType选择显示不同组件 -->
  <!-- <index-component v-if="draftType === '0'" /> -->
  <!-- <upload-drafted-contract
      v-else-if="draftType === '1'"
      :is-add="true"
      :visible="true"
      @close="handleClose"
    /> -->
  <!-- <general-contract-draft v-else-if="draftType === '2'" :process-instance-id="id" @close="handleClose" /> -->
  <!-- </div> -->

  <div style="display: flex; width: 100%; height: 100%; gap: 8px" :class="{ 'mobile-layout': isMobileDevice }">
    <div style="flex: 0.7; height: 850px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1)">
      <div class="title-line">
        <div class="title-line-left">基本信息</div>
      </div>
      <n-descriptions label-placement="left" label-style="width:20%" bordered :column="1" size="small">
        <n-descriptions-item>
          <template #label>统一编号</template>
          {{ contractInfo.ctUnifiedCode }}
        </n-descriptions-item>
        <n-descriptions-item>
          <template #label>合同编号</template>
          {{ contractInfo.ctCode }}
        </n-descriptions-item>
        <n-descriptions-item>
          <template #label>合同名称</template>
          {{ contractInfo.ctName }}
        </n-descriptions-item>
        <n-descriptions-item>
          <template #label>合同开始日期</template>
          {{
            contractInfo.validityStartDate === null || contractInfo.validityStartDate === 'null'
              ? '/'
              : contractInfo.validityStartDate
          }}
        </n-descriptions-item>
        <n-descriptions-item>
          <template #label>合同结束日期</template>
          {{
            contractInfo.validityEndDate === null || contractInfo.validityEndDate === 'null'
              ? '/'
              : contractInfo.validityEndDate
          }}
        </n-descriptions-item>
      </n-descriptions>
      <div class="title-line">
        <div class="title-line-left">院方信息</div>
      </div>
      <n-descriptions label-placement="left" label-style="width:12%" bordered :column="2" size="small" style="width: 100%">
        <n-descriptions-item>
          <template #label>管理科室</template>
          {{ orgList.find(item => item.orgId === contractInfo.manageOrg)?.orgName || contractInfo.manageOrg }}
        </n-descriptions-item>
        <n-descriptions-item>
          <template #label>负责人</template>
          {{ contractInfo.responsiblePersonName }}
        </n-descriptions-item>
        <n-descriptions-item>
          <template #label>联系电话</template>
          {{ contractInfo.responsiblePhone }}
        </n-descriptions-item>
      </n-descriptions>
      <div class="title-line">
        <div class="title-line-left">相对方信息</div>
      </div>
      <n-descriptions label-placement="left" label-style="width:12%" bordered :column="2" size="small" style="width: 100%">
        <n-descriptions-item>
          <template #label>相对方(中标)</template>
          {{ contractInfo.oppositeName }}
        </n-descriptions-item>
        <n-descriptions-item>
          <template #label>联系人</template>
          {{ contractInfo.oppositePerson }}
        </n-descriptions-item>
        <n-descriptions-item>
          <template #label>联系电话</template>
          {{ contractInfo.oppositePhone }}
        </n-descriptions-item>
        <n-descriptions-item>
          <template #label>相对方银行</template>
          {{ contractInfo.oppositeBank }}
        </n-descriptions-item>
        <n-descriptions-item>
          <template #label>相对方账户</template>
          {{ contractInfo.oppositeAccount }}
        </n-descriptions-item>
      </n-descriptions>
      <div class="title-line">
        <div class="title-line-left">合同金额</div>
      </div>
      <n-descriptions label-placement="left" label-style="width:12%" bordered :column="2" size="small" style="width: 100%">
        <n-descriptions-item>
          <template #label>合同金额(元)</template>
          {{
            contractInfo.totalAmt
              ? `${contractInfo.totalAmt}元（${contractInfo.totalAmt / 10000}万元）`
              : '/'
          }}
        </n-descriptions-item>
      </n-descriptions>
      <div class="title-line">
        <div class="title-line-left">其他说明</div>
      </div>
      <n-descriptions label-placement="left" label-style="width:12%" bordered :column="2" size="small" style="width: 100%">
        <n-descriptions-item>
          <template #label>合同备注说明</template>
          {{ contractInfo.contractRemark }}
        </n-descriptions-item>
        <n-descriptions-item>
          <template #label>合同附件名称</template>
          {{ contractInfo.attachmentNames }}
        </n-descriptions-item>
        <n-descriptions-item>
          <template #label>录入人</template>
          {{ contractInfo.appyerName }}
        </n-descriptions-item>
        <n-descriptions-item>
          <template #label>录入时间</template>
          {{ contractInfo.applyTime }}
        </n-descriptions-item>
      </n-descriptions>
    </div>
    <div style="flex: 1; height: 800px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1)">
      <div style="height: 50%; padding: 10px">
        <h5 style="margin: 0; padding: 5px 0">草案原件</h5>
        <j-n-data-table
          :columns="[
            {
              title: '文件名',
              key: 'label',
              render: row => {
                return h(
                  NButton,
                  {
                    text: true,
                    type: 'primary',
                    onClick: () => showSignContractFile(row),
                    style: 'text-align: left; padding: 4px; height: auto; white-space: normal;'
                  },
                  {
                    default: () => [
                      h(JIcon, {
                        name: 'file',
                        width: 16,
                        height: 16,
                        style: 'margin-right: 6px; flex-shrink: 0;'
                      }),
                      h('span', {
                        style: 'max-width: 580px; word-break: break-all; line-height: 1.4; display: inline-block;'
                      }, row.label),
                    ],
                  }
                )
              },
            },
          ]"
          :data="contractFile1"
          :pagination="false"
        />
      </div>
    </div>
  </div>
  <!--   预览pdf文件   -->
  <j-preview
    v-model:show="showPreview2"
    :oss-path="previewOssPath3"
    :oss-path-name="previewOssPathName3"
    bucket="cms"
  />
  <!--   预览pdf文件   -->
  <j-preview
    v-model:show="showPreview1"
    :oss-path="previewOssPath1"
    :oss-path-name="previewOssPathName1"
    bucket="temp"
  />
</template>
<script lang="ts" setup>
  import { ref, watch, h, onMounted } from 'vue'
  import IndexComponent from './index.vue'
  import UploadDraftedContract from './uploadDraftedContract.vue'
  import GeneralContractDraft from './generalContractDraft.vue'
  import { queryCmsContract } from '@/api/cms/contractManage/ContractWeb.ts'
  import { queryCmsContractDetail } from '@/api/cms/contractManage/ContractDetailWeb.ts'
  import {
    NCascader,
    NDatePicker,
    NButton,
    NInput,
    NInputNumber,
    NPopover,
    NPopselect,
    NTimePicker,
    NTreeSelect,
    UploadFileInfo,
  } from 'naive-ui'
  import { Icon as JIcon } from '@jcomponents'
  import { Option } from '@/types/comps/common.ts'
  import { queryOrg } from '@/api/hrm/hrmOrg.ts'

  const isMobileDevice = inject<Ref<Boolean>>('isMobileDevice')

  // 定义类型接口
  interface ContractInfo {
    ctUnifiedCode?: string
    ctCode?: string
    ctName?: string
    validityStartDate?: string | null
    validityEndDate?: string | null
    manageOrg?: string
    responsiblePersonName?: string
    responsiblePhone?: string
    oppositeName?: string
    oppositePerson?: string
    oppositePhone?: string
    oppositeBank?: string
    oppositeAccount?: string
    totalAmt?: number
    contractRemark?: string
    attachmentNames?: string
    appyerName?: string
    applyTime?: string
    att?: string
    attName?: string
  }

  interface OrgItem {
    orgId: string
    orgName: string
  }

  const props = defineProps({
    id: {
      type: String,
      default: '',
    },
  })

  const draftType = ref('')

  let contractInfo = ref<ContractInfo>({})
  //合同上传
  let contractFile1 = ref<{ value: string; label: string }[]>([])
  let contractFile = ref<{ value: string; label: string }[]>([])
  //科室
  let orgList = ref<OrgItem[]>([])

  onMounted(() => {
    queryCmsContract({ id: props.id }).then((res1: any) => {
      //       draftType.value = res1.data[0].draftType
      console.log('res1', res1.data[0])

      contractInfo.value = res1.data[0]

      const atts1 = res1.data[0].att.split(',')
      const attNames1 = res1.data[0].attName.split(',')
      // 遍历数组，将数据添加到contractFile中
      atts1.forEach((att, index) => {
        let attItem = {
          value: att,
          label: attNames1[index],
        }

        contractFile1.value.push(attItem)
        console.log('contractFile1', contractFile1.value)
      })
    })
    // 查询管理科室名称
    queryOrg({ activeFlag: '1' }).then((res: any) => {
      orgList.value = res.data
      console.log('orgList', orgList.value)
    })
  })
  // watch(
  //   () => props.id,
  //   newVal => {
  //     window.$message.info('流程实例ID变更为:' + newVal)
  //     contractFile1.value = []
  //     contractFile.value = []
  //     queryCmsContract({ id: newVal }).then((res1: any) => {
  //       draftType.value = res1.data[0].draftType
  //       // 将att和attName拆分成数组
  //       const atts1 = res1.data[0].att.split(',')
  //       const attNames1 = res1.data[0].attName.split(',')
  //       // 遍历数组，将数据添加到contractFile中
  //       atts1.forEach((att, index) => {
  //         let attItem = {
  //           value: att,
  //           label: attNames1[index],
  //         }
  //         console.log('attItem', attItem)
  //         contractFile1.value.push(attItem)
  //       })
  //       console.log('contractFile1', contractFile1.value)
  //       queryCmsContractDetail({ applyId: newVal }).then((res2: any) => {
  //         // 循环处理数据
  //         res2.data.forEach(item => {
  //           // 将att和attName拆分成数组
  //           const atts2 = item.att.split(',')
  //           const attNames2 = item.attName.split(',')
  //           // 遍历数组，将数据添加到contractFile中
  //           atts2.forEach((att, index) => {
  //             let attItem2 = {
  //               value: att,
  //               label: attNames2[index],
  //             }
  //             console.log('attItem2', attItem2)
  //             contractFile.value.push(attItem2.value)
  //           })
  //         })
  //         console.log('contractFile', contractFile.value)
  //       })
  //     })
  //   },
  //   { immediate: true }
  // )

  // 预览已上传文件
  let showPreview2 = ref(false)
  let previewOssPath3 = ref('')
  let previewOssPathName3 = ref('')

  //OSS地址预览
  let showPreview1 = ref(false)
  //OSS路径
  let previewOssPath1 = ref('')
  //Oss文件名称
  let previewOssPathName1 = ref('')
  const showSignContractFile = (item: any) => {
    if (item.value.includes('originalDraftOfContract/')) {
      showPreview2.value = true
      previewOssPath3.value = item.value
      previewOssPathName3.value = item.label
    } else {
      showPreview1.value = true
      previewOssPath1.value = item.value
      previewOssPathName1.value = item.label
    }
  }

  const emits = defineEmits(['close'])

  const handleClose = () => {
    emits('close')
  }
</script>
<style scoped>
  .title-line {
    height: 2.013rem;

    width: 100% !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    font-weight: normal !important;
  }

  .title-line-left {
    height: 2.013rem;
    padding-left: 0.5rem !important;
    border-left: 0.3em solid var(--j-frame-color) !important;
  }
  .mobile-layout {
    flex-direction: column;
  }
</style>
